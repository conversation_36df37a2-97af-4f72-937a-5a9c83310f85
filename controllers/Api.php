<?php

/**
 * @file Api.php
 * @brief 三方提供内网APi
 * <AUTHOR>
 * @version 1.0
 * @date 2021-05-21
 */
class Controller_Api extends Ap_Controller_Abstract
{

    public $actions = [
        'crawl' => 'actions/api/v1/Crawl.php', //抓取数据上报
        'crawlv2' => 'actions/api/v2/Crawl.php', //抓取数据上报-原始数据
        'crawlhistory' => 'actions/api/v1/CrawlHistory.php', //历史抓取数据上报
        //线下接口
        'offlinequery' => 'actions/api/v1/OfflineQuery.php', //提供线下查询接口
        'offlineupdate' => 'actions/api/v1/OfflineUpdate.php', //提供线下修改接口
        //无答案题干推送
        'tikubookstem' => 'actions/api/v1/tiku/BookStem.php', //题库书籍题干推送-即将下线

        'getisbnscaninfo' => 'actions/api/v1/GetIsbnScanInfo.php', // 高优图书判断接口(扫录系统)
        //
        'addproduct' => 'actions/api/v1/AddProduct.php', //生产流程
        'purchasecrawl' => 'actions/api/v1/PurchaseCrawl.php', //网采抓取回调

        'isbnresultevaluatelist' => 'actions/api/v1/ISBNResultEvaluateList.php',  //图书准召评估列表

        'imageprocess' => 'actions/api/v1/ImageProcess.php', // 快对图片策略

        'receiveimageprocesscallback' => 'actions/api/v1/ReceiveImageProcessCallback.php', // 异步服务回调接口，更新任务图片数据

        'productplat' => 'actions/api/v1/tiku/ProductPlat.php', //作业帮生产平台的接口--即将下线
        'syncbookidrel' => 'actions/api/v1/tiku/SyncBookIdRel.php', //题库扫录Id与bookId对应关系同步

        'runcommand' => 'actions/api/v1/RunCommand.php', // 执行命令行

        // 抓取升级
        'crawlv3'   => 'actions/api/v2/CrawlV3.php', //抓取数据上报-原始数据
        'crawllist' => 'actions/api/v2/CrawlList.php',

        'addtikuanswerslocs' => 'actions/api/v1/tiku/AnswersLocs.php', // 接收生产完书籍的答案坐标信息
        // 有答案无答案题库图书推送
        'tikubook'  => 'actions/api/v1/tiku/TikuBook.php',
        // 反馈后台导出查询接口
        'featurereportexportlist' => 'actions/api/v1/FeatureReportExportList.php',
    ];
}

?>
