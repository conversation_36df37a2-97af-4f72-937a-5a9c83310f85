<?php
/***************************************************************************
 *
 * Copyright (c) 2021 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : Report.php
 * Author: <EMAIL>
 * Date: 2021/5/27
 * Time: 15:38
 */

class Ablib_Service_Report{
    //appId
    private $appId = null;
    //cuId
    private $cuId  = null;
    //userid
    private $userid = null;

    //reportUrl
    private $pathInfo = null;
    //trackUrl
    private $trackUrl = null;
    //service
    private $services = null;

    public function __construct($appId,$cuId,$userid = 0){
        $this->appId  = strval($appId);
        $this->cuId   = strval($cuId);
        $this->userid = intval($userid);

        if($appId == Ablib_Const_Const::OverSeaAppKey){
            $this->pathInfo = Ablib_Const_Const::OverSeaReportUrl;
            $this->trackUrl = Ablib_Const_Const::OverSeaReportCallBackUrl;
            $this->services = Ablib_Const_Const::OverSeaService;
        }else{
            $this->pathInfo = Ablib_Const_Const::ReportUrl;
            $this->trackUrl = Ablib_Const_Const::ReportCallBackUrl;
            $this->services = Ablib_Const_Const::Service;
        }
    }
    /**
     * @param $cuid    string
     * @param $params  json      [{"k":"course","v":"语文"},{"k":"course","v":"英语"},{"k":"grade","v":"15"}]
    */
    public function Report($keyName,$params){
        //参数校验
        if(empty($this->appId)){
            Bd_Log::warning("Error:[Param Error] Detail:[appId:$this->appId]");
            return false;
        }

        $arrHeader = [
            'pathinfo'      => $this->pathInfo,
        ];
        $arrParams = array(
            'appId'       => $this->appId,
            'cuid'        => $this->cuId,
            'userid'      => $this->userid,
            'params'      => is_array(json_decode($params,true)) ? $params : '[]',
            'featureKey'  => $keyName,
        );
        $res = Ablib_Util_ServiceTools::ralRequest($this->services, 'POST',$arrHeader, $arrParams);
        //如果返回错误则是ral网络错误
        if(false === $res){
            Bd_Log::warning("Error:[Ral Service Error] Detail:[service:$this->services arrParams:".json_encode($arrParams)." arrHeader:".json_encode($arrHeader)."]");
            return false;
        }
        //接口errNo
        $errNo  = intval($res['errNo']); 
        $errMsg = strval($res['errMsg']); 
        $data   = $res['data'];
        if($errNo > 0){
            Bd_Log::warning("Error:[Ral Errno Error] Detail:[errNo:$errNo errMsg:$errMsg data:".json_encode($data)."]");
            return false;
        }
        $data = !empty($data['ab']) ? $data['ab'][0] : [];

        if(!empty($data)){
            //上报
            $res = $this->callBackReport($keyName,$data);
            if(false === $res){
                Bd_Log::warning("Error:[callBackReport Error] Detail:[keyName:$keyName data:".json_encode($data)."]");
                //目前不做返回错误处理,仅打日志标记
            }
        }
        return $data;
    }

    /*
     * @synopsis  Report 根据选取到对应的值然后上报
     *
     * @param $keyName    string
     * @param $data array      
     *
     * @returns
     */
    public function callBackReport($keyName,$abResult){
        //参数校验
        if(empty($keyName) || empty($abResult)){
            Bd_Log::warning("Error:[Param Error] Detail:[keyName:$keyName abResult:".json_encode($abResult)."]");
            return false;
        }
        $arrHeader = [
            'pathinfo'      => $this->trackUrl,
        ];
        $arrParams = array(
            'appId'        => $this->appId,
            'cuid'         => $this->cuId,
            'userid'       => $this->userid,
            'key'          => $keyName,
            'type'         => intval($abResult['type']),
            'versionId'    => intval($abResult['versionId']),
            'experimentId' => intval($abResult['experimentId']),
            'correctVersionId' => intval($abResult['correctVersionId']),
            'value'        => strval($abResult['value']),
            'sdk'         => 'odp',
        );
        $res = Ablib_Util_ServiceTools::ralRequest($this->services, 'POST',$arrHeader, $arrParams);
        //如果返回错误则是ral网络错误
        if(false === $res){
            Bd_Log::warning("Error:[Ral Service Error] Detail:[service:$this->services arrParams:".json_encode($arrParams)." arrHeader:".json_encode($arrHeader)."]");
            return false;
        }
        //接口errNo
        $errNo  = intval($res['errNo']);
        $errMsg = strval($res['errMsg']);
        $data   = $res['data'];
        if($errNo > 0){
            Bd_Log::warning("Error:[Ral Errno Error] Detail:[errNo:$errNo errMsg:$errMsg data:".json_encode($data)."]");
            return false;
        }
        //上报成功，打点记录
        $noticeKey = $this->appId . '_' . $this->cuId . '_' . implode('_' , $abResult);
        Bd_Log::addNotice($noticeKey,1);
        return true;
    }

    /**
     * @param $keys    array
     * @param $params  json      [{"k":"course","v":"语文"},{"k":"course","v":"英语"},{"k":"grade","v":"15"}]
     */
    public function MultiReport($keys,$params, $trackFlag = false){
        //参数校验
        if(empty($this->appId)){
            Bd_Log::warning("Error:[Param Error] Detail:[appId:$this->appId]");
            return false;
        }
        if (!is_array($keys) || empty($keys)) {
            Bd_Log::warning("Error:[FeatureKeys Error] Detail:[FeatureKeys:$keys]");
            return false;
        }
        if (count($keys) > 100) {
            Bd_Log::warning("Error:[FeatureKeys over 100]");
            return  false;
        }

        $arrHeader = [
            'pathinfo'      => $this->pathInfo,
        ];
        $arrParams = array(
            'appId'       => $this->appId,
            'cuid'        => $this->cuId,
            'userid'      => $this->userid,
            'params'      => is_array(json_decode($params,true)) ? $params : '[]',
            'featureKeys'  => json_encode($keys),
        );
        $res = Ablib_Util_ServiceTools::ralRequest($this->services, 'POST',$arrHeader, $arrParams);
        //如果返回错误则是ral网络错误
        if(false === $res){
            Bd_Log::warning("Error:[Ral Service Error] Detail:[service:$this->services arrParams:".json_encode($arrParams)." arrHeader:".json_encode($arrHeader)."]");
            return false;
        }
        //接口errNo
        $errNo  = intval($res['errNo']);
        $errMsg = strval($res['errMsg']);
        $data   = $res['data'];
        if($errNo > 0){
            Bd_Log::warning("Error:[Ral Errno Error] Detail:[errNo:$errNo errMsg:$errMsg data:".json_encode($data)."]");
            return false;
        }
        $data = !empty($data['ab']) ? $data['ab'] : [];

        if (!empty($data) && $trackFlag) {

            $arrHeader = [
                'pathinfo' => $this->trackUrl,
            ];

            $reqArray = [];

            foreach ($data as $v) {
            $arrParams = array(
                'appId'        => $this->appId,
                'cuid'         => $this->cuId,
                'userid'       => $this->userid,
                'key'          => $v['key'],
                'type'         => intval($v['type']),
                'versionId'    => intval($v['versionId']),
                'experimentId' => intval($v['experimentId']),
                'correctVersionId' => intval($v['correctVersionId']),
                'value'        => strval($v['value']),
                'sdk'         => 'odp',
            );

            $reqArray[$v['key']]    =  array($this->services, "post", $arrParams, mt_rand(), $arrHeader);
            }

            Ablib_Util_ServiceTools::ralMulti($reqArray);

        }
        return $data;
    }
}
