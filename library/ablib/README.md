### 一：模块接入
#### 1:容器化
###### 1.1加载模块
 > git submodule add -<NAME_EMAIL>:znzt/ablib.git  library/ablib

###### 1.2修改配置
> 打开.gitmodules文件：需替换url 由(********************)为(../../)形式  或执行 （:%s/********************:/..\/..\//g）

###### 1.3提交代码至git仓库
> git add/commit/push

#### 2:集群
###### 2.1 获取代码仓库
> ********************:znzt/ablib.git 

###### 2.2 找op需将代码上线至对应集群

### 二：配置接入
#### 1:容器化
abdocker.conf,各业务在自我odin里配置
###### 1.1 同一业务线
```
domain: abengine-svc:8080
connectTimeOut: 10
timeout: 100
protocol: http
converter: form
retry: 0
proxy: 1
```
###### 1.2 不同业务线
```
domain: abengine-svc.app-service:8080
connectTimeOut: 10
timeout: 100
protocol: http
converter: form
retry: 0
proxy: 0
```
#### 2:集群
abdocker.conf
```

[CamelConfig]
[.ServiceConfig]
 
[..Local]
 
[...@Service]
Name : abdocker
DefaultPort :   80
DefaultRetry :  2
DefaultConnectType :    SHORT
DefaultConnectTimeOut : 300
DefaultReadTimeOut :    5000
DefaultWriteTimeOut :   500
[....@Server]
Hostname : abengine.znzt-growth.zuoyebang.dd
[....Protocol]
Name :  http
[....Converter]
Name :  form
```
**注:在op添加该配置时需说明传输数据格式为form**

#### 3:ship环境
```
domain:abengine-svc.znzt-growth:8080
connectTimeOut: 10
timeout: 100
protocol: http
converter: form
retry: 0
proxy: 0
```
#### 4:docker环境
```
[CamelConfig]
[.ServiceConfig]
 
[..Local]
 
[...@Service]
Name : abdocker
DefaultPort :   80
DefaultRetry :  2
DefaultConnectType :    SHORT
DefaultConnectTimeOut : 300
DefaultReadTimeOut :    5000
DefaultWriteTimeOut :   500
[....@Server]
Hostname : abtest-base-e.suanshubang.cc
[....Protocol]
Name :  http
[....Converter]
Name :  form
```

配置路径:/home/<USER>/conf/ral/services/abdocker.conf

### 二：代码接入
#### 1:sdk路径
> /home/<USER>/php/phplib/ablib/interface/Sdk.php

#### 2:调用demo
```
    /*
     * @synopsis  Report 获取策略结果
     *
     * @param $keyName string
     * @param $appId   string
     * @param $cuid    string   
     * @param $userid  int
     * @param $params  json      [{"k":"course","v":"语文"},{"k":"course","v":"英语"},{"k":"grade","v":"15"}]
     *
     * @returns   false/array
     *     [
     *         "key"          : $keyName,   //key名称
     *         "type"         : 1,          //1:string 2:bool 4:float64 5:json
     *         "versionId"    : 20,         //版本ID
     *         "value"        : "a",        //命中版本值
     *         "experimentId" : 9,          //实验Id
     *     ]
     */
//cuid调用
$data = Ablib_Interface_Sdk::Report($keyName,$appId,$cuid,$params);
if(false !== $data && !empty($data)){
    $value = $data['value'];
    switch($value){
        case 'a':
            //a逻辑
            break;
        case 'b':
            //b逻辑
            break;
        default:
    }
}
//userid调用
$data = Ablib_Interface_Sdk::ReportUserid($keyName,$appId,$userid,$params);
if(false !== $data && !empty($data)){
    $value = $data['value'];
    switch($value){
        case 'a':
            //a逻辑
            break;
        case 'b':
            //b逻辑
            break;
        default:
    }
}
```

参考wiki:https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=265340227