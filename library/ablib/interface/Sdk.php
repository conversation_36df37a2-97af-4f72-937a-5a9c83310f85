<?php
/***************************************************************************
 *
 * Copyright (c) 2021 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : Sdk.php
 * Author: <EMAIL>
 * Date: 2021/5/27
 * Time: 15:38
 * Desc: ab侧sdk接口
 */
class Ablib_Interface_Sdk{
    /*
     * @synopsis  Report 获取策略结果
     *
     * @param $keyName string
     * @param $appId   string
     * @param $cuid    string
     * @param $params  json      [{"k":"course","v":"语文"},{"k":"course","v":"英语"},{"k":"grade","v":"15"}]
     * http://yapi.zuoyebang.cc/project/5500/interface/api/174152
     *
     * @returns
     */
 
    public static function Report($keyName,$appId,$cuid,$params){
        //初始化类
        $objReport = new Ablib_Service_Report($appId,$cuid,0);
        return $objReport->Report($keyName,$params);
    }
    /*
     * @synopsis  Report 获取策略结果
     *
     * @param $keyName string
     * @param $appId   string
     * @param $userid  int
     * @param $params  json      [{"k":"course","v":"语文"},{"k":"course","v":"英语"},{"k":"grade","v":"15"}]
     * http://yapi.zuoyebang.cc/project/5500/interface/api/174152
     *
     * @returns
     */

    public static function ReportUserid($keyName,$appId,$userid,$params){
        //初始化类
        $objReport = new Ablib_Service_Report($appId,'',$userid);
        return $objReport->Report($keyName,$params);
    }
    /*
     * @synopsis  Report 获取策略结果
     *
     * @param $keyName string
     * @param $appId   string
     * @param $cuid    string 
     * @param $userid  int
     * @param $params  json      [{"k":"course","v":"语文"},{"k":"course","v":"英语"},{"k":"grade","v":"15"}]
     * http://yapi.zuoyebang.cc/project/5500/interface/api/174152
     *
     * @returns
     */

    public static function ReportCuidUserid($keyName,$appId,$cuid,$userid,$params){
        //初始化类
        $objReport = new Ablib_Service_Report($appId,$cuid,$userid);
        return $objReport->Report($keyName,$params);
    }


    /*
        * @synopsis  MultiReport 批量获取策略结果
        *
        * @param $keys    array     array("foo", "bar")
        * @param $appId   string
        * @param $cuid    string
        * @param $userid  int
        * @param $params  json      [{"k":"course","v":"语文"},{"k":"course","v":"英语"},{"k":"grade","v":"15"}]
        * http://yapi.zuoyebang.cc/project/5500/interface/api/174152
        *
        * @returns
        */
    public static function MultiReport($keys, $appId, $cuid, $userid, $params, $multiTrackFlag = false){
        //初始化类
        $objReport = new Ablib_Service_Report($appId,$cuid,$userid);
        return $objReport->MultiReport($keys,$params, $multiTrackFlag);
    }


    /*
      * @synopsis  Track MultiReport批量获取结果后打点使用,将命中的结果上报
      *            非批量获取无需上报！！！
      *
      * @param $keyName string
      * @param $appId   string
      * @param $cuid    string
      * @param $userid  int
      * @param $abResult  array      Multi命中结果集合的一个元素 array("type" => 1, "versionId" => 2, "experimentId" => 3, value => "test" )
      * http://yapi.zuoyebang.cc/project/5500/interface/api/174152
      *
      * @returns
      */
    public static function Track($keyName,$appId,$cuid,$userid,$abResult) {
        //初始化类
        $objReport = new Ablib_Service_Report($appId,$cuid,$userid);
        return $objReport->callBackReport($keyName, $abResult);
    }
}
