<?php


class Zb_Service_Das_StudentLessonRealTime
{
    private static $service = 'zbcore_das';
    private static $serviceUri = '/zbcore/api/api';
    private static $module = 'das';
    private static $stuLessonEntity = 'studentLessonRealTime';

    /**
     * 学生章节详情接口
     *（学生章节维度数据）
     *
     * 单章节 + 多学生
     * 多章节 + 单学生
     *
     * @param array $studentLessonIds 学生课程ID映射数组，array([studentUid]_[courseId]_[lessonId], ...)。eg. ["2000098000_231715_229368", ...]
     * @param array $studentLessonFields 接口返回的所需字段
     * @param array $options 扩展数据
     * @return array
     */
    public static function getKVByLessonIdRealTime($studentLessonIds, $studentLessonFields, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuLessonEntity, 'getKVByLessonIdRealTime');
        $arrParams = array(
            'studentLessonIds'      => $studentLessonIds,
            'studentLessonFields'   => $studentLessonFields,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
