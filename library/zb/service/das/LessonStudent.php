<?php


class Zb_Service_Das_LessonStudent {

    private static $service         = 'zbcore_das';
    private static $serviceUri      = '/zbcore/api/api';
    private static $module          = 'das';
    private static $lessonStuEntity = 'lessonStudent';


    /**
     * 章节学生详情接口
     * 单章节 + 多学生
     * @param array     $lessonStudentIds      学生课程ID映射数组，array(studentUid => lessonId, ...)。eg. [2183318929=>89864, ...]
     * @param array     $lessonStudentFields    接口返回的所需字段
     * @param array     $options                扩展数据
     * @return array
     */
    public static function getKVByLessonId($lessonStudentIds, $lessonStudentFields, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$lessonStuEntity, 'getKVByLessonId');
        $arrParams = array(
            'lessonStudentIds'      => $lessonStudentIds,
            'lessonStudentFields'   => $lessonStudentFields,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
