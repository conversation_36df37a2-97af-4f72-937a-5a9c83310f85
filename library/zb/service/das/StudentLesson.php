<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   StudentLesson.php
 * <AUTHOR>
 * @date   2018/7/2 下午10:06
 * @brief
 *
 **/


class Zb_Service_Das_StudentLesson {

    private static $service     = 'zbcore_das';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'das';
    private static $stuLessonEntity      = 'studentLesson';


    /**
     * 学生章节详情接口
     *（学生章节维度数据）
     *
     * 单章节 + 多学生
     * 多章节 + 单学生
     *
     * aram array        $studentLessonIds      学生课程ID映射数组，array([studentUid]_[lessonId], ...)。eg. ["2183318929_89864", ...]
     * @param array     $studentLessonFields    接口返回的所需字段
     * @param array     $options                扩展数据
     * @return array
     */
    public static function getKVByLessonId($studentLessonIds, $studentLessonFields, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuLessonEntity, 'getKVByLessonId');
        $arrParams = array(
            'studentLessonIds'      => $studentLessonIds,
            'studentLessonFields'   => $studentLessonFields,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单用户已结束章节列表接口
     *
     *
     *  按章节开始时间倒序
     * 支持分页,分页limit 10条
     *
     * @param int   $studentUid     学生UID
     * @param array $courseIds      批量课程ID(可选)
     * @param int   $offset         偏移
     * @param int   $limit          分页大小，最大支持单页100
     * @param array $options        扩展数据
     * @return array|bool
     */
    public static function getListForFinished($studentUid, $courseIds = array(), $offset = 0, $limit = 10, $options = array(),$sources = [4, 7, 111, 112, 113, 114, 115, 119]) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuLessonEntity, 'getListForFinished');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseIds'         => $courseIds,
            'offset'            => $offset,
            'limit'             => $limit,
            'sources'           => $sources,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单用户未结束章节列表接口
     *
     *
     *  按章节开始时间正序
     * 支持分页,分页limit 10条
     *
     * @param int   $studentUid     学生UID
     * @param array $courseIds      批量课程ID(可选)
     * @param int   $offset         偏移
     * @param int   $limit          分页大小，最大支持单页100
     * @param array $options        扩展数据
     * @return array|bool
     */
    public static function getListForUnFinished($studentUid, $courseIds = array(), $offset = 0, $limit = 10, $options = array(),$sources = [4, 7, 111, 112, 113, 114, 115, 119]) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuLessonEntity, 'getListForUnFinished');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseIds'         => $courseIds,
            'offset'            => $offset,
            'limit'             => $limit,
            'sources'           => $sources
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 学生章节列表【单课程 + 多学生】
     * @param       $studentCourseIds 学生课程ID映射数组，array([studentUid]_[courseId], ...)。eg. ["2183318929_89864", ...]
     * @param       $studentLessonFields 接口返回的所需学生章节字段
     * @param array $options  扩展数据
     * @return array
     */
    public static function getListByCourseId($studentCourseIds, $studentLessonFields,$options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuLessonEntity, 'getListByCourseId');
        $arrParams = array(
            'studentCourseIds'    => $studentCourseIds,
            'studentLessonFields'  => $studentLessonFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * @param $studentUid
     * @param $studentLessonFields
     * @param int $offset
     * @param int $limit max 100
     * @param array $timeRange [ 0 => start_time, 1 => stop_time]
     * @param array $options
     * @return array
     */
    public static function getSLListByStudentUid($studentUid, $studentLessonFields, $offset=  0, $limit = 10, $timeRange = [], $arrBrand = [], $options = [], $sources = []) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuLessonEntity, 'getSLListByStudentUid');
        $arrParams = array(
            'studentUid'     => $studentUid,
            'arrFields'      => $studentLessonFields,
            'offset'         => $offset,
            'limit'          => $limit,
            'timeRange'      => $timeRange,
            'arrBrandId'     => $arrBrand,
            'sources'        => $sources
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

}
