<?php


class Zb_Service_Das_StudentCourseRealTime
{
    private static $service = 'zbcore_das';
    private static $serviceUri = '/zbcore/api/api';
    private static $module = 'das';
    private static $stuCourseEntity = 'studentCourseRealTime';

    /**
     * 学生课程详情接口
     *（学生课程维度数据）
     *
     * 单课程 + 多学生
     *
     * @param array     $studentCourseIds      学生课程ID映射数组，array([studentUid]_[courseId], ...)。eg. ["2183318929_89864", ...]
     * @param array     $studentCourseFields    接口返回的所需字段
     * @param array     $options                扩展数据
     * @return array
     */
    public static function getKVByCourseIdRealTime($studentCourseIds, $studentCourseFields, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getKVByCourseIdRealTime');
        $arrParams = array(
            'studentCourseIds'    => $studentCourseIds,
            'studentCourseFields' => $studentCourseFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据多查询条件获取学生课程信息接口
     *
     * 支持分页,最大支持单页40
     *
     * @param int  $studentUid              学生UID
     * @param array $newCourseType          默认[] 获取所有课程类型
     * @param array $subject                默认[] 获取所有学科
     * @param array $grade                  默认[] 获取所有年级
     * @param array $source                 默认[] 获取所有业务线
     * @param int $queryType                完结状态
     * @param int $sortType                 排序类型
     * @param int $queryTime                获取课程信息的查询时间
     * @param int $offset                   分页偏移
     * @param int   $limit                  分页大小，最大支持单页40
     * @param array $options                扩展字段
     * @return array
     */
    public static function getStuCourseByCondsRealTime($studentUid, $newCourseType = [],  $subject= [], $grade = [], $queryType = -1, $queryTime = -1, $sortType = 0, $source = [], $offset = 0, $limit = 20, $options = array())
    {
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getStuCourseByCondsRealTime');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'newCourseType'     => $newCourseType,
            'subject'           => $subject,
            'grade'             => $grade,
            'source'            => $source,
            'queryType'         => $queryType,
            'sortType'          => $sortType,
            'queryTime'         => $queryTime,
            'offset'            => $offset,
            'limit'             => $limit,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
