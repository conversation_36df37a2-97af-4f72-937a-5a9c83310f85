<?php
/**
 * Created by PhpStorm.
 * User: wangfeng<PERSON>@zuoyebang.com
 * Date: 2018/8/20
 * Time: 下午12:06
 */

class Zb_Service_Das_CourseStudent
{
    private static $service     = 'zbcore_das';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'das';
    private static $stuCourseEntity      = 'courseStudent';

    public static function getStuListByCourseId($courseId, $courseStudentFields, $offset = 0, $limit = 10, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getStuListByCourseId');
        $arrParams = array(
            'courseId' => $courseId,
            'courseStudentFields' => $courseStudentFields,
            'offset' => $offset,
            'limit' => $limit,
            );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 获取课程用户表数据
     * @param array $aCourseStudentId
     * @param array $aCourseStudentFields
     * @param array $options
     * @return array
     */
    public static function getKVByCourseId($aCourseStudentId,$aCourseStudentFields,$options = array()) {
        $aHeader    = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getKVByCourseId');
        $aParam     = array(
            'courseStudentIds'      => $aCourseStudentId,
            'courseStudentFields'   => $aCourseStudentFields
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $aParam, $aHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $aParam, $aHeader);
    }

    /**
     * 无total拉取courseStu数据
     * 
     */
    public static function getStuListByCourseIdTmp($courseId, $courseStudentFields, $offset = 0, $limit = 10, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getStuListByCourseIdTmp');
        $arrParams = array(
            'courseId' => $courseId,
            'courseStudentFields' => $courseStudentFields,
            'offset' => $offset,
            'limit' => $limit,
            );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 指定课程total 获取，仅有效学生
     */
    public static function getStuListCntByCourseId($courseId, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getStuListCntByCourseId');
        $arrParams = array(
            'courseId' => $courseId,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}