<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   StudentCourse.php
 * <AUTHOR>
 * @date   2018/7/2 下午10:06
 * @brief
 *
 **/


class Zb_Service_Das_StudentCourse {

    private static $service     = 'zbcore_das';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'das';
    private static $stuCourseEntity      = 'studentCourse';


    /**
     * 学生课程详情接口
     *（学生课程维度数据）
     *
     * 单课程 + 多学生
     *
     * @param array     $studentCourseIds      学生课程ID映射数组，array([studentUid]_[courseId], ...)。eg. ["2183318929_89864", ...]
     * @param array     $studentCourseFields    接口返回的所需字段
     * @param array     $options                扩展数据
     * @return array
     */
    public static function getKVByCourseId($studentCourseIds, $studentCourseFields, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getKVByCourseId');
        $arrParams = array(
            'studentCourseIds'    => $studentCourseIds,
            'studentCourseFields'  => $studentCourseFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 学生课程产品详情接口
     *（学生课程列表维度数据）
     *
     *
     * @param array     $studentCpuIds      学生课程ID映射数组，array([studentUid]_[cpuId], ...)。eg. ["2183318929_89864", ...]
     * @param array     $studentCourseFields    接口返回的所需字段
     * @param array     $options                扩展数据
     * @return array
     */
    public static function getSpuListByKV($studentCpuIds, $studentCourseFields, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getSpuListByKV');
        $arrParams = array(
            'studentCpuIds'    => $studentCpuIds,
            'studentCourseFields'  => $studentCourseFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单学生获取购课记录全量列表接口
     *
     * （包含全部课程类型；包含正常课、已退课程）
     *  按报名时间倒序
     *  支持分页,分页limit 100条
     *
     * @param int    $studentUid        查询指定用户UID
     * @param int    $offset            分页偏移
     * @param int   $limit              分页大小，最大支持单页100
     * @param array  $options           扩展数据
     * @return array
     */
    public static function getListForFull($studentUid, $offset = 0, $limit = 100, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForFull');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'offset'            => $offset,
            'limit'             => $limit,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单学生获取购课记录列表接口
     *
     * （仅包含班课与体验课）
     *  按报名时间倒序
     *  支持分页,分页limit 100条
     *
     * @param int    $studentUid        查询指定用户UID
     * @param int    $offset            分页偏移
     * @param int   $limit              分页大小，最大支持单页100
     * @param array  $options           扩展数据
     * @return array
     */
    public static function getListForPurchased($studentUid, $offset = 0, $limit = 100, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForPurchased');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'offset'            => $offset,
            'limit'             => $limit,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单用户已上课程列表接口(必须指定课程类型)
     *
     * （默认包含专题课、公开课、班课、家长课、体验课；可通过courseType指定具体课程类型实现过滤）
     *  按最近章节完成时间倒序
     * 支持分页,最大支持单页100
     *
     * @param int   $studentUid     学生UID
     * @param int   $courseType     默认-1 获取所有课程类型
     * @param int   $courseSubject  默认-1 获取所有课程学科
     * @param int   $offset         分页偏移
     * @param int   $limit          分页大小，最大支持单页100
     * @param array $options        扩展字段
     * @return array
     */
    public static function getListForAttended($studentUid, $courseType = -1, $courseSubject = -1, $offset = 0, $limit = 10, $options = array(), $sources = []) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForAttended');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseType'        => $courseType,
            'courseSubject'    => $courseSubject,
            'offset'            => $offset,
            'limit'             => $limit,
            'sources'            => $sources
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *   根据多查询条件获取学生课程信息接口
     *
     *  支持分页,最大支持单页40
     *
     * @param int  $studentUid              学生UID
     * @param array $newCourseType          默认[] 获取所有课程类型
     * @param array $subject                默认[] 获取所有学科
     * @param array $grade                  默认[] 获取所有年级
     * @param array $source                 默认[] 获取所有业务线
     * @param int $queryType                完结状态
     * @param int $sortType                 排序类型
     * @param int $queryTime                获取课程信息的查询时间
     * @param int $offset                   分页偏移
     * @param int   $limit                  分页大小，最大支持单页40
     * @param array $options                扩展字段
     * @return array
     */
    public static function getStuCourseByConds($studentUid, $newCourseType = [],  $subject= [], $grade = [], $queryType = -1, $queryTime = -1, $sortType = 0, $source = [], $offset = 0, $limit = 20, $options = array())
    {
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getStuCourseByConds');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'newCourseType'     => $newCourseType,
            'subject'           => $subject,
            'grade'             => $grade,
            'source'            => $source,
            'queryType'         => $queryType,
            'sortType'          => $sortType,
            'queryTime'         => $queryTime,
            'offset'            => $offset,
            'limit'             => $limit,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单用户已完成课程列表接口(必须指定课程类型)
     *
     * （默认包含专题课、公开课、班课、家长课、体验课；可通过courseType指定具体课程类型实现过滤）
     *  按最近章节完成时间倒序
     * 支持分页,最大支持单页100
     *
     * @param int   $studentUid     学生UID
     * @param int   $courseType     默认-1 获取所有课程类型
     * @param int   $courseSubject  默认-1 获取所有课程学科
     * @param int   $offset         分页偏移
     * @param int   $limit          分页大小，最大支持单页100
     * @param array $options        扩展字段
     * @return array
     */
    public static function getListForFinished($studentUid, $courseType = -1, $courseSubject = -1, $offset = 0, $limit = 10, $options = array(), $sources = []) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForFinished');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseType'        => $courseType,
            'courseSubject'     => $courseSubject,
            'offset'            => $offset,
            'limit'             => $limit,
            'sources'           => $sources
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单用户未完成课程列表接口
     *
     * （包含专题课、公开课、班课、家长课、体验课）
     *  按最近上课时间升序
     * 支持分页, 最大支持单页100
     *
     * @param int   $studentUid     学生UID
     * @param int   $offset         分页偏移
     * @param int   $limit          分页大小，最大支持单页100
     * @param array $options        扩展字段
     * @return array
     */
    public static function getListForUnfinished($studentUid, $courseType = -1, $courseSubject = -1, $offset = 0, $limit = 10, $options = array(), $sources = []) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForUnfinished');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseType'        => $courseType,
            'courseSubject'     => $courseSubject,
            'offset'            => $offset,
            'limit'             => $limit,
            'sources'           => $sources
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单用户未开始课程列表接口
     *
     * （包含专题课、公开课、班课、家长课、体验课）
     *  按最近上课时间升序
     * 支持分页, 最大支持单页100
     *
     * @param int   $studentUid     学生UID
     * @param int   $offset         分页偏移
     * @param int   $limit          分页大小，最大支持单页100
     * @param array $options        扩展字段
     * @return array
     */
    public static function getListForUnStart($studentUid, $courseType = -1, $courseSubject = -1, $offset = 0, $limit = 10, $options = array(), $sources = []) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForUnStart');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseType'        => $courseType,
            'courseSubject'    => $courseSubject,
            'sources'           => $sources,
            'offset'            => $offset,
            'limit'             => $limit,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单用户进行中课程列表接口
     *
     * （包含专题课、公开课、班课、家长课、体验课）
     *  按最近上课时间升序
     * 支持分页, 最大支持单页100
     *
     * @param int   $studentUid     学生UID
     * @param int   $offset         分页偏移
     * @param int   $limit          分页大小，最大支持单页100
     * @param array $options        扩展字段
     * @return array
     */
    public static function getListForStart($studentUid, $courseType = -1, $courseSubject = -1, $offset = 0, $limit = 10, $options = array(), $sources = []) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForStart');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseType'        => $courseType,
            'courseSubject'    => $courseSubject,
            'sources'           => $sources,
            'offset'            => $offset,
            'limit'             => $limit,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单用户已报名课程列表接口(必须指定课程类型)
     *
     * （包含专题课、公开课、班课、家长课、体验课）
     *  按报名时间倒序
     * 支持分页,分页limit 10条
     *
     * @param int   $studentUid         学生UID
     * @param int   $courseType         课程类型 默认-1 获取全部课程类型
     * @param int   $offset             分页偏移
     * @param int   $limit              分页大小，最大支持单页100
     * @param int   $order              排序顺序 报名时间 1 倒序 0 正序
     * @param array $options            扩展字段
     * @return array
     */
    public static function getListForRegistered($studentUid, $courseType = -1, $offset = 0, $limit = 10, $order = 1,  $deleteStatus = -1, $options = array(), $sources = []) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForRegistered');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseType'        => $courseType,
            'offset'            => $offset,
            'limit'             => $limit,
            'order'             => $order,
            'deleteStatus'      => $deleteStatus,
            'sources'           => $sources
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     *  获取用户的课程可选择的过滤条件列表（课程学科，课程类型）
     *
     * @param int   $studentUid         学生UID
     * @param array $options            扩展字段
     * @return array
     */
    public static function getListForFilter($studentUid, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForFilter');
        $arrParams = array(
            'studentUid'        => $studentUid,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  获取用户未完成、已完成课程数量
     *
     * @param int   $studentUid         学生UID
     * @param int $courseType
     * @param int $courseSubject
     * @param array $options            扩展字段
     * @return array
     */
    public static function getListForCount($studentUid, $courseType = -1, $courseSubject = -1, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForCount');
        $arrParams = array(
            'studentUid'    => $studentUid,
            'courseSubject' => $courseSubject,
            'courseType'    => $courseType,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     *  单学生获取购课记录班课列表接口
     *
     * （包含全部课程类型；包含正常课、已退课程）
     *  按报名时间倒序
     *  支持分页,分页limit 100条
     *
     * @param int    $studentUid        查询指定用户UID
     * @param array  $courseTypes       查询指定课程类型，支持多个
     * @param int    $startTime         开始时间
     * @param int    $endTime           结束时间
     * @param int    $courseGrade       查询指定课程年级
     * @param int    $offset            分页偏移
     * @param int    $limit             分页大小，最大支持单页100
     * @param array  $options           扩展数据
     * @return array
     */
    public static function getListForCourseType($studentUid, $courseTypes, $startTime=0, $endTime=0, $courseGrade=0, $offset = 0, $limit = 100, $options = array(), $sources = []) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForCourseType');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseTypes'       => $courseTypes,
            'courseGrade'       => $courseGrade,
            'startTime'         => $startTime,
            'endTime'           => $endTime,
            'offset'            => $offset,
            'limit'             => $limit,
            'sources'           => $sources,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     *  是否存在指定类型课程接口
     *
     * （包含全部课程类型；包含正常课、已退课程）
     *  按报名时间倒序
     *
     * @param int    $studentUid        查询指定用户UID
     * @param int    $courseType        查询指定课程类型
     * @param int    $startTime         开始时间
     * @param int    $endTime           结束时间
     * @param array  $options           扩展数据
     * @return array
     */
    public static function hasCourseByType($studentUid, $courseType, $startTime=0, $endTime=0, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'hasCourseByType');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseType'        => $courseType,
            'startTime'         => $startTime,
            'endTime'           => $endTime,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     *  单学生获取购课记录列表接口
     *
     *  可以根据班型、年级、科目筛选
     *  包括 已报名的课程
     *  按报名时间倒序
     *  支持分页,分页limit 100条
     *
     * @param int    $studentUid        查询指定用户UID
     * @param int    $courseType        查询指定课程类型，支持多个
     * @param int    $grade             年级ID
     * @param int    $subject           学科ID
     * @param int    $offset            分页偏移
     * @param int    $limit             分页大小，最大支持单页100
     * @param array  $options           扩展数据
     * @return array
     */
    public static function getListForRegisteredAdvance($studentUid, $courseType, $grade=-1, $subject=-1,  $offset = 0, $limit = 100, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForRegisteredAdvance');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseType'        => $courseType,
            'grade'             => $grade,
            'subject'           => $subject,
            'offset'            => $offset,
            'limit'             => $limit,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    /**
     * 根据学生uid级联获取stuCourse stuLesson
     * 支持课程类型筛选
     * 支持学科筛选
     * 支持品牌筛选
     * 支持分页
     * @param int $studentUid
     * @param int $courseType
     * @param int $courseSubject
     * @param array $arrBrandId
     * @param int $offset        
     * @param int $limit
     */
    public static function getListForLinkage($studentUid, $arrStudentCourseFields, $arrStudentLessonFields, $courseType = -1, $courseSubject = -1, $arrBrandId = [], $offset = 0, $limit = 100, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$stuCourseEntity, 'getListForLinkage');
        $arrParams = array(
            'studentUid'        => $studentUid,
            'courseType'        => $courseType,
            'courseSubject'     => $courseSubject,
            'arrBrandId'        => $arrBrandId,
            'offset'            => $offset,
            'limit'             => $limit,
            'arrStudentCourseFields' => $arrStudentCourseFields,
            'arrStudentLessonFields' => $arrStudentLessonFields
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    } 


}
