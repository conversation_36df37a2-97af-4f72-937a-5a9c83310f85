<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Nmq.php
 * <AUTHOR>
 * @date 2017/9/11 20:26:46
 * @brief nmq服务
 *
 **/

class Zb_Service_Nmq {

    public static $is_log_mq_transid = 1;//是否输出MQ的transID

    private static $_product = 'zb';
    private static $_topic   = 'core';
    private static $_topicCache = 'cache';
    private static $_topicExam = 'exam';
    private static $_topicKunpeng = 'kunpeng';
    private static $_topicOrder = 'order';
    private static $_topicIntegralGrant = 'integral_grant';
    private static $_topicIntegral = 'integral';
    private static $_topicWenda = 'wenda';
    private static $_topicScrm = 'scrm';
    private static $_topicPayOther = 'pay_other';
    private static $_topicEco = 'eco';
    private static $_topicJiaowu = 'jiaowu';

    const TOPIC_CORE = 'core';
    const TOPIC_CACHE = 'cache';
    const TOPIC_EXAM = 'exam';
    const TOPIC_KUNPENG = 'kunpeng';
    const TOPIC_ORDER = 'order';
    const TOPIC_OF = 'of';
    const TOPIC_PAY = 'pay';
    const TOPIC_PAY_OTHER = 'pay_other';
    const TOPIC_INTEGRAL = 'integral';
    const TOPIC_INTEGRAL_GRANT = 'integral_grant';
    const TOPIC_WENDA = 'wenda';
    const TOPIC_KP_AUDIT = 'kpaudit'; // 待废弃
    const TOPIC_KP_YAYA  = 'kpyaya';
    const TOPIC_KP_DUXUE = 'kpduxue';
    const TOPIC_KP_QK    = 'kpqk';
    const TOPIC_KP_ADULT = 'kpadult';
    const TOPIC_KP_TEACHER = 'kpteacher';
    const TOPIC_KP_ANTI_BRUSH = 'kpantibrush';
    const TOPIC_KEFU       = 'kefu';
    const TOPIC_SCRM       = 'scrm';
    const TOPIC_ACTPLAT = 'actplat';
    const TOPIC_SELL = 'sell';
    const TOPIC_KP_PERSONAL = 'kppersonal';
    const TOPIC_KP_LEC = 'kplec';
    const TOPIC_ECO = 'eco';
    const TOPIC_KP_DYLYK = 'kpdylyk';
    const TOPIC_SP_INFRA = 'sp_infra';
    const TOPIC_LAXINQK = "laxinqk";
    const TOPIC_LXQK = "lxqk";
    const TOPIC_QUALITYCHECK  = 'qualitycheck';
    const TOPIC_KP_IMC = 'kpimc';
    const TOPIC_KP_NT = 'kpnt';
    const TOPIC_KP_ALL = 'kpall';
    const TOPIC_FWYY_ALL = 'fwyy';
    const TOPIC_STB_ORDER = 'stb_order';
    const TOPIC_JIAOWU  = 'jiaowu';
    const TOPIC_LXZERODESK  = 'laxinzd';

    public static $nmqTopicMap = [
        self::TOPIC_CORE    => '业务通用',
        self::TOPIC_CACHE   => '延迟删除',
        self::TOPIC_EXAM    => '测试系统',
        self::TOPIC_KUNPENG => '鲲鹏系统',
        self::TOPIC_ORDER   => '订单核心',
        self::TOPIC_OF      => '订单履约',
        self::TOPIC_PAY     => '支付系统',
        self::TOPIC_PAY_OTHER      => '支付系统非核心',
        self::TOPIC_INTEGRAL       => '积分通用',
        self::TOPIC_INTEGRAL_GRANT => '积分发放',
        self::TOPIC_WENDA => '问答系统',
        self::TOPIC_KP_AUDIT => '鲲鹏-成人', // 待废弃
        self::TOPIC_KP_YAYA  => '鲲鹏-鸭鸭',
        self::TOPIC_KP_DUXUE => '鲲鹏-督学',
        self::TOPIC_KP_QK    => '鲲鹏-群控',
        self::TOPIC_KP_ADULT => '鲲鹏-成人',
        self::TOPIC_KP_TEACHER => '鲲鹏-班主任',
        self::TOPIC_KP_ANTI_BRUSH => '鲲鹏-群控防刷',
        self::TOPIC_KEFU       => '客服系统',
        self::TOPIC_SCRM       => '拉新客服系统',
        self::TOPIC_ACTPLAT => '活动平台',
        self::TOPIC_SELL => '商品平台',
        self::TOPIC_KP_PERSONAL => '鲲鹏-个人号',
        self::TOPIC_KP_LEC => '鲲鹏-LEC',
        self::TOPIC_ECO    => '支付推消息给ECO',
        self::TOPIC_KP_DYLYK => '鲲鹏-低幼零元课',
        self::TOPIC_SP_INFRA => '基础架构队列',
        self::TOPIC_LAXINQK  => '拉新群控',
        self::TOPIC_LXQK  => '拉新',
        self::TOPIC_LXZERODESK  => '拉新0转正工作台',
        self::TOPIC_QUALITYCHECK => '质检系统',
        self::TOPIC_KP_IMC => '鲲鹏-IMC',
        self::TOPIC_KP_NT => '鲲鹏-NT',
        self::TOPIC_KP_ALL => '鲲鹏-ALL',
        self::TOPIC_FWYY_ALL => '服务运营-ALL',
        self::TOPIC_JIAOWU => '教务',
    ];

    public static $rmqCluster = [
	    self::TOPIC_CORE => 'jxzt-rmq-zb',//对应集群nmq-zhibo.na.bjcq
    ];
    //集群
    public static $nmqCluster = [
        self::TOPIC_CORE => 'nmq-zb',//对应集群nmq-zhibo.na.bjcq
        self::TOPIC_ORDER => 'rmq-order',//对应集群nmq-order.na.bjdd
		self::TOPIC_OF => 'rmq-order',//对应集群nmq-order.na.bjdd
        self::TOPIC_CACHE => 'rmq-cache',//对应集群rmq-zhibo.na.bjdd 腾讯云 rmq-zhibo.na.qcvmbj5
        self::TOPIC_KUNPENG => 'nmq-kunpeng',// 对应集群rmq-kunpeng.na.bjdd
        self::TOPIC_PAY => 'rmq-order',// 对应集群nmq-order
        self::TOPIC_PAY_OTHER => 'rmq-currency', // 对应集群  rmq-payment-cur.na.bjdd
        self::TOPIC_INTEGRAL => 'rmq-currency', // 对应集群  rmq-payment-cur.na.bjdd
        self::TOPIC_INTEGRAL_GRANT => 'rmq-currency', // 对应集群  rmq-payment-cur.na.bjdd
        self::TOPIC_WENDA => 'nmq-zb', // 对应集群 rmq-wenda.na.bjdd
        self::TOPIC_KP_AUDIT => 'nmq-kunpeng',// 对应集群rmq-kunpeng.na.bjdd 待废弃
        self::TOPIC_KP_YAYA  => 'nmq-kunpeng',// 对应集群rmq-kunpeng.na.bjdd
        self::TOPIC_KP_DUXUE => 'nmq-kunpeng',// 对应集群rmq-kunpeng.na.bjdd
        self::TOPIC_KP_QK    => 'nmq-kunpeng',// 对应集群rmq-kunpeng.na.bjdd
        self::TOPIC_KP_ADULT => 'nmq-kunpeng',// 对应集群rmq-kunpeng.na.bjdd
        self::TOPIC_KP_TEACHER => 'nmq-kunpeng',// 对应集群rmq-kunpeng.na.bjdd
        self::TOPIC_KP_ANTI_BRUSH => 'nmq-kunpeng',// 对应集群rmq-kunpeng.na.bjdd
        self::TOPIC_KEFU    => 'nmq-zb', //集群 (容器环境) : common-svc.mq:8080  值对应的是ral的名字和rmq.json,pusher.json中的映射,不指向nmq-zb 虚机集群
        self::TOPIC_SCRM    => 'nmq-scrm',
        self::TOPIC_ACTPLAT  => 'rmq-sell',
        self::TOPIC_SELL  => 'rmq-sell',
        self::TOPIC_KP_PERSONAL => 'nmq-kunpeng',// 对应集群rmq-kunpeng.na.bjdd
        self::TOPIC_KP_LEC => 'nmq-kunpeng',// 对应集群rmq-kunpeng.na.bjdd
        self::TOPIC_ECO    => 'rmq-common',//对应 rmq-common.na.bjdd | rmq-common.na.qcvmbj5
        self::TOPIC_KP_DYLYK => 'nmq-kunpeng',// 对应集群rmq-kunpeng
        self::TOPIC_SP_INFRA => 'rmq-spinfra',
        self::TOPIC_LAXINQK => 'rmq-laxinqk', // 对应 rmq-common.na.bjdd
        self::TOPIC_LXQK => 'rmq-lxqk', // 对应 rocketmq-laxin-svc.mq:8080
        self::TOPIC_QUALITYCHECK => 'common-tx', //集群(容器环境), 值对应ral名字和rmq.json,pusher.json中的映射
        self::TOPIC_LXZERODESK => 'rmq-lxzd', // 对应 rocketmq-laxin-svc.mq:8080
        self::TOPIC_KP_IMC => 'nmq-kunpeng',// 对应集群rmq-kunpeng
        self::TOPIC_KP_NT => 'nmq-kunpeng',// 对应集群rmq-kunpeng
        self::TOPIC_KP_ALL => 'nmq-kunpeng',// 对应集群rmq-kunpeng
        self::TOPIC_FWYY_ALL => 'rmq-fwyy',// 对应集群rmq-fwyy
        self::TOPIC_STB_ORDER => 'rmq-order',//对应的集群
        self::TOPIC_JIAOWU => 'rmq-jiaowu',
    ];

    private static $_topicList  = array(
        'core',     // 业务topic
        'cache',    // 延迟删除 topic
        'exam',     // 测试系统 topic
        'kunpeng',  // 鲲鹏系统 topic
        'order',    // 交易订单 topic
        'of',       // 交易履约 topic
        'pay',       // 支付系统 topic
        'pay_other',  // 支付系统 topic
        'integral_grant', // 积分发放 topic
        'integral', // 积分通用 topic
        'wenda', //问答系统 topic
        'kpaudit', // 鲲鹏-成人 topic 待废弃
        'kpyaya',  // 鲲鹏-鸭鸭 topic
        'kpduxue', // 鲲鹏-督学 topic
        'kpqk',    // 鲲鹏-群控 topic
        'kpadult', // 鲲鹏-成人 topic
        'kpteacher', // 鲲鹏-班主任 topic
        'kpantibrush', // 鲲鹏-群控防刷 topic
        'kefu',     // 客服系统 topic(目前只有容器)
        'scrm',     // 拉新客服系统 topic
        'actplat',     // 活动平台 topic
        'sell',         //商品平台topic
        'kppersonal', // 鲲鹏-群控 topic
        'kplec', // 鲲鹏-LEC topic
        'eco', // 支付临时用
        'kpdylyk', // 鲲鹏-低幼零元课 topic
        'sp_infra', // 基础架构队列
        'laxinqk',//拉新群控 topic
        'lxqk',//拉新(新) topic
        'qualitycheck', // 质检系统 topic
        'kpimc', // 鲲鹏-IMC topic
        'kpnt', // 鲲鹏-NT topic
        'kpall', // 鲲鹏-ALL topic
    	'stb_order',//台账订单
        'fwyy', // 服务运营-ALL topic
        'jiaowu',
        'laxinzd',//拉新工作台
        'stucourse', // 教务,das
        'stucourse_check', // 教务,das
        'stucourse_hi', // 教务,das
    );
	
	
	/**
	 * 发送rmq命令
	 *
	 * @param  int    $commandNo   命令号（必须在类Zb_Const_Command中注册）
	 * @param  array  $data        命令体
	 * @param  string $topic       nmq topic
	 * @return true|false
	 */
	public static function sendCommandByrmq($commandNo, $data, $topic=null) {
		
		//参数检查
		$commandNo = intval($commandNo);
		if($commandNo <= 0) {
			Bd_Log::warning("Error:[param error], Detail:[commandNo:$commandNo]");
			return false;
		}
		
		if ($commandNo === Zb_Const_Command::COMMAND_CORE_100001) {
			$topic = self::$_topicCache;
		}
		
		if (is_null($topic)) {
			$topic = self::$_topic;
		}
		if (!in_array($topic, self::$_topicList)) {
			Bd_Log::warning("Error:[param error], Detail:[topic:$topic]");
			return false;
		}
		
		//命令必须注册
		if(!isset(Zb_Const_Command::$arrCommandMap[$commandNo])) {
			Bd_Log::warning("Error:[command not register], Detail:[commandNo:$commandNo]");
			return false;
		}
		
		//打印命令体
		$data['command_no'] = $commandNo;
		self::_printCommand($data);
		
		//获取命令体字段定义
		if ($commandNo == 100001) {
			$arrCmdConf = Bd_Conf::getConf("/zb/cmd/cmd$commandNo");
		}else{
			$arrCmdConf = Bd_Conf::getConf("/zb/cmd/$commandNo");
		}
		
		if(empty($arrCmdConf)) {
			Bd_Log::warning("Error:[getConf error], Detail:[commandNo:$commandNo, arrCmdConf: ".json_encode($arrCmdConf)."]");
			return false;
		}
		
		//命令体字段检查
		$ret = self::_checkCmd($data, $arrCmdConf);
		
		if(false === $ret) {
			Bd_Log::warning("Error:[cmd check error], Detail:[commandNo:$commandNo]");
			return false;
		}
		
		//命令发送到nmq
		$arrRes = self::_talkToRmq($commandNo, $data, $topic);
		if (false === $arrRes) {
			Bd_Log::warning("Error:[talkToNmq error], Detail:[commandNo:$commandNo data:". json_encode($data) . "]");
			return false;
		}
		
		return true;
	}
	
	
	/**
	 * 发送rmq命令
	 *
	 * @param  int    $commandNo   命令号（必须在类Zb_Const_Command中注册）
	 * @param  array  $data        命令体
	 * @param  string $topic       nmq topic
	 * @return true|false
	 */
	public static function sendCommandByRocketMQ($commandNo, $data, $topic=null) {
		
		//参数检查
		$commandNo = intval($commandNo);
		if($commandNo <= 0) {
			Bd_Log::warning("Error:[param error], Detail:[commandNo:$commandNo]");
			return false;
		}
		
		if ($commandNo === Zb_Const_Command::COMMAND_CORE_100001) {
			$topic = self::$_topicCache;
		}
		
		if (is_null($topic)) {
			$topic = self::$_topic;
		}
		if (!in_array($topic, self::$_topicList)) {
			Bd_Log::warning("Error:[param error], Detail:[topic:$topic]");
			return false;
		}
		
		//命令必须注册
		if(!isset(Zb_Const_Command::$arrCommandMap[$commandNo])) {
			Bd_Log::warning("Error:[command not register], Detail:[commandNo:$commandNo]");
			return false;
		}
		
		//打印命令体
		$data['command_no'] = $commandNo;
		self::_printCommand($data);
		
		//获取命令体字段定义
		if ($commandNo == 100001) {
			$arrCmdConf = Bd_Conf::getConf("/zb/cmd/cmd$commandNo");
		}else{
			$arrCmdConf = Bd_Conf::getConf("/zb/cmd/$commandNo");
		}
		
		if(empty($arrCmdConf)) {
			Bd_Log::warning("Error:[getConf error], Detail:[commandNo:$commandNo, arrCmdConf: ".json_encode($arrCmdConf)."]");
			return false;
		}
		
		//命令体字段检查
		$ret = self::_checkCmd($data, $arrCmdConf);
		
		if(false === $ret) {
			Bd_Log::warning("Error:[cmd check error], Detail:[commandNo:$commandNo]");
			return false;
		}
		
		//命令发送到nmq
		$arrRes = self::_talkToRocketMq($commandNo, $data, $topic);
		if (false === $arrRes) {
			Bd_Log::warning("Error:[talkToNmq error], Detail:[commandNo:$commandNo data:". json_encode($data) . "]");
			return false;
		}
		
		return true;
	}
	
	
	/**
     * 发送nmq命令
     *
     * @param  int    $commandNo   命令号（必须在类Zb_Const_Command中注册）
     * @param  array  $data        命令体
     * @param  string $topic       nmq topic
     * @return true|false
     */
    public static function sendCommand($commandNo, $data, $topic=null) {

        //参数检查
        $commandNo = intval($commandNo);
        if($commandNo <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[commandNo:$commandNo]");
            return false;
        }

        if ($commandNo === Zb_Const_Command::COMMAND_CORE_100001) {
            $topic = self::$_topicCache;
        }

        if (is_null($topic)) {
            $topic = self::$_topic;
        }
        if (!in_array($topic, self::$_topicList)) {
            Bd_Log::warning("Error:[param error], Detail:[topic:$topic]");
            return false;
        }

        //命令必须注册
        if(!isset(Zb_Const_Command::$arrCommandMap[$commandNo])) {
            Bd_Log::warning("Error:[command not register], Detail:[commandNo:$commandNo]");
            return false;
        }

        //打印命令体
        $data['command_no'] = $commandNo;
        self::_printCommand($data);

        //获取命令体字段定义
        if ($commandNo == 100001) {
            $arrCmdConf = Bd_Conf::getConf("/zb/cmd/cmd$commandNo");
        }else{
            $arrCmdConf = Bd_Conf::getConf("/zb/cmd/$commandNo");
        }

        if(empty($arrCmdConf)) {
            Bd_Log::warning("Error:[getConf error], Detail:[commandNo:$commandNo, arrCmdConf: ".json_encode($arrCmdConf)."]");
            return false;
        }

        //命令体字段检查
        $ret = self::_checkCmd($data, $arrCmdConf);

        if(false === $ret) {
            Bd_Log::warning("Error:[cmd check error], Detail:[commandNo:$commandNo]");
            return false;
        }

        //命令发送到nmq
        $arrRes = self::_talkToNmq($commandNo, $data, $topic);
        if (false === $arrRes) {
            Bd_Log::warning("Error:[talkToNmq error], Detail:[commandNo:$commandNo data:". json_encode($data) . "]");
            return false;
        }

        return true;
    }

    //命令体字段检查
    //支持类型：字符串、整型、列表（索引数组）、字典（关联数组），支持嵌套。
    //命令配置由命令发起者负责定义
    //
    //命令配置样例: /home/<USER>/conf/zb/cmd/100000.conf
    //[a]
    //type : int
    //must : 1
    //remark : 整型字段a
    //
    //[b]
    //type : string
    //must : 1
    //remark : 字符型字段b
    //
    //[c]
    //type : list
    //must : 0
    //remark : 列表c(索引数组)
    //[.list]
    //type : int
    //must : 1
    //remark : 整型
    //
    //[d]
    //type : map
    //must : 1
    //remark : 字典d(关联数组)
    //[..map]
    //[...bb1]
    //type : string
    //must : 1
    //remark : 字符bb1
    //[...bb2]
    //type : int
    //must : 0
    //remark : 整型bb2
    //
    //[e]
    //type : list
    //must : 1
    //remark : 列表c(索引数组)
    //[.list]
    //type : map
    //must : 1
    //remark : 字典d(关联数组)
    //[..map]
    //[...bb1]
    //type : string
    //must : 1
    //remark : 字符bb1
    //[...bb2]
    //type : int
    //must : 0
    //remark : 整型bb2
    private static function _checkCmd($data, $arrCmdConf) {
        $arrOutput = array();
        foreach($arrCmdConf as $key => $cmdConf) {
            if(!isset($cmdConf['type']) || !isset($cmdConf['must']) || !isset($cmdConf['remark'])) {
                Bd_Log::warning("Error:[cmdConf format error], Detail:[key:$key cmdConf:" . json_encode($cmdConf) . "]");
                return false;
            }
            //必须注明字段含义
            $remark = strlen($cmdConf['remark']);
            if(strlen($remark) <= 0) {
                Bd_Log::warning("Error:[remark empty], Detail:[key:$key]");
                return false;
            }

            //是否必须字段
            $must = intval($cmdConf['must']);
            if(!isset($data[$key])) {
                if($must === 1) {
                    Bd_Log::warning("Error:[key must exist], Detail:[key:$key]");
                    return false;
                }

                continue;
            }

            $dataValue = $data[$key];
            //字段类型
            $type = strval($cmdConf['type']);

            switch($type) {
            case 'string'://字符串
                if(!is_string($dataValue)) {
                    Bd_Log::warning("Error:[value must be string], Detail:[key:$key value:$dataValue]");
                    return false;
                }
                $arrOutput[$key] = $dataValue;

                break;
            case 'int'://整形
                if(!is_int($dataValue)) {
                    Bd_Log::warning("Error:[value must be int], Detail:[key:$key value:$dataValue]");
                    return false;
                }

                $arrOutput[$key] = $dataValue;

                break;
            case 'list'://列表
                if(!isset($cmdConf['list'])) {
                    Bd_Log::warning("Error:[conf format error], Detail:[key:$key cmdConf:" . json_encode($cmdConf) . "]");
                    return false;
                }

                if(!is_array($dataValue)) {
                    Bd_Log::warning("Error:[value must be list], Detail:[key:$key]");
                    return false;
                }

                $output = array();
                foreach($dataValue as $subKey => $subValue) {
                    $ret = self::_checkCmd(array($subKey => $subValue), array($subKey => $cmdConf['list']));
                    if(false === $ret) {
                        Bd_Log::warning("Error:[list check error], Detail:[key:$key subKey:$subKey]");
                        return false;
                    }

                    $output = array_merge($output, $ret);
                }

                $arrOutput[$key] = $output;

                break;
            case 'map'://字典
                if(!isset($cmdConf['map'])) {
                    Bd_Log::warning("Error:[conf format error], Detail:[key:$key cmdConf:" . json_encode($cmdConf) . "]");
                    return false;
                }

                if(!is_array($dataValue)) {
                    Bd_Log::warning("Error:[value must be map], Detail:[key:$key]");
                    return false;
                }

                $output = array();
                $cmdMapConf = $cmdConf['map'];
                foreach($cmdMapConf as $subKey => $subCmdConf) {
                    $ret = self::_checkCmd(array($subKey => $dataValue[$subKey]), array($subKey => $subCmdConf));
                    if(false === $ret) {
                        Bd_Log::warning("Error:[map check error], Detail:[key:$key]");
                        return false;
                    }

                    $output = array_merge($output, $ret);
                }

                $arrOutput[$key] = $output;

                break;
            default:
                Bd_Log::warning("Error:[conf type invalid], Detail:[key:$key type:$type]");
                return false;
            }
        }
        return $arrOutput;
    }

    //访问nmq服务
    private static function _talkToNmq($commandNo, $data, $topic){

        //设置nmq_proxy接口参数
        $data['_product'] = self::$_product;
        $data['_topic']   = $topic;
        $data['_cmd']     = strval($commandNo);
        $data['_caller_uri'] = $_SERVER['HTTP_X_BD_CALLER_URI'];
        $data['_press_mark'] = self::_getPressMark($data['_caller_uri']);
        $data['_idc_tag']   = ral_get_idc();
        //添加_span_id 2020-04-28
        if(!isset($data['_span_id'])){
            $data['_span_id'] = Bd_Log::getParentSpanid();  //NMQ pusher会自动添加随机后缀
        }

        //设置当前logid
        if(defined('LOG_ID')){
            ral_set_logid(LOG_ID);
        }

        //一致性hash
        $balanceId = isset($data['groupKey']) ? intval($data['groupKey']) : mt_rand();

        //获取集群名
        $clusterName = isset(self::$nmqCluster[$topic])?strval(self::$nmqCluster[$topic]):self::$nmqCluster[self::TOPIC_CORE];

        //活动平台nmq切rmq的兼容。rename topic
        if($data["_topic"] == self::TOPIC_ACTPLAT) $data["_topic"] = self::TOPIC_CORE;
        //教务方向特殊处理下jiaowu topic还用core
        if($data["_topic"] == self::TOPIC_JIAOWU) {
            $data['_topic'] = self::TOPIC_CORE;
        }
        //调用nmq服务
        $ret = ral($clusterName, 'post', $data, $balanceId);
        if(false === $ret){
            $errno           = ral_get_errno();
            $errmsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[nmq connect error], Detail[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
            return false;
        }

        $errno  = intVal($ret['_error_no']);
        $errmsg = strVal($ret['_error_msg']);
        if(self::$is_log_mq_transid){
            Bd_Log::addNotice ('nmq_transid:'.$ret['_transid'], $commandNo.':'.$errno);
        }
        
        if(0 !== $errno) {
            Bd_Log::warning("Error:[nmq process error], Detail[errno:$errno errmsg:$errmsg]");
            return false;
        }


        return true;
    }
	//访问nmq服务
	private static function _talkToRmq($commandNo, $data, $topic){
		//设置nmq_proxy接口参数
		$data['_product'] = self::$_product;
		$data['_topic']   = $topic;
		$data['_cmd']     = strval($commandNo);
		$data['_caller_uri'] = $_SERVER['HTTP_X_BD_CALLER_URI'];
		$data['_press_mark'] = self::_getPressMark($data['_caller_uri']);
		$data['_idc_tag']   = ral_get_idc();
		//添加_span_id 2020-04-28
		if(!isset($data['_span_id'])){
			$data['_span_id'] = Bd_Log::getParentSpanid();  //NMQ pusher会自动添加随机后缀
		}
		
		//设置当前logid
		if(defined('LOG_ID')){
			ral_set_logid(LOG_ID);
		}
		
		//一致性hash
		$balanceId = isset($data['groupKey']) ? intval($data['groupKey']) : mt_rand();
		
		//获取集群名
		$clusterName = isset(self::$rmqCluster[$topic])?strval(self::$rmqCluster[$topic]):self::$rmqCluster[self::TOPIC_CORE];
		
		//活动平台nmq切rmq的兼容。rename topic
		if($data["_topic"] == self::TOPIC_ACTPLAT) $data["_topic"] = self::TOPIC_CORE;
		
		//调用nmq服务
		$ret = ral($clusterName, 'post', $data, $balanceId);
		if(false === $ret){
			$errno           = ral_get_errno();
			$errmsg          = ral_get_error();
			$protocol_status = ral_get_protocol_code();
			Bd_Log::warning("Error:[nmq connect error], Detail[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
			return false;
		}
		
		$errno  = intVal($ret['_error_no']);
		$errmsg = strVal($ret['_error_msg']);
		if(self::$is_log_mq_transid){
			Bd_Log::addNotice ('nmq_transid:'.$ret['_transid'], $commandNo.':'.$errno);
		}
		
		if(0 !== $errno) {
			Bd_Log::warning("Error:[nmq process error], Detail[errno:$errno errmsg:$errmsg]");
			return false;
		}
		
		
		return true;
	}
	
	private static function _talkToRocketMq($commandNo, $data, $topic){
		//设置nmq_proxy接口参数
		$data['_product'] = self::$_product;
		$data['_topic']   = $topic;
		$data['_cmd']     = strval($commandNo);
		$data['_caller_uri'] = $_SERVER['HTTP_X_BD_CALLER_URI'];
		$data['_press_mark'] = self::_getPressMark($data['_caller_uri']);
		$data['_idc_tag']   = ral_get_idc();
		//添加_span_id 2020-04-28
		if(!isset($data['_span_id'])){
			$data['_span_id'] = Bd_Log::getParentSpanid();  //NMQ pusher会自动添加随机后缀
		}
		
		//设置当前logid
		if(defined('LOG_ID')){
			ral_set_logid(LOG_ID);
		}
		
		//一致性hash
		$balanceId = isset($data['groupKey']) ? intval($data['groupKey']) : mt_rand();
		
		//获取集群名
		$clusterName = 'jxzt-rocketmq-zb';
		//活动平台nmq切rmq的兼容。rename topic
		if($data["_topic"] == self::TOPIC_ACTPLAT) $data["_topic"] = self::TOPIC_CORE;
		
		//调用nmq服务
		$ret = ral($clusterName, 'post', $data, $balanceId);
		if(false === $ret){
			$errno           = ral_get_errno();
			$errmsg          = ral_get_error();
			$protocol_status = ral_get_protocol_code();
			Bd_Log::warning("Error:[nmq connect error], Detail[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
			return false;
		}
		
		$errno  = intVal($ret['_error_no']);
		$errmsg = strVal($ret['_error_msg']);
		if(self::$is_log_mq_transid){
			Bd_Log::addNotice ('nmq_transid:'.$ret['_transid'], $commandNo.':'.$errno);
		}
		
		if(0 !== $errno) {
			Bd_Log::warning("Error:[nmq process error], Detail[errno:$errno errmsg:$errmsg]");
			return false;
		}
		
		
		return true;
	}
	
	
	//打印命令体
    private static function _printCommand($data) {
        $commandNo = $data['command_no'];
        foreach($data as $key => $value) {
            if(is_string($value)) {
                if(strlen($value) > 50) {
                    $data[$key] = "LongString";
                }
            }
        }

        Bd_Log::addNotice ('nmq_input' . $commandNo, json_encode($data));

        return true;
    }

    /**
     * @comment 获取压测标识 1-是 0-否
     *
     * @param string $callerUri ocs生成
     * @return int
    **/
    private static function _getPressMark($callerUri) {
        if (empty($callerUri)) {
            return 0;
        }

        if (false === strpos($callerUri, "/qa/test")) {
            return 0;
        }

        return 1;
    }

    /**
     * 发送rmq命令, 指定topic和集群
     *
     * @param int $commandNo 命令号（必须在类Zb_Const_Command中注册）
     * @param array $data 命令体
     * @param string $topic nmq topic
     * @param string $cluster
     * @return true|false
     */
    public static function sendCommandByRmqCluster($commandNo, $data, $topic, $cluster)
    {
        // 参数检查
        $commandNo = intval($commandNo);
        if($commandNo <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[commandNo:$commandNo]");
            return false;
        }

        if ($commandNo === Zb_Const_Command::COMMAND_CORE_100001) {
            $topic = self::$_topicCache;
        }

        if (is_null($topic)) {
            $topic = self::$_topic;
        }
        if (!in_array($topic, self::$_topicList)) {
            Bd_Log::warning("Error:[param error], Detail:[topic:$topic]");
            return false;
        }

        //命令必须注册
        if (!isset(Zb_Const_Command::$arrCommandMap[$commandNo])) {
            Bd_Log::warning("Error:[command not register], Detail:[commandNo:$commandNo]");
            return false;
        }

        //打印命令体
        $data['command_no'] = $commandNo;
        self::_printCommand($data);

        //获取命令体字段定义
        if ($commandNo == 100001) {
            $arrCmdConf = Bd_Conf::getConf("/zb/cmd/cmd$commandNo");
        } else {
            $arrCmdConf = Bd_Conf::getConf("/zb/cmd/$commandNo");
        }

        if (empty($arrCmdConf)) {
            Bd_Log::warning("Error:[getConf error], Detail:[commandNo:$commandNo, arrCmdConf: ".json_encode($arrCmdConf)."]");
            return false;
        }

        //命令体字段检查
        $ret = self::_checkCmd($data, $arrCmdConf);

        if (false === $ret) {
            Bd_Log::warning("Error:[cmd check error], Detail:[commandNo:$commandNo]");
            return false;
        }

        //命令发送到nmq
        $arrRes = self::_talkToRmqCluster($commandNo, $data, $topic, $cluster);
        if (false === $arrRes) {
            Bd_Log::warning("Error:[talkToNmq error], Detail:[commandNo:$commandNo data:". json_encode($data) . "]");
            return false;
        }

        return true;
    }

    /**
     * 访问rmq服务
     * @param $commandNo
     * @param $data
     * @param $topic
     * @param $cluster
     * @return bool
     */
    private static function _talkToRmqCluster($commandNo, $data, $topic, $cluster)
    {
        // 获取集群名
        if (empty($cluster)) {
            Bd_Log::warning("Error:[cluster is empty]");
            return false;
        }

        //设置nmq_proxy接口参数
        $data['_product'] = self::$_product;
        $data['_topic']   = $topic;
        $data['_cmd']     = strval($commandNo);
        $data['_caller_uri'] = $_SERVER['HTTP_X_BD_CALLER_URI'];
        $data['_press_mark'] = self::_getPressMark($data['_caller_uri']);
        $data['_idc_tag']   = ral_get_idc();
        //添加_span_id 2020-04-28
        if (!isset($data['_span_id'])) {
            $data['_span_id'] = Bd_Log::getParentSpanid();  //NMQ pusher会自动添加随机后缀
        }

        //设置当前logid
        if (defined('LOG_ID')) {
            ral_set_logid(LOG_ID);
        }

        //一致性hash
        $balanceId = isset($data['groupKey']) ? intval($data['groupKey']) : mt_rand();

        // 活动平台nmq切rmq的兼容。rename topic
        if ($data["_topic"] == self::TOPIC_ACTPLAT) {
            $data["_topic"] = self::TOPIC_CORE;
        }

        //调用nmq服务
        $ret = ral($cluster, 'post', $data, $balanceId);
        if (false === $ret) {
            $errno           = ral_get_errno();
            $errmsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[nmq connect error], Detail[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
            return false;
        }

        $errno  = intVal($ret['_error_no']);
        $errmsg = strVal($ret['_error_msg']);
        if (self::$is_log_mq_transid) {
            Bd_Log::addNotice ('nmq_transid:'.$ret['_transid'], $commandNo.':'.$errno);
        }

        if (0 !== $errno) {
            Bd_Log::warning("Error:[nmq process error], Detail[errno:$errno errmsg:$errmsg]");
            return false;
        }

        return true;
    }
}
