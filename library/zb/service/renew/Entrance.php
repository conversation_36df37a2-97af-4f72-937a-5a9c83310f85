<?php

class Zb_Service_Renew_Entrance
{
    private $nowTime;

    public function __construct()
    {
        $this->nowTime = Hkzb_Util_FuDao::getCurrentTimeStamp();
    }

    public function getEntranceInfo($uid, $cuid)
    {
        // 获取当前的续报季
        $renewSeason = $this->getRenewSeason();
        switch ($renewSeason) {
            case Zb_Const_Renew::RENEW_WINTER:
                $ret = $this->getWinterEntrance($uid, $cuid);
                break;
            case Zb_Const_Renew::RENEW_SPRING:
                $ret = $this->getSpringEntrance($uid, $cuid);
                break;
            case Zb_Const_Renew::RENEW_SUMMER:
                $ret = $this->getSummerEntrance($uid, $cuid);
                break;
            case Zb_Const_Renew::RENEW_AUTUMN:
                $ret = $this->getAutumnEntrance($uid, $cuid);
                break;
            default:
                return $this->defaultOutput();
        }
        return $ret;
    }

    public function getRenewSeason()
    {
        foreach (Zb_Const_Renew::RENEW_SEASON as $key => $val) {
            if ($this->nowTime >= strtotime($val['start_time']) && $this->nowTime < strtotime($val['end_time'])) {
                return $key;
            }
        }
        return Zb_Const_Renew::RENEW_NULL;
    }

    private function getWinterEntrance($uid, $cuid)
    {
        // todo winter entrance info
        $output = $this->defaultOutput();
        $grade  = $this->recommendGrade($cuid, $uid);
        if ($grade > 10) {
            if ($grade > 60) {
                $roleList = array(985);
            } else {
                $roleList = array(973);
            }
            if ($this->nowTime < strtotime('2021-1-10 10:00:00') || $this->nowTime > strtotime('2021-05-11')) {
                return $output;
            }
        } else {
            $roleList = array(1046,1097);
            if ($grade > 4) { // 高中
                if ($this->nowTime < strtotime('2021-1-11 10:00:00') || $this->nowTime > strtotime('2021-05-17')) {
                    return $output;
                }
            } else { // 初中
                if ($this->nowTime < strtotime('2021-1-11 10:00:00') || $this->nowTime > strtotime('2021-05-26')) {
                    return $output;
                }
            }
        }

        $res    = $this->getUserRole($uid, $roleList);// 130 2020购买秋季班课用户且未退款
        if ($grade >= 60) {
            if (!$res['data'][$uid][985]) {
                return $output;
            }
        } elseif ($grade > 10 && $grade < 60) {
            if (!$res['data'][$uid][973]) {
                return $output;
            }
        } elseif($grade > 4) {
            if (!$res['data'][$uid][1097]) {
                return $output;
            }
        }else {
            if(!$res['data'][$uid][1046]) {
                return $output;
            }
        }

        $host = 'https://h5-sell.zuoyebang.com';
        if ($grade > 10) { // 小学部和学前
            $output['jumpUrl'] = $host . '/static/hy/sell-promotion/spring-promotion.html?ZybHideTitle=1&hideNativeTitleBar=1&hideNav=1&fillHomeIndicator=0&lastfrom=in_mycourse_xubaocapsule_sell_&fr=ori_in_mycourse_xubaocapsule_sell_&grade=' . $grade;
        } else {
            $output['jumpUrl'] = $host . '/static/hy/sell-promotion/spring-entry.html?ZybHideTitle=1&hideNativeTitleBar=1&hideNav=1&fillHomeIndicator=0&lastfrom=in_mycourse_xubaocapsule_sell_&fr=ori_in_mycourse_xubaocapsule_sell_&grade=' . $grade;
        }
        $output['sellSwitch'] = 1;
        $output['imgUrlIpad'] = 'https://img.zuoyebang.cc/zyb_34040345c81add969f3aace2c53285cd.png';
        $output['imgUrl']     = 'https://img.zuoyebang.cc/zyb_34040345c81add969f3aace2c53285cd.png';
        return $output;
    }

    private function getSpringEntrance($uid, $cuid)
    {
        // todo spring entrance info
        $output = $this->defaultOutput();
        $grade  = $this->recommendGrade($cuid, $uid);
        $person = Zb_Const_Renew::$gradeToRoles[$grade];
//        if(empty($person)) {
            return $output;
//        }
        // 获取角色，是否插班生
        $res = $this->getUserRoleV2($uid, $person);// 130 2020购买秋季班课用户且未退款

        // 高中 （初三，高一，高二）
        $res = $res['data'];
        // 新高一 非春三用户
        $now = $this->nowTime;

        // 新高一 非春三用户 高中 （初三，高一，高二）
        if ((($grade == "5" && in_array(Zb_Const_Renew::SPRING_2021_NORMAL_5, $res['yes']))
                || ($grade == "6" && in_array(Zb_Const_Renew::SPRING_2021_NORMAL_6, $res['yes'])))
            && $now < strtotime('2021-04-29 10:00')
        ) {
            return $output;
        }

        if($grade == "4" && $now < strtotime('2021-04-08 10:00')) {
            return $output;
        }
        // 高三 4.9
        if (($grade == "7")  && $now < strtotime('2021-04-08 10:00')) {
            return $output;
        }


        // 初中 （六年级)
        if ($grade == "16" && (
                (in_array(Zb_Const_Renew::SPRING_2021_NORMAL_16_1, $res['yes']) && $now < strtotime('2021-5-13 10:00'))
                || (in_array(Zb_Const_Renew::SPRING_2021_NORMAL_16_2, $res['yes']) && $now < strtotime('2021-05-23 10:00'))
                || (in_array(Zb_Const_Renew::SPRING_2021_NORMAL_16_2_1, $res['yes']) && $now < strtotime('2021-05-13 10:00'))
            )) {
            return $output;
        }

        if ($grade == "16" && (
                (in_array(Zb_Const_Renew::SPRING_2021_NORMAL_16_1, $res['yes']) && $now > strtotime('2021-5-24 24:00'))
                || (in_array(Zb_Const_Renew::SPRING_2021_NORMAL_16_2, $res['yes']) && $now > strtotime('2021-06-07 24:00'))
                || (in_array(Zb_Const_Renew::SPRING_2021_NORMAL_16_2_1, $res['yes']) && $now > strtotime('2021-06-07 24:00'))
            )) {
            return $output;
        }

        // 初一
        if ($grade == "2"
            && (in_array(Zb_Const_Renew::SPRING_2021_NORMAL_2_1, $res['yes']))
            && $now < strtotime('2021-04-23 10:00')) {
            return $output;
        }
        // 初二
        if ($grade == "3"
            && (in_array(Zb_Const_Renew::SPRING_2021_NORMAL_3_1, $res['yes']))
            && $now < strtotime('2021-04-23 10:00')) {
            return $output;
        }

        if ($grade == "2"
            && !in_array(Zb_Const_Renew::SPRING_2021_NORMAL_2_1, $res['yes'])
            && in_array(Zb_Const_Renew::SPRING_2021_NORMAL_2_2, $res['yes'])
            && $now < strtotime('2021-05-14 19:00')
        ) {
            return $output;
        }

        if ($grade == "3"
            && !in_array(Zb_Const_Renew::SPRING_2021_NORMAL_3_1, $res['yes'])
            && in_array(Zb_Const_Renew::SPRING_2021_NORMAL_3_2, $res['yes'])
            && $now <= strtotime('2021-05-14 19:00')
        ) {
            return $output;
        }


        // 如果是小学和大班就走 5.5 10:00 开始，正常角色
        if (($grade > 10) && $grade != 16 && $now < strtotime('2021-05-14 10:00')) {
            return $output;
        }

        // 如果是小学和大班就走 5.5 10:00 开始，只有春下角色就走5.28
        if ($grade > 10 && $grade != 16 && $now < strtotime('2021-6-4 10:00')
            && ((in_array(Zb_Const_Renew::SPRING_2021_NORMAL_60_2, $res['yes']) && !in_array(Zb_Const_Renew::SPRING_2021_NORMAL_60_1, $res['yes']) && !in_array(Zb_Const_Renew::SPRING_2021_NORMAL_1_1, $res['yes']))
                || (in_array(Zb_Const_Renew::SPRING_2021_NORMAL_1_2, $res['yes']) && !in_array(Zb_Const_Renew::SPRING_2021_NORMAL_1_1, $res['yes']) && !in_array(Zb_Const_Renew::SPRING_2021_NORMAL_60_1, $res['yes'])))) {
            return $output;
        }

        $host = 'https://h5-sell.zuoyebang.com';
        if (in_array($grade, [2, 3])) { // 小学部和学前
            $output['jumpUrl'] = $host . '/static/hy/sell-summer-sales/summer-junior-entry.html?ZybHideTitle=1&hideNativeTitleBar=1&hideNav=1&fillHomeIndicator=0&lastfrom=in_mycourse_xubaocapsule_sell_&fr=ori_in_mycourse_xubaocapsule_sell_&grade=' . $grade;
        } else if (in_array($grade, [4,5,6,7])){
            $output['jumpUrl'] = $host . '/static/hy/sell-summer-sales/senior-promotion.html?ZybHideTitle=1&hideNativeTitleBar=1&hideNav=1&fillHomeIndicator=0&lastfrom=in_mycourse_xubaocapsule_sell_&fr=ori_in_mycourse_xubaocapsule_sell_&grade=' . $grade;
        } else if (in_array($grade, [62,11,12,13,14,15])) {
            $output['jumpUrl'] = $host . '/static/hy/sell-primary-sales/primary-promotion.html?ZybHideTitle=1&hideNativeTitleBar=1&hideNav=1&fillHomeIndicator=0&lastfrom=in_mycourse_xubaocapsule_sell_&fr=ori_in_mycourse_xubaocapsule_sell_&grade=' . $grade;
        }else if ($grade == 16) {
            $output['jumpUrl'] = $host . '/static/hy/sell-junior-sales/summer-junior-entry.html?ZybHideTitle=1&hideNativeTitleBar=1&hideNav=1&fillHomeIndicator=0&lastfrom=in_mycourse_xubaocapsule_sell_&fr=ori_in_mycourse_xubaocapsule_sell_&grade=' . $grade;
        }
        $output['sellSwitch'] = 1;
        $output['imgUrlIpad'] = 'https://img.zuoyebang.cc/zyb_34040345c81add969f3aace2c53285cd.png';
        $output['imgUrl']     = 'https://img.zuoyebang.cc/zyb_34040345c81add969f3aace2c53285cd.png';
        return $output;
        
    }

    private function getSummerEntrance($uid, $cuid)
    {
        // todo summer entrance info
        return $this->defaultOutput();
    }

    private function getAutumnEntrance($uid, $cuid)
    {
        $output = $this->defaultOutput();
        $grade  = $this->recommendGrade($cuid, $uid);
        if($grade>60){
            return $output;
        }else if ($grade > 10) {
            $roleList = array(1481);
            if ($this->nowTime < strtotime('2021-12-10 00:00:00') || $this->nowTime > strtotime('2022-01-10 23:59:59')) {
                return $output;
            }
        } else {
            if ($grade > 4) { // 高中
                $roleList = array(1865,1866,1867);
                if ($this->nowTime < strtotime('2021-11-05 20:00:00') || $this->nowTime > strtotime('2022-02-10 23:59:58')) {
                    return $output;
                }
            } else { // 初中
                $roleList = array(1868,1869,1870);
                if ($this->nowTime < strtotime('2021-12-10 00:00:00') || $this->nowTime > strtotime('2022-12-31 23:59:59')) {
                    return $output;
                }
            }
        }

        $res    = $this->getUserRole($uid, $roleList);
        Bd_Log::notice(json_encode($res,true),"Zb_Service_Renew_Entrance获取用户角色getUserRole");
        if (false === $res || $res['errNo'] != 0 ) {
            return $output;
        }

        if ($grade >= 10) {
            if ($res['data'][$uid][1481]) {
                if ($this->nowTime < strtotime('2021-12-10:00:00') || $this->nowTime > strtotime('2022-01-10 23:59:59')) {
                    return $output;
                }
            }else{
                return $output;
            }
        } elseif ($grade > 4) {
            //高中
            if ($res['data'][$uid][1865] || $res['data'][$uid][1866] || $res['data'][$uid][1867]) {
                if ($this->nowTime < strtotime('2021-11-05 20:00:00') || $this->nowTime > strtotime('2022-02-10 23:59:58')) { 
                    return $output;
                }
            }else{
                return $output;
            }
        } else {
            //初中
            if ($res['data'][$uid][1868] || $res['data'][$uid][1869] || $res['data'][$uid][1870]) {
                if ($this->nowTime < strtotime('2021-12-10 00:00:00') || $this->nowTime > strtotime('2022-12-31 23:59:59')) {
                    return $output;
                }
            }else{
                return $output;
 
            }
        }

        $host = 'https://h5-sell.zuoyebang.com'; 
        //高中saleWay=1,小学和初中是2
        if(in_array($grade, [5,6,7])){
            $output['jumpUrl'] = $host . '/static/hy/sell-promotion-sales/primary-promotion.html?ZybHideTitle=1&hideNativeTitleBar=1&padPhone=1&hideNav=1&fillHomeIndicator=0&lastfrom=in_mycourse_xubaocapsule_sell_&fr=ori_in_mycourse_xubaocapsule_sell_&year=2021&saleWay=1&season=3&grade=' . $grade;
        }else{
            $output['jumpUrl'] = $host . '/static/hy/sell-promotion-sales/primary-promotion.html?ZybHideTitle=1&hideNativeTitleBar=1&padPhone=1&hideNav=1&fillHomeIndicator=0&lastfrom=in_mycourse_xubaocapsule_sell_&fr=ori_in_mycourse_xubaocapsule_sell_&year=2021&season=3&saleWay=2&grade=' . $grade; 
        }
        

        $output['sellSwitch'] = 1;
        $output['imgUrlIpad'] = 'https://img.zuoyebang.cc/zyb_34040345c81add969f3aace2c53285cd.png';
        $output['imgUrl']     = 'https://img.zuoyebang.cc/zyb_34040345c81add969f3aace2c53285cd.png';
        
        return $output;
    }

    private function getUserRole($uid, $roleList)
    {
        $url       = '/userrole/coreapi/oldcheckuserroles';
        $roleStr   = implode(',', $roleList);
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($url);
        $arrParams = array(
            'userId'  => $uid,
            'source'  => 4,
            'roleIds' => $roleStr
        );

        return Zb_Util_ZbServiceTools::post('userrole', $arrParams, $arrHeader);
    }

    private function getUserRoleV2($uid, $roleList)
    {
        $url       = '/userrole/coreapi/checkuserroles';
        $roleStr   = implode(',', $roleList);
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($url);
        $arrParams = array(
            'userId'  => $uid,
            'source'  => 4,
            'roleIds' => $roleStr
        );

        return Zb_Util_ZbServiceTools::post('userrole', $arrParams, $arrHeader);
    }

    /**
     * Desc: 挖掘默认grade
     * @param $cuid
     * @param $studentUid
     * @return int
     * Created by PhpStorm.
     * User: xiaoyang
     * Date: 2019-03-06
     * Time: 17:52
     */
    private function recommendGrade($cuid, $studentUid)
    {
        $grade    = 0;
        $objUser  = new Hk_Ds_User_Ucloud();
        $userInfo = $objUser->getUserInfo($studentUid);
        if (!empty($userInfo)) {
            $grade = intval($userInfo['grade']);
        }
        if (intval($grade) <= 0) {
            $grade = 2;
        }
        return $grade;
    }

    private function defaultOutput()
    {
        return array(
            'jumpUrl'    => '',
            'sellSwitch' => 0,
            'imgUrlIpad' => '',
            'imgUrl'     => ''
        );
    }
}
