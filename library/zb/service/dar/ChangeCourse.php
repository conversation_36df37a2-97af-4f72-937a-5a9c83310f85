<?php
/**
 * Copyright (c) 1998-2018 作业帮. (http://www.zybang.com)
 * o开头的变量是对象,a开头的变量是数组
 * Author: MaRongcai
 * Date: 19/3/2
 * Class Zb_Service_Dar_ChangeCourse 转约RPC
 */
 

class Zb_Service_Dar_ChangeCourse {

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'changeCourse';


    public static function getFinalSubTradeFromChange($userId, $subTradeId) {
        $aHeader        = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getFinalSubTradeFromChange');
        $aParameter     = array(
            'userId'            => $userId,
            'originSubTradeId'  => $subTradeId
        );

        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }

    /**
     * 获取转班链路信息
     * @param $userId
     * @param $subTradeId
     * @param bool $related | 是否获取关联链路信息， 默认不获取
     * @return array
     */
    public static function getTransferChain($userId, $subTradeId, $related=false) {
        $aHeader        = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getTransferChain');
        $input = [
            "userId" => $userId,
            "subTradeId" => $subTradeId,
            "related" => $related,
        ];
        return Zb_Util_ZbServiceTools::post(self::$service, $input, $aHeader);
    }

	/**
     * 转班详情
     *
     * @param  int      $userId
     * @param  array    $transferBatchId
     * @param  array    $refundDetailFields
     * @param  array    $options
     * @return array
     */
    public static function getKVByTransferBatchId($userId, $transferBatchId, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByTransferBatchId');
        $arrParams = array(
            'userId'            => $userId,
            'changeBatchId'		=> $transferBatchId,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
