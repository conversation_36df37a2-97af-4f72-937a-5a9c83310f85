<?php
/**
 * Copyright (c) 2018 zuoyebang.com, Inc. All Rights Reserved
 * @author: liu<PERSON><PERSON>@zuoyebang.com
 * @file: zb/service/dar/OpTransfer.php
 * @date: 2019/5/26
 * @file: 8:40 PM
 * @desc: 转班操作订单数据连接层
 */

class Zb_Service_Dar_OpTransfer {

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'opTransfer';


    public static function init($input) {
        $arrParams = $input;
        return self::helper($arrParams, "init");
    }

    public static function commit($input) {
        $arrParams = $input;
        return self::helper($arrParams, "commit");
    }

    public static function cancel($input) {
        $arrParams = $input;
        return self::helper($arrParams, "cancel");
    }

    private static function helper($arrParams, $api) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, $api);
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
