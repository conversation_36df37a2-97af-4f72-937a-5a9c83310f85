<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   RefundDetail.php
 * <AUTHOR>
 * @date   2018/8/18 下午7:26
 * @brief
 **/
 
class Zb_Service_Dar_RefundDetail {

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'refundDetail';


    /**
     * 2.7  退款流水详情
     *
     * @param  int      $userId
     * @param  array    $refundBatchIds
     * @param  array    $refundDetailFields
     * @param  array    $options
     * @return array
     */
    public static function getKVByRefundBatchIds($userId, $refundBatchIds, $refundDetailFields=array(), $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByRefundBatchIds');
        $arrParams = array(
            'userId'            => $userId,
            'refundBatchIds'    => $refundBatchIds,
            'refundDetailFields'=> $refundDetailFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 2.8  退款流水详情
     *
     * @param  int      $userId
     * @param  array    $refundBatchSubIds
     * @param  array    $refundDetailFields
     * @param  array    $options
     * @return array
     */
    public static function getKVBySubTradeIds($userId, $refundBatchSubIds, $refundDetailFields=array(), $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVBySubTradeIds');
        $arrParams = array(
            'userId'            => $userId,
            'refundBatchSubIds' => $refundBatchSubIds,
            'refundDetailFields'=> $refundDetailFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据subTradeId 获取退款列表
     * @param       $userId
     * @param       $aSubTradeId
     * @param array $refundDetailFields
     * @param array $options
     * @return array
     */
    public static function getListBySubTradeIds($userId, $aSubTradeId, $refundDetailFields=array(), $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getListBySubTradeIds');
        $arrParams = array(
            'userId'            => $userId,
            'subTradeIds'       => $aSubTradeId,
            'refundDetailFields'=> $refundDetailFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
