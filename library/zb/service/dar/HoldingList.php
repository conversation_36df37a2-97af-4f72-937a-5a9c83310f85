<?php
/**
 * Copyright (c) 1998-2018 作业帮. (http://www.zybang.com)
 * o开头的变量是对象,a开头的变量是数组
 * Author: MaRongcai
 * Date: 19/3/2
 * Class Zb_Service_Dar_ChangeCourse 转约RPC
 */
 

class Zb_Service_Dar_HoldingList {

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'holdingList';


    public static function addHoldingList($userId, $tradeId,$subTradeId,$holdingStartTime,$holdingEndTime) {
        $aHeader        = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'addHoldingList');
        $aParameter     = array(
            'userId'            => $userId,
            'tradeId'           => $tradeId,
            'subTradeId'        => $subTradeId,
            'holdingStartTime'  => $holdingStartTime,
            'holdingEndTime'    => $holdingEndTime,
        );

        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }

    public static function updateHoldingList($userId, $tradeId,$subTradeId,$holdingStatus = 0,$holdingTime) {
        $aHeader        = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'updateHoldingList');
        $aParameter     = array(
            'userId'            => $userId,
            'tradeId'           => $tradeId,
            'subTradeId'        => $subTradeId,
            'holdingStatus'     => $holdingStatus,
            'holdingTime'       => $holdingTime,
        );

        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }
}
