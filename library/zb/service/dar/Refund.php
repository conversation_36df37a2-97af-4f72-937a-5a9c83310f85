<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Trade.php
 * <AUTHOR>
 * @date   2018/7/3 下午6:45
 * @brief
 **/
 

class Zb_Service_Dar_Refund{

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'refund';


    /**
     * 退款流水列表信息
     *
     * @param  int      $userId
     * @param  array    $refundFields
     * @param  int      $page
     * @param  int      $pageSize
     * @param  array    $options
     * @return array
     */
    public static function getListByUserId($userId, $refundFields=array(), $page=1, $pageSize=10,  $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getListByUserId');
        $arrParams = array(
            'userId'            => $userId,
            'refundField'       => $refundFields,
            'page'              => $page,
            'pageSize'          => $pageSize,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * @param  int      $userId
     * @param  array    $refundNos
     * @param  array    $refundFields
     * @param  array $options
     * @return array
     */
    public static function getKVByRefundNo($userId, $refundNos, $refundFields, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getListByUserId');
        $arrParams = array(
            'userId'            => $userId,
            'refundNos'         => $refundNos,
            'refundFields'      => $refundFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

}
