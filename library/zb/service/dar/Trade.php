<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Trade.php
 * <AUTHOR>
 * @date   2018/7/3 下午6:45
 * @brief
 **/
 

class Zb_Service_Dar_Trade {

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'trade';


    /**
     * 学生的购课信息
     *
     * @param array     $studentCourseIds   学生课程ID映射数组，array([studentUid]_[courseUid], ...)。eg. ["2183318929_89864", ...]
     * @param array     $fields             接口返回的Trade字段
     * @param array     $options            扩展数据, 控制
     * @param array     $orderChannel       订单来源
     * @return array
     */
    public static function getKVByCourseIds($studentCourseIds, $fields, $options=array(), $orderChannel=[0]) {
        return Zb_Service_Dar_SubTrade::getKVByCourseIds($studentCourseIds, $fields, $options, $orderChannel);
    }


    /**
     * 批量获取主订单信息，以及其子订单信息，预付子订单信息
     *
     * @param  array    $userTradeIds
     * @param  array    $tradeFields
     * @param  array    $subTradeFields
     * @param  array    $preTradeFields
     * @param  array    $options
     * @return array
     */
    public static function getKVByTradeIds($userTradeIds, $tradeFields=array(), $subTradeFields=array(), $preTradeFields=array(), $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByTradeIds');
        $arrParams = array(
            'userTradeIds'      => $userTradeIds,
            'tradeFields'       => $tradeFields,
            'subTradeFields'    => $subTradeFields,
            'preTradeFields'    => $preTradeFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 主订单列表信息，以及其子订单、预付订单信息
     *
     * @param  int      $userId
     * @param  array    $tradeFields
     * @param  array    $subTradeFields
     * @param  array    $preTradeFields
     * @param  int      $page
     * @param  int      $pageSize
     * @param  int      $orderType
     * @param  array    $options
     * @param  array    $orderChannel
     * @return array
     */
    public static function getListByUserId($userId, $tradeFields=array(), $subTradeFields=array(), $preTradeFields=array(), $page=1, $pageSize=10, $orderType = -1, $options=array(), $orderChannel=[0]) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getListByUserId');
        $arrParams = array(
            'userId'            => $userId,
            'orderChannel'      => $orderChannel,
            'tradeFields'       => $tradeFields,
            'subTradeFields'    => $subTradeFields,
            'preTradeFields'    => $preTradeFields,
            'page'              => $page,
            'pageSize'          => $pageSize,
            'orderType'         => $orderType,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 主订单列表信息，以及其子订单、预付订单信息
     *
     * 支持返回转出课 , 是接口 getListByUserId 的升级版本
     *
     * @param  int      $userId
     * @param  array    $tradeFields
     * @param  array    $subTradeFields
     * @param  array    $preTradeFields
     * @param  int      $page
     * @param  int      $pageSize
     * @param  array    $changeType
     * @param  int      $orderType
     * @param  array    $options
     * @param  array    $orderChannel
     * @return array
     */
    public static function getFullListByUserId($userId, $tradeFields=array(), $subTradeFields=array(), $preTradeFields=array(), $page=1, $pageSize=10, $changeType=[0,1,2], $orderType = -1, $orderChannel=[0], $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getFullListByUserId');
        $arrParams = array(
            'userId'            => $userId,
            'orderChannel'      => $orderChannel,
            'tradeFields'       => $tradeFields,
            'subTradeFields'    => $subTradeFields,
            'preTradeFields'    => $preTradeFields,
            'page'              => $page,
            'pageSize'          => $pageSize,
            'orderType'         => $orderType,
            'changeType'        => $changeType,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 预付主订单列表信息，以及其预付订单信息
     *
     * @param  int      $userId
     * @param  int      $status
     * @param  array    $tradeFields
     * @param  array    $preTradeFields
     * @param  int      $page
     * @param  int      $pageSize
     * @param  array    $options
     * @return array
     */
    public static function getPreListByUserId($userId, $status = -1, $tradeFields=array(), $preTradeFields=array(), $page=1, $pageSize=10,  $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getListByUserId');
        $arrParams = array(
            'userId'            => $userId,
            'tradeFields'       => $tradeFields,
            'preTradeFields'    => $preTradeFields,
            'orderType'         => Zb_Const_Trade::ORDER_TYPE_PRE,
            'status'            => $status,
            'page'              => $page,
            'pageSize'          => $pageSize,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 关闭订单,将订单及其子订单或预约子订单状态置为已关闭
     * @param int $userId
     * @param int $tradeId
     * @param int $closeType
     * @param string $reason
     * @param array $options
     * @return array
     */
    public static function closeOrder($userId, $tradeId, $closeType, $reason='订单超时关闭',$options=array()) {
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'closeOrder');
        $arrParams = array(
            'userId' => $userId,
            'tradeId' => $tradeId,
            'closeType' => $closeType,
            'reason' => $reason,
        );
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * trade 支付回调数据接口 --仅trade可调用
     * @param $orderId
     * @param $userId
     * @param $justTrade 0 1 开关获取trade接口
     * @param array $options
     * @return array
     */
    public static function queryTradeForTrade($userId, $orderId, $justTrade = 0, $options=array())
    {
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'queryTradeForTrade');
        $arrParams = array(
            'orderId' => $orderId,
            'userId'  => $userId,
            'justTrade' => $justTrade,
        );

        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 获取指定订单对应的列表信息
     * @param int $userId 用户ID
     * @param int $tradeId 订单ID
     * @return array
     */
    public static function getListingInfo($userId, $tradeId) {
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getListingInfo');
        $arrParams = array(
            "userId" =>$userId,
            "tradeId" => $tradeId
        );
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 查询与指定tccId相绑定的主订单Id列表
     * @param $uid
     * @param $tccId
     * @return array
     */
    public static function getAssociatedTradeIds($uid, $tccId) {
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getAssociatedTradeIds');
        $arrParams = array(
            "userId" => $uid,
            "tccId" => $tccId
        );
        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);

    }

    /**
     * 操作代扣
     * 代扣成功状态从6->1 ,代扣不成功不变，如果手动支付变为1，否则过期自动取消，变为7
     * @param $userId
     * @param $tradeId
     * @param $holdingStatus
     * @return array
     */
    public static function modifyTradeHolding($userId, $tradeId, $holdingStatus) {
        $aHeader        = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'modifyTradeHolding');
        $aParameter     = array(
            'userId'        => $userId,
            'tradeId'       => $tradeId,
            'holdingStatus' => $holdingStatus
        );
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $aParameter, $aHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }

    /**
     * 取消代扣
     * @param $userId
     * @param $tradeId
     * @return array
     */
    public static function cancelHolding($userId, $tradeId) {
        $aHeader        = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'cancelHolding');
        $aParameter     = array(
            'userId'    => $userId,
            'tradeId'   => $tradeId,
        );
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $aParameter, $aHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }

    /**
     * 根据给定订单信息获取订单链路上最原始订单相关信息
     * @param $userId
     * @param $tradeId
     * @param $subTradeId
     * @return array
     */
    public static function getOriginTradeInfo($userId, $tradeId, $subTradeId) {
        $aHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getOriginTradeInfo');
        $aParameter = [
            'userId' => $userId,
            'tradeId' => $tradeId,
            'subTradeId' => $subTradeId,
        ];
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $aParameter, $aHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }

    /**
     * 获取用户指定条件过滤后的地址后置待填地址订单ID列表
     * @param $userId
     * @param array $status
     * @param array $reviewStatus
     * @param int $startTime
     * @param int $endTime
     * @param array $ext
     * @return array
     */
    public static function getAddressDeficiencyInfo($userId, $status=[1,21], $reviewStatus=[0,1,2],
                                        $startTime=946684800, $endTime=4102444800, $ext=[]) {
        $aHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getAddressDeficiencyInfo');
        $aParameter = [
            "userId" => $userId,
            "status" => $status,
            "reviewStatus" => $reviewStatus,
            "startTime" => $startTime,
            "endTime" => $endTime,
            "ext" => $ext
        ];
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $aParameter, $aHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }

    /**
     * 供团系统机型团信息同步使用，其他方向不可使用
     * @param $userId
     * @param $tradeId
     * @param array $syncInfo | 待同步信息，需包含 isNewGroupon,tuanMemberId,teamType,tuanId等字段
     * @return array
     */
    public function syncTuanInfo($userId, $tradeId, $syncInfo=[]) {
        $aHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'syncTuanInfo');
        $aParameter = [
            "userId" => $userId,
            "tradeId" => $tradeId,
            "info" => $syncInfo,
        ];
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $aParameter, $aHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $aParameter, $aHeader);
    }
}