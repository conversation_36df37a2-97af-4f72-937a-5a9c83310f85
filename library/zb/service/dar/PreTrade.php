<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   PreTrade.php
 * <AUTHOR>
 * @date   2018/9/11 上午11:45
 * @brief
 **/

class Zb_Service_Dar_PreTrade {

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'preTrade';

    /**
     * 学生的课程预付购买信息
     *
     * @param array     $studentCourseIds   学生课程ID映射数组，array([studentUid]_[courseUid], ...)。eg. ["2183318929_89864", ...]
     * @param array     $preTradeFields     接口返回的preTrade字段
     * @param array     $options            扩展数据, 控制
     * @return array
     */
    public static function getKVByCourseIds($studentCourseIds, $preTradeFields, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByCourseIds');
        $arrParams = array(
            'studentCourseIds'  => $studentCourseIds,
            'fields'            => $preTradeFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 批量获取单品子订单详情接口
     *
     * @param $userSkuIds
     * @param array $preTradeFields
     * @param array $options
     * @return array
     */
    public static function getKVBySkuIds($userSkuIds, $preTradeFields=array(), $options=array(), $arrStatus=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVBySkuIds');
        $arrParams = array(
            'userSkuIds'        => $userSkuIds,
            'preTradeFields'    => $preTradeFields,
            'arrStatus'         => $arrStatus,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据用户uid和预付子订单id查询预付子订单信息
     * @param $userId
     * @param $preTradeId
     * @param $preTradeFields
     * @param array $options
     * @return array
     */
    public static function getKVByPreTradeId($userId, $preTradeId, $preTradeFields, $options=[]) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByPreTradeId');
        $arrParams = array(
            'userId'    => $userId,
            'preTradeId'=> $preTradeId,
            'fields'    => $preTradeFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 获取预付子订单列表
     *
     * @param  int      $userId
     * @param  int      $status  默认-1 全部； 1：已支付   2： 已退款
     * @param  array    $preTradeFields
     * @param  int      $page
     * @param  int      $pageSize
     * @param  array    $options
     * @return array
     */
    public static function getListByUserId($userId, $status = -1, $preTradeFields, $page=1, $pageSize=10, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getListByUserId');
        $useCache = isset($options['useCache'])? $options['useCache'] : true;
        $arrParams = array(
            'userId'            => $userId,
            'status'            => $status,
            'preTradeFields'   => $preTradeFields,
            'page'              => $page,
            'pageSize'          => $pageSize,
            'useCache'          => $useCache,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 获取课程购买状态
     *
     * @param  int      $userId
     * @param  array    $courseIds
     * @param  array    $options
     * @return array    返回购买状态列表，未购买过 0， 已购买过 1
     */
    public static function getStatusKVByCourseIds($userId, $courseIds, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getStatusKVByCourseIds');
        $arrParams = array(
            'userId'            => $userId,
            'courseIds'         => $courseIds,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

}
