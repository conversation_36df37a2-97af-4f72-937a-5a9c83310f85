<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Commit.php
 * <AUTHOR>
 * @date   2018/7/30 上午10:56
 * @brief
 **/

class Zb_Service_Dar_Commit {

    const CREATE_TRADE_CMD      = '170020';
    const PAY_SUCCESS_CMD       = '170021';
    const CHANGE_COURSE_CMD     = '170022';
    const REFUND_START_CMD      = '170023';
    const REFUND_COMPLETE_CMD   = '170024';
	const PAY_WITHHOLD_CMD      = '170025';
    const UPDATE_ADDRESS_CMD    = '170026';
    const COPY_ADD_SUBTRADE_CMD = '170029';
    const UPDATE_COUPON_CMD     = '170030';
    const UPDATE_BATCH_SUBTRADE_CMD = '170031';
    const UPDATE_REMIND_TIME_CMD  = '170034';
    const CHANGE_CMD            = '170051';
    const REFUND_OP_CMD         = 'opRefund';
    const OFC_CMD               = 'ofc';
    const REVIEW_CMD            = 'review';
    const RESEND_TRADE_CMD      = 'resendTradeAdd';

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';


    /**
     * 创建订单
     *
     * @param  array    $arrInput
     * @param  array    $options
     * @return array
     */
    public static function createTrade($arrInput, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::CREATE_TRADE_CMD, 'createTrade');
        $arrParams = array(
            'arrInput'  => $arrInput,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 创建订单
     *
     * @param  array    $arrInput
     * @param  array    $options
     * @return array
     */
    public static function initTrade($arrInput, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::CREATE_TRADE_CMD, 'initTrade');
        $arrParams = array(
            'arrInput'  => $arrInput,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 创建订单
     *
     * @param  array    $arrInput
     * @param  array    $options
     * @return array
     */
    public static function syncPointMallOrder($arrInput, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::CREATE_TRADE_CMD, 'syncOrder');
        $arrParams = array(
            'arrInput'  => $arrInput,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }



    /**
     * 支付成功
     *
     * @param  int      $userId
     * @param  int      $orderId
     * @param  int      $paymentDiscount
     * @param  array    $options
     * @return array
     */
    public static function paySuccess($userId, $orderId, $paymentDiscount, $payTime, $payChannel, $tccId=0, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::PAY_SUCCESS_CMD, 'paySuccess');
        $arrParams = array(
            'orderId'           => $orderId,
            'userId'            => $userId,
            'paymentDiscount'   => $paymentDiscount,
            'payTime'           => $payTime,
            'payChannel'        => $payChannel,
			'tccId'				=> $tccId,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * @param       $userId
     * @param       $orderId
     * @param       $paymentDiscount
     * @param       $payTime
     * @param       $payChannel
     * @param       $withholdStatus
     * @param array $options
     * @return array
     */
    public static function payWithholdSuccess($userId, $orderId, $paymentDiscount, $payTime, $payChannel, $withholdStatus, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::PAY_WITHHOLD_CMD, 'paySuccess');
        $arrParams = array(
            'orderId'           => $orderId,
            'userId'            => $userId,
            'paymentDiscount'   => $paymentDiscount,
            'payTime'           => $payTime,
            'payChannel'        => $payChannel,
            'withholdStatus'    => $withholdStatus,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 调课接口
     *
     * @param  int      $userId
     * @param  int      $subTradeId
     * @param  array    $newCourseInfo
     * @param  int      $refundFee
     * @param  array    $refundInfo
     * @param  array    $changeInfo
     * @param  array    $subTradeFields
     * @param  array    $options
     * @return array
     */
    public static function changeCourse($userId, $subTradeId, $newCourseInfo, $refundFee, $refundInfo, $changeInfo, $subTradeFields=array(), $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::CHANGE_COURSE_CMD, 'changeCourse');
        $arrParams = array(
            'userId'            => $userId,
            'subTradeId'        => $subTradeId,
            'newCourseInfo'     => $newCourseInfo,
            'refundFee'         => $refundFee,
            'refundInfo'        => $refundInfo,
            'changeInfo'        => $changeInfo,
            'subTradeFields'    => $subTradeFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    public static function initChangeCourse($changeCourseBizInfo, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::CHANGE_COURSE_CMD, 'initChangeCourse');

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $changeCourseBizInfo, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $changeCourseBizInfo, $arrHeader);
    }

    public static function commitChangeCourse($arrParams, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::CHANGE_COURSE_CMD, 'commitChangeCourse');

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    public static function commitHxChangeCourse($arrParams, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::CHANGE_COURSE_CMD, 'commitHxChangeCourse');

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 发起退款接口
     *
     * @param  array    $refundBasic     : 基础信息（userId、refundOpUid、refundComment、refundSource等）
     * @param  array    $refundSubTrade  : 退款子订单集合（[subTradeId => array(tradeType)]）
     * @param  array    $refundFeeDetail : 退款金额明细（每个子订单的明细）
     * @param  array    $refundPayDetail : 退款流水明细（每个子订单的明细）
     * @param  array    $depositPayDetail: 抵押拟退流水明细（subTradeId + 赠品subTradeId作为key）
     * @param  array    $options
     * @return array
     */
    public static function startRefund($refundBasic, $refundSubTrade, $refundFeeDetail, $refundPayDetail, $depositPayDetail, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::REFUND_START_CMD, 'startRefund');
        $arrParams = array(
            'refundBasic'       => $refundBasic,
            'refundSubTrade'    => $refundSubTrade,
            'refundFeeDetail'   => $refundFeeDetail,
            'refundPayDetail'   => $refundPayDetail,
            'depositPayDetail'  => $depositPayDetail,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 发起退款接口V2
     */
    public static function startRefundV2($refundDetail, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::REFUND_START_CMD, 'startRefundV2');
        $arrParams = $refundDetail;

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 浣熊同步退款接口
     * @param $refundDetail
     * @param array $options
     * @return array
     */
    public static function startRefundV3HX($refundDetail, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::REFUND_START_CMD, 'startRefundV3HX');
        $arrParams = $refundDetail;

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 完成退款接口
     *
     * @param  int      $userId
     * @param  int      $tradeId
     * @param  int      $tradeType
     * @param  array    $options
     * @return array
     */
    public static function refundComplete($tradeId, $userId, $batchId, $subTradeIdList, $refundChannel, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::REFUND_COMPLETE_CMD, 'refundComplete');
        $arrParams = array(
            'tradeId'           => $tradeId,
            'userId'            => $userId,
            'batchId'           => $batchId,
            'subTradeIdList'    => $subTradeIdList,
			'refundChannel'		=> $refundChannel,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 回填退款物流单号接口
     *
     * @param  int      $refundId
     * @param  int      $expressCode
     * @param  array    $options
     * @return array
     */
    public static function setRefundExpressCode($refundId, $expressCode, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::REFUND_COMPLETE_CMD, 'setRefundExpressCode');
        $arrParams = array(
            'refundId'          => $refundId,
            'expressCode'       => $expressCode,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 更新寄送地址接口
     *
     * @param  int      $userId
     * @param  int      $tradeId
     * @param  array    $addressInfo
     * @param  array    $subTradeIds
     * @param  array    $options
     * @return array
     */
    public static function updateAddressInfo($userId, $tradeId, $addressInfo, $subTradeIds=array(), $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::UPDATE_ADDRESS_CMD, 'updateAddressInfo');
        $arrParams = array(
            'userId'            => $userId,
            'tradeId'           => $tradeId,
            'addressInfo'       => $addressInfo,
            'subTradeIds'       => $subTradeIds,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    public static function addAddress($userId, $tccId, $addressInfo) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::UPDATE_ADDRESS_CMD, 'addAddressInfo');
        $arrParams = array(
            'userId'            => $userId,
            'tccId'           => $tccId,
            'addressInfo'       => $addressInfo,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 复制新增子订单接口
     * @biref   根据目标子订单信息复制并生成新的子订单，可自定义变更新子订单信息
     * @param   int     userId          用户ID
     * @param   int     subTradeId      目标子订单ID
     * @param   int     isFix           是否为修复，默认为0
     * @param   array   updateInfo      更新数据
     * @param   int     operId          操作人ID
     * @param   string  reason          原因
     * @return  array   ret             操作结果
     */
    public static function copyAddSubTrade($userId, $subTradeId, $isFix=0, $updateInfo=[], $operId=0, $reason=''){
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::COPY_ADD_SUBTRADE_CMD, 'copyAddSubTrade');
        $arrParams = array(
            'userId' => $userId,
            'subTradeId' => $subTradeId,
//            'isFix' => $isFix,//暂时注释，不传递
        );
        if($updateInfo){
            $arrParams['updateInfo'] = json_encode($updateInfo, JSON_UNESCAPED_UNICODE);
        }
        if($operId){
            $arrParams['operId'] = $operId;
        }
        if($reason){
            $arrParams['reason'] = $reason;
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    
    /**
     * @biref   【原单补寄】，新售后类型【不退货补寄】，新建一个订单和商品行，并对原单进行关联。只有2.x的过度阶段会用到。主要复制copyAddSubTrade的逻辑
     *           别的地方不要引用！！！！！
     * added by sunxiancan
     * @param   int     userId          用户ID
     * @param   int     subTradeId      目标子订单ID
     * @param   int     isFix           是否为修复，默认为0
     * @param   array   updateInfo      更新数据
     * @param   int     operId          操作人ID
     * @param   string  reason          原因
     * @return  array   ret             操作结果
     */
    public static function resendTradeAdd($userId, $subTradeId, $isFix=0, $updateInfo=[], $operId=0, $reason=''){
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::RESEND_TRADE_CMD, 'addTrade');
        $arrParams = array(
            'userId' => $userId,
            'subTradeId' => $subTradeId,
            //            'isFix' => $isFix,//暂时注释，不传递
        );
        if($updateInfo){
            $arrParams['updateInfo'] = json_encode($updateInfo, JSON_UNESCAPED_UNICODE);
        }
        if($operId){
            $arrParams['operId'] = $operId;
        }
        if($reason){
            $arrParams['reason'] = $reason;
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    
    /**
     * @brief   更新优惠券信息
     * @param   int     $userId
     * @param   int     $tradeId
     * @param   array   $couponIds
     * @param   array   $couponPlatformIds
     * @param   array   $options
     * @return  array
     */
    public static function updateCouponInfo($userId, $tradeId, $couponIds=[], $couponPlatformIds=[], $options=[]){
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::UPDATE_COUPON_CMD, 'updateCouponInfo');
        $arrParams = array(
            'userId'            => $userId,
            'tradeId'           => $tradeId,
            'couponIds'         => $couponIds,
            'couponPlatformIds' => $couponPlatformIds,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    /**
     * 更新子订单批次号
     * @biref   存储子订单的物流下发批次号
     * @param   int     userId          用户ID
     * @param   int     subTradeId      子订单ID
     * @param   string  batchId         批次号
     * @return  array   ret             更新结果
     */
    public static function updateBatchId($userId, $subTradeId, $batchId){
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::UPDATE_BATCH_SUBTRADE_CMD, 'updateBatchId');
        $arrParams = array(
            'userId' => $userId,
            'subTradeId' => $subTradeId,
            'batchId' => $batchId,
        );
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    public static function updateExpressRemindTime($userId, $subTradeId, $remindTime){
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::UPDATE_REMIND_TIME_CMD, 'updateRemindTime');
        $arrParams = array(
            'userId' => $userId,
            'subTradeId' => $subTradeId,
            'remindTime' => $remindTime,
        );
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 转约接口
     *
     * @param $userId 用户编号
     * @param $tradeId 转出父订单编号
     * @param $newTradeId 转入父订单编号
     * @param $tradeFee 转入父订单尾款
     * @param $groupInfo 联保策略
     * @param $giftBizList 买就送策略
     * @param $deductInfo 抵扣信息
     * @param $skuRefundAndChangeList 转退列表
     * @param $subOrderGroup 订单绑定关系
     * @param $tradeIdList 联合订单关系
     * @param $subTradeList 转入子订单明细
     * @param $changeOutList 转出子订单信息
     * @param $changeCourseSummary 转班幂等
     * @param $changeCourseDetail 转班明细
     * @return array
     */
    public static function change($userId, $tradeId, $newTradeId, $tradeFee, $groupInfo, $giftBizList, $deductInfo,
                                  $skuRefundAndChangeList, $subOrderGroup, $tradeIdList, $subTradeList, $changeOutList, $changeCourseSummary, $changeCourseDetail){
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::CHANGE_CMD, 'change');
        $arrParams = array(
            'userId' =>$userId,
            'tradeId' => $tradeId,
            'newTradeId' => $newTradeId,
            'tradeFee' => $tradeFee,
            'groupInfo' => $groupInfo,
            'giftBizList' => $giftBizList,
            'deductInfo' => $deductInfo,
            'skuRefundAndChangeList' => $skuRefundAndChangeList,
            'subOrderGroup' => $subOrderGroup,
            'tradeIdList' => $tradeIdList,
            'subTradeList' => $subTradeList,
            'changeOutList' => $changeOutList,
            'changeCourseSummary' => $changeCourseSummary,
            'changeCourseDetail' => $changeCourseDetail,
        );
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 修改订单履约信息
     *
     * @param $userId 用户编号
     * @param $subTradeId 子订单编号
     * @param $ofcRet 履约结果信息
     * @param array $ofcInfo 履约信息 TODO:本期不支持
     * @return array
     */
    public static function updateOfcRet($userId, $subTradeId, $ofcRet, $ofcInfo=[]){
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::OFC_CMD, 'updateOfcRet');
        $arrParams = array(
            'userId' =>$userId,
            'subTradeId' => $subTradeId,
            'ofcRet' => $ofcRet,
//          'ofcInfo' => $ofcInfo,
        );
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 批量更新履约结果  2020-03-21
     * @param $userId
     * @param $ofcRetBatch  array(subTradeId=>ofcRet,)
     * @return array
     */
    public static function updateOfcRetBatch($userId, $ofcRetBatch){
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::OFC_CMD, 'updateOfcRetBatch');
        $arrParams = array(
            'userId' =>$userId,
            'ofcRetBatch' => $ofcRetBatch,
        );
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 修改审核状态
     *
     * @param $userId 用户编号
     * @param $tradeIds 订单编号集
     * @param int $status 订单状态
     * @param int $reviewStatus 审核状态
     * @param int $changeReviewStatus 变更审核状态
     * @return array
     */
    public static function updateReviewStatus($userId, $tradeIds, $status=Zb_Const_Trade::TRADE_STATUS_PAID, $reviewStatus=Zb_Const_Trade::REVIEW_STATUS_WAIT, $changeReviewStatus=Zb_Const_Trade::REVIEW_STATUS_PASS){
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::REVIEW_CMD, 'updateReviewStatus');
        $arrParams = array(
            'userId' =>$userId,
            'tradeIds' => $tradeIds,
            'status' => $status,
            'reviewStatus' => $reviewStatus,
            'changeReviewStatus' => $changeReviewStatus,
        );
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 修改订单履约信息
     *
     * @param $userId 用户编号
     * @param $subTradeId 子订单编号
     * @param $ofcData 履约信息 , key => value
     * @return array
     */
    public static function updateOfcPrivilegeId($userId, $subTradeId, $privilegeId){
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::OFC_CMD, 'updateOfcPrivilegeId');
        $arrParams = array(
            'userId' =>$userId,
            'subTradeId' => $subTradeId,
            'privilegeId' => $privilegeId,
        );
        if(!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
