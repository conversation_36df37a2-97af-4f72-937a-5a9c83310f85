<?php
/**
 * Created by PhpStorm.
 * User: wangfeng<PERSON>@zuoyebang.com
 * Date: 2018/7/24
 * Time: 上午10:28
 * Desc: 主讲章节SDK
 */

class Zb_Service_Dat_TeacherLesson
{
    private static $service     = 'zbcore_dat';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dat';
    private static $entity      = 'teacherLesson';


    /**
     * 课程主讲UID 接口
     *
     * @param array     $lessonIds      待查询课程ID json list [123, 234, 345]
     * @param array     $fields   接口返回的Course字段
     * @param array     $options        扩展数据
     * @return array
     */
    public static function getTeacherUidByLessonIdArr(array $lessonIds, array $fields, array $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getTeacherUidByLessonIdArr');
        $arrParams = array(
            'lessonIds'     => $lessonIds,
            'fields'        => $fields,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据主讲id 获取章节列表并筛选
     * @param $teacherUid
     * @param $fields
     * @param int $offset
     * @param int $limit
     * @param array $timeRange
     * @param int $status
     * @param array $options
     * @param array $timeRange
     * @return array
     */
    public static function getLessonInfoByTeacherUid($teacherUid, $fields,  $offset = 0, $limit = 20, $timeRange = [], $status = -1,  $options = array(), $useCache = 1) {

        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getLessonInfoByTeacherUid');
        $arrParams = array(
            'teacherUid'     => $teacherUid,
            'arrFields'      => $fields,
            'offset'         => $offset,
            'limit'          => $limit,
            'timeRange'      => $timeRange,
            'status'         => $status,
            'useCache'       => $useCache,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}