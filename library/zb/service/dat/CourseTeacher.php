<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   CourseTeacher.php
 * <AUTHOR>
 * @date   2018/7/11 上午11:46
 * @brief
 **/


class Zb_Service_Dat_CourseTeacher {

    private static $service     = 'zbcore_dat';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dat';
    private static $entity      = 'courseTeacher';


    /**
     * 课程主讲UID 接口
     *
     * @param array     $courseIds      待查询课程ID json list [123, 234, 345]
     * @param array     $fields   接口返回的Course字段
     * @param array     $options        扩展数据
     * @return array
     */
    public static function getKVByCourseId(array $courseIds, array $fields, array $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKV');
        $arrParams = array(
            'courseIds'     => $courseIds,
            'fields'        => $fields,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * @param $teacherUid
     * @param $fields
     * @param int $offset
     * @param int $limit
     * @param array $timeRange [0:起时间戳 1:止时间戳]
     * @param array $options
     * @return array
     */
    public static function getListByTeacherUid($teacherUid, $fields, $offset = 0, $limit = 20, $timeRange = [],$options=[] ) {

        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByTeacherUid');
        $arrParams = array(
            'teacherUid'    => $teacherUid,
            'fields'        => $fields,
            'offset'        => $offset,
            'limit'         => $limit,
            'timeRange'     => $timeRange,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);

    }

}