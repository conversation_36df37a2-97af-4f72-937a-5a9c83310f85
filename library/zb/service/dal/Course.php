<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Course.php
 * <AUTHOR>
 * @date   2018/7/2 下午10:06
 * @brief
 *
 **/


class Zb_Service_Dal_Course {

    const STATUS_UNFINISH = 0; //未结束
    const STATUS_FINISHED = 1; //已结束（正常结束）
    const STATUS_DELETED  = 2; //已删除（异常结束）

    private static $service     = 'zbcore_dal';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dal';
    private static $entity      = 'course';


    /**
     * 课程详情接口
     *
     * @param array     $courseIds      待查询课程ID json list [123, 234, 345]
     * @param array     $courseFields   接口返回的Course字段
     * @param array     $lessonFields   接口返回的Lesson字段
     * @param array     $options        扩展数据
     * @param array     $materialFields 接口返回的Material字段
     * @return array
     */
    public static function getKVByCourseId(array $courseIds, array $courseFields, array $lessonFields=array(), array $options=array(), array $materialFields=array(), $noUseCache=0) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKV');
        $arrParams = array(
            'courseIds'       => $courseIds,
            'courseFields'    => $courseFields,
            'lessonFields'    => $lessonFields,
            'materialFields'  => $materialFields,
            'noUseCache'      => $noUseCache,
        );

        if (!empty($options['allFields'])) {
            $arrParams['allFields'] = 1;
        }

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
    }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    public static function getCourseListByConds(array $arrConds, array $arrFields, $offset, $limit,$optins =array()){
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getCourse');
        $arrParams = array(
            'arrConds'   => $arrConds,
            'arrFields'  => $arrFields,
            'offset'     => $offset,
            'limit'      => $limit,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

}
