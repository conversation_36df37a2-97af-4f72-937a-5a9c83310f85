<?php

/**
 * @file   Outline.php
 * <AUTHOR>
 * @date   2019/3/11
 * @brief
 */

class Zb_Service_Dal_Outline {

    private static $service     = 'zbcore_dal';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dal';
    private static $entity      = 'outline';

    public static function getKVByOutlineId($outlineIds, $outlineFields, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKV');
        $arrParams = array(
            'outlineIds' => $outlineIds,
            'outlineFields' => $outlineFields,
        );

        if (!empty($options['allFields'])) {
            $arrParams['allFields'] = 1;
        }

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}

