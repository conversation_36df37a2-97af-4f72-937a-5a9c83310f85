<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2019/6/24
 * Time: 11:58
 */


class Zb_Service_Dal_GetCourseListByConds {

    const STATUS_UNFINISH = 0; //δ����
    const STATUS_FINISHED = 1; //�ѽ���������������
    const STATUS_DELETED  = 2; //��ɾ�����쳣������

    private static $service     = 'zbcore_dal';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dal';
    private static $entity      = 'getCourseListByConds';


    public static function getCourseListByConds(array $arrConds, array $arrFields, $offset, $limit,$optins =array()){
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKV');
        $arrParams = array(
            'arrConds'   => $arrConds,
            'arrFields'  => $arrFields,
            'offset'     => $offset,
            'limit'      => $limit,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

}
