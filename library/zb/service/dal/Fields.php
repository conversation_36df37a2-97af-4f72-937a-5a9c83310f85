<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Fields.php
 * <AUTHOR>
 * @date   2018/7/3 下午9:55
 * @brief
 **/



class Zb_Service_Dal_Fields {

    /**
     * @var array 接口可以提供的课程数据字段
     */
    private static $courseFields   = array(
        "courseId",             // 课程ID
        "courseName",           // 课程名称
        "grades",               // 年级
        "grade",                // 年级（单值）
        "subjects",             // 学科
        "courseType",           // 课程类型
        "newCourseType",        // 新课程类型
        "content",              // 课程详情
        "learnSeason",          // 学季ID
        "season",               //学季不带期数
        "numberPeriods",        //期数
        "year",                 // 学年
        "firstLessonTime",      // 第一个章节开课时间
        "lastLessonStopTime",   // 最后一个章节结束时间
        "status",               // 状态
        "isInner",              // 是否内部课
        "startTime",            // 开始时间
        "stopTime",             // 结束时间
        "allStartTime",         // 所有章节开始时间
        "allStopTime",          // 所有章节结束时间
        "createTime",           // 创建时间
        "curLessonId",          // 当前上课章节
        "onlineFormatTime",     // 上课时间格式化
        "onlineFormatTimeAll",  // 上课时间格式化，展示频率最高的时间
        "lessonCnt",            // 课程章节数
        "lessonCompleteCnt",    // 已上章节数
        "coreLessonCnt",        // 核心章节数
        "coreLessonCompleteCnt",// 已上核心章节数
        "hasMaterial",          // 是否有教材
        "mainGradeId",          // 主年级ID
        "mainSubjectId",        // 主年级ID
        "vipClass",             // VIP课程
        'finishTime',           // 结束时间
        'lectureSendTime',      // 教材发送时间
        'formatShow',           // content展示方式是html还是格式化信息
        'deleteTime',           // 删除时间
        'deleteReason',         // 删除原因
        "specialSellType",      //特惠课标记
        "playType",             // 0 普通课 1 是demo课
        "tags",                 // 课程服务文案
        "resource",             // 课程资源
        "services",             // 课程服务ID
        "isClosing",            // 0 非预关班状态，1 预关班状态
        "createTime",           // 记录创建时间
        "updateTime",           // 记录更新时间
        "cpuId",                // 课程产品ID
        "extFlag",              // 扩展开关
        "teacherUids",          // 主讲老师ID
        "lessonStartTime",      // 章节开始时间
        "lessonStopTime",       // 章节结束时间
        "lessonCycle",          // 章节周期
        "applicableGradeDesc",  // 适用年级描述
        "applicableGrades",     // 适用年级
        "isQq",                 // 是否是qq
        "isWeixin",             // 是否是微信
        "weixin",                // 微信信息
        "serviceInfo",          // ares 服务信息
        "brandId",              // 品牌ID
        "hasSCWXGroupService",  // 是否有学习顾问微信群
        "shortTraining",        // 是否是短训班
        "suggestedGradeIds",    // 推荐年级
        "suggestedSubjectIds",  // 推荐学科
        "innovation",           // 创新课标签
        "system",               // 体系
        "classType",            // 班型
        "module",               // 模块
        "bookVer",              // 版本
        "courseTags",           // 课程标签
        "source",               //课程业务线
        "level",                // 等级
        "packageLength",        //课包长度
        "themes",               ///课程主题
        "unLockTime",           //解锁时间
        "t007Tag",
        "directoryTree",
        "playForm",
        "publishNumber",
        "publishOrganization",
        "publishCopyright",
        "difficulty",//难度
        "extFields",    // 配置化扩展字段
        "skuId",
        "spuId",
        "onSaleJwCourseList", // 教委上架课程列表
    );

    /**
     * @var array 接口可以提供的章节数据字段
     */
    private static $lessonFields    = array(
        "lessonId",             // 章节ID
        "courseId",             // 课程ID，数组
        "lessonName",           // 章节名称
        "status",               // 章节状态
        "startTime",            // 开始时间
        "stopTime",             // 结束时间
        "lessonType",           // 章节类型
        "finishTime",           // 老师关播时间
        "outlineId",            // 大纲ID
        // 以下来自ext_data
        "playType",             // 0 普通课 1 是demo课
        "relatedLesson",        // 原始章节信息
        "attached",             // 学生资料
        "previewNoteUri",       // 课前预习
        "classNoteUri",         // 课堂笔记
        "hasHomework",          // 是否有作业
        "hasPlayback",          // 是否有回放
        "reopenLessonId",       // 重开章节
        "stageTest",            // 阶段测试
        "finishNum",            // 完课学生数量
        "attendNum",            // 出勤学生数量
        "attendLongNum",        // 到课30分钟学生数量
        "registerNum",          // 报名学生数量
        "deleteTime",           // 删除时间
        "deleteReason",         // 删除原因
        "isClassing",           // 上课中 @todo 2018-11-24 添加，DAL侧未上线
        "coverPics",            // 封面信息
        "createTime",           // 记录创建时间
        "updateTime",           // 记录更新时间
        "learnTarget",          // 学习目标
        "playback",             // 回放信息
        "extFlag",              // 扩展开关
        "secondOutline",        // 二级大纲
        "fileList",             // pdf列表
        "serviceInfo",          // ares 服务信息
        "subjectId",            // 学科，仅针对短训班课程
        "source",
        "hasShare",
        "shareIdList",
        "indexLessonId",
        "shareGroupId",
        "visitType",            //章节旁听
        "observation",
        "unLockTime",           //解锁时间
        "t007Tag",
        "subLessonList",
        "t007LessonId",
        "beginRecordTime",
        "banxueInfo",            //伴学3.0信息
        "extFields",    // 配置化扩展字段
    );

    private static $cpuFields = array(
        'cpuId',
        'cpuType',
        'newCourseType',
        'cpuName',
        'gradeId',
        'subjectId',
        'year',
        'learnSeason',
        'system',
        'classType',
        'bookVer',
        'module',
        'status',
        'extData',
        'isInner',
        'serviceIds',
        'courseIds',
        'materialIds',
        "serviceInfo",          // ares 服务信息
        'brandId',
        'shortTraining',        // 是否是短训班
        "source",               //课程业务线
        "packageLength",        //课包长度
        "playForm",
        "extFields",    // 配置化扩展字段
    );

    private static $outlineFields = array(
        'outlineId',
        'outlineType',
        'outlineName',
        'cpuId',
        'seqNum',
        'resource',
        'serviceIds',
        'isInner',
        'extData',
        'lessonIds',
        'examServiceStatus',
        'hasCourseware',
        "serviceInfo",          // ares 服务信息
        "source",
        "subOutlineList",
        "playType",
        "beforeRecord",
        "extFields",    // 配置化扩展字段
    );

    private static $materialFields = array(
        'materialId',
        'lessonIds',
    );

    private static function doCheck($arrInput, $allFields, $extFields = []) {
        if (!empty($extFields) && is_array($extFields)) {
            $allFields = array_merge($allFields, $extFields);
        }

        return array_diff($arrInput, $allFields);
    }

    public static function chkCourseFields($arrInput, $extFields = []) {
        return self::doCheck($arrInput, self::$courseFields, $extFields);
    }

    public static function chkLessonFields($arrInput, $extFields = []) {
        return self::doCheck($arrInput, self::$lessonFields, $extFields);
    }

    public static function chkCpuFields($arrInput, $extFields = []) {
        return self::doCheck($arrInput, self::$cpuFields, $extFields);
    }

    public static function chkOutlineFields($arrInput, $extFields = []) {
        return self::doCheck($arrInput, self::$outlineFields, $extFields);
    }

    public static function chkMaterialFields($arrInput, $extFields = []) {
        return self::doCheck($arrInput, self::$materialFields, $extFields);
    }

    public static function getCourseFields($extFields = []) {
        $allFields = self::$courseFields;
        if (!empty($extFields) && is_array($extFields)) {
            $allFields = array_merge($allFields, $extFields);
        }
        return $allFields;
    }

    public static function getLessonFields($extFields = []) {
        $allFields = self::$lessonFields;
        if (!empty($extFields) && is_array($extFields)) {
            $allFields = array_merge($allFields, $extFields);
        }
        return $allFields;
    }

    public static function getCpuFields($extFields = []) {
        $allFields = self::$cpuFields;
        if (!empty($extFields) && is_array($extFields)) {
            $allFields = array_merge($allFields, $extFields);
        }
        return $allFields;
    }

    public static function getOutlineFields($extFields = []) {
        $allFields = self::$outlineFields;
        if (!empty($extFields) && is_array($extFields)) {
            $allFields = array_merge($allFields, $extFields);
        }
        return $allFields;
    }

    public static function getMaterialFields($extFields = []) {
        $allFields = self::$materialFields;
        if (!empty($extFields) && is_array($extFields)) {
            $allFields = array_merge($allFields, $extFields);
        }
        return $allFields;
    }
}
