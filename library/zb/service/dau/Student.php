<?php

/**
 * @file   student.php
 * <AUTHOR>
 * @date   2018/7/3 下午15:10
 * @brief
 **/

class Zb_Service_Dau_Student {

    private static $service     = 'zbcore_dau';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dau';
    private static $entity      = 'student';

    /**
     * 学生UidKv接口
     * @param array     $studentUid 学生uid数组
     * @param array     $fields     获取字段
     * @param array     $options    额外选项 [isRalMulti]
     * @return array
     */
    public static function getKVByStudentUids($studentUid, $fields, $options = array()) {
        
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByStudentUids');
        $arrParams = array(
            'studentUid'  => $studentUid,
            'fields' => $fields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 电话kv学生信息
     * @param array    $phone    电话
     * @param array     $fields   所需字段
     * @return array
     */
    public static function getKVByPhones($phone, $fields, $options=array()) {

        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByPhones');
        $arrParams = array(
            'phone'  => $phone,
            'fields' => $fields,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据学生Uid更新学生Kv信息
     *
     * @param int       $studentUid   学生Uid
     * @param array     $fields       所需字段
     * @return array
     */
    public static function setKVByStudentUid($studentUid, $fields, $options=array()) {

    	$arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'setKVByStudentUid');
        $arrParams = array(
            'studentUid'  => $studentUid,
            'fields' => $fields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据学生Uid更新学生扩展字段信息
     *
     * @param int       $studentUid   学生Uid
     * @param array     $extFields    所需字段
     * @return array
     */
    public static function setExtByStudentUid($studentUid, $extFields, $options=array()) {

        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'setExtByStudentUid');
        $arrParams = array(
            'studentUid'  => $studentUid,
            'extFields' => $extFields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}