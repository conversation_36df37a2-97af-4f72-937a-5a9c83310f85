<?php
/**
 * Created by PhpStorm.
 * User: wangfeng<PERSON>@zuoyebang.com
 * Date: 2018/7/10
 * Time: 下午7:15
 */

class Zb_Service_Dau_Teacher
{
    private static $service     = 'zbcore_dau';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dau';
    private static $entity      = 'teacher';

    /**
     * 根据教师Uid获取教师信息
     * @param $teacherUid
     * @param $fields
     * @param array $options
     * @return array
     */
    public static function getKVByTeacherUids($teacherUid, $fields, $options = array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVByTeacherUids');
        $arrParams = array(
            'teacherUid'  => $teacherUid,
            'fields' => $fields,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据主讲名称 / 年级 / 学科 拉取主讲老师列表
     * @param array $teacherFields
     * @param string $teacherName
     * @param array $arrGrade
     * @param int   $subject
     * @param int   $offset
     * @param int   $limit
     */
    public static function getTeacherListByGradeAndSubject($teacherFields, $teacherName = '', $arrGrade = [], $subject = -1, $offset = 0, $limit = 20, $options = [])
    {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getTeacherListByGradeAndSubject');

        $arrParams = array(
            'arrFields'  => $teacherFields,
            'teacherName' => $teacherName,
            'arrGrade' => $arrGrade,
            'subject' => $subject,
            'offset' => $offset,
            'limit' => $limit,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}