<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


 /**
 * @file    Tcc.php
 * <AUTHOR>
 * @date    2019-08-02
 * @brief   Tcc通用化SDK
**/

class Zb_Service_Tcc {

    const TCC_GALAXY_SERVEICE_ID = 2040;
    const SERVICE_NAME  = "tcc";
	public static $rsn = 1000;
	private static $sType = 'mysql';
    //最近的错误信息
	private static $lastErrInfo = [];
	//本地降级方案
    private static $localLogPath = LOG_PATH . DIRECTORY_SEPARATOR . 'tcc';
    private static $localLogFilePrefix = 'tcc_local.log';
    private static $localLogFile = '';   //日志文件
    private static $localResourceData = []; //资源信息缓存
    private static $localLogSep = "\t";
    private static $localFailedRM = ''; //commit失败的rm,用于cancel操作
    private static $localStepConf = [
        'begin'  => 1,
        'regist' => 2,
        'commit' => 3,
        'cancel' => 4,
    ];

    /**
     * @comment 开启一个全局事务
     *
     * @param	string	$location		    资源定位符
     * @param	int		$excuteType		    事务执行方式 1-串行(默认) 2-并行	        
     * @param	int 	$timeout		    事务有效时间,默认60秒(1分钟)
     * 
     * @return  int
    **/
    public static function begin($location, $processType=1, $duration=60) {

		// 1. 参数判断
	    if (!self::_checkBeginParam($location, $processType, $duration)) {
            return false; 
        }

		// 2. 申请全局事务ID
		$xid = Zb_Service_Galaxy::singleId(self::TCC_GALAXY_SERVEICE_ID);
		if (intval($xid) == 0) {
			Bd_Log::warning("Error:[singleId error], Detail:[location:$location xid:$xid]");
			return false;
		}

		//2020-04-02 增加降级本地预案
        $sType = self::getSType();
		if($sType == 'local'){
		    if(!is_dir(self::$localLogPath)){
		        mkdir(self::$localLogPath);
            }
            self::$sType = $sType;
            self::$localResourceData = [];
            self::$localFailedRM = ''; //先清空，避免脚本连续调用读取错误
            self::$localLogFile = self::$localLogPath . DIRECTORY_SEPARATOR .
                self::$localLogFilePrefix . '.' . date('YmdH');
            $logInfo = [
                self::$localStepConf['begin'],
                $xid,
                $location,
                time(),
            ];
            $ret = self::writeLog($logInfo);
            if($ret === false){
                return false;
            }
		    return $xid;
        }

		// 3. 记录全局事务信息
        $serviceConfig = json_encode(array(
	        "name"		=>  Zb_Const_Tcc::$serviceConfig[$location]["name"],
		));

		$param = array(
			"xid"			=> $xid,
			"location"		=> $location,
			"processType"	=> $processType,
			"duration"		=> $duration,
		);

		$header = array(
            "pathinfo"      => "/tcs/begin",
        );

		$ret = self::invoke("tcc", $header, $param);
		if ($ret == false) {
			return false;
		}
		if($ret['data']['sType']){
		    self::$sType = $ret['data']['sType'];
        }
		return $ret['data']['xid'];
    }

    static private function _checkBeginParam ($location, $processType, $duration) {

        if (empty(Zb_Const_Tcc::$transactionManagerPackage[$location])) {
			Bd_Log::warning("Error:[location config empty], Detail:[location:$location]");
			return false;	
		}

        if (!in_array($processType , array(1, 2))) {
			Bd_Log::warning("Error:[location processType invalid], Detail:[location:$location]");
            return false; 
        }

        $serviceConfig = json_encode(array(
	        "name"		=>  Zb_Const_Tcc::$serviceConfig[$location]["name"],
		));

        if (strlen($serviceConfig) > 100) {
            Bd_Log::warning("Error:[location serviceConfig length greater than 100], Detail:[location:$location]");
            return false;  
        } 

        // 判断location package下资源
        foreach (Zb_Const_Tcc::$transactionManagerPackage[$location] as $am => $package) {
            foreach ($package as $step => $url ) {
                if (!empty($url) && strlen($url) > 200) {
                    Bd_Log::warning("Error:[$am $url length greater than 200], Detail:[location:$location]");
                    return false; 
                } 
            } 
        }

        return true;
    }

	/**
     * @comment 事务准备阶段
     *
	 * @param	int		$xid			全局事务ID
     * @param	string	$location		资源定位符	        
     * @param	array	$tryParam       try 参数
	 * @param	array	$commitParam    commit 参数
	 * @param	array	$cancelParam    cancel 参数
     *
     * return array
    **/
    public static function prepare($xid, $location, $tryParam, $commitParam, $cancelParam) {

		// 1. 参数验证
		$commitParam = json_encode($commitParam, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
		$cancelParam = json_encode($cancelParam, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

        if (!self::_checkPrepareParam($xid, $location, $commitParam, $cancelParam)) {
            return false; 
        }
        list($tm, $rm)  = explode('_', $location);
        $package	    = Zb_Const_Tcc::$transactionManagerPackage[$tm][$rm];
        if (self::$sType == 'local'){
            // 本地记录
            $commitUrl = $package['confirm']?$package['confirm']:"";
            $cancelUrl = $package['cancel']?$package['cancel']:"";
            $resourceData = [
                $tm, $rm, $commitUrl, $cancelUrl, $commitParam, $cancelParam
            ];
            self::$localResourceData[] = $resourceData;
            $logInfo = array_merge([
                self::$localStepConf['regist'],
                $xid,
            ], $resourceData);
            $ret = self::writeLog($logInfo);
            if($ret === false){
                return false;
            }
        }else{
            // 2. regist to TC Server
            $service	    = Zb_Const_Tcc::$serviceConfig[$rm]['name'];
            $registParam = array(
                "xid"			=> $xid,
                "location"		=> $rm,
                "rsn"			=> self::$rsn++,
                "commitUrl"		=> $package['confirm'],
                "cancelUrl"		=> $package['cancel'],
                "commitParam"	=> $commitParam,
                "cancelParam"	=> $cancelParam,
                "serviceConfig"	=> json_encode(array(
                    "name"		=>  $service,
                )),
                'sType'         => self::$sType,
            );

            $header = array("pathinfo"=>"/tcs/regist");

            $ret = self::invoke(self::SERVICE_NAME, $header, $registParam);
            if ($ret == false) {
                return false;
            }
        }
		// 3. 执行location的try方法
        if (empty($package["prepare"])) {
            return json_encode(array(
                "errNo" => 0,
                "errStr"    => "ok",       
            )); 
        }

		$aHeader    = ["pathinfo" => $package["prepare"]];
        if(in_array($service,['goodsservice','hulk'])) {
            $aHeader["Content-Type"] = "application/json";
        }
		$response   = self::invokeTrace(Zb_Const_Tcc::$serviceConfig[$rm]["name"], $aHeader, $tryParam);

        if (false === $response) {
            self::$sType == 'local' && self::$localFailedRM = $rm; //用于localCancel
            return false; 
        }

		return $response;
    }

    private static function _checkPrepareParam ($xid, $location, $commitParam, $cancelParam) {

        if (empty($xid) || empty($location)) {
            Bd_Log::warning("Error:[xid or location empty], Detail:[location:$location xid:$xid]");
			return false; 
        }

        if (!empty($commitParam) && strlen($commitParam) > 1048576) {
            Bd_Log::warning("Error:[location commit param length greater than 1048576], Detail:[location:$location commitParam:$commitParam]");
			return false; 
        }

		$cancelParamLen = strlen($cancelParam);
		if (!empty($cancelParam) && $cancelParamLen > 100000) {
            Bd_Log::warning("Error:[location cancel param length greater than 100000], Detail:[length:$cancelParamLen $location:$location cancelParam:$cancelParam]");
			return false; 
        }

		list($tm, $rm) = explode('_', $location);

		if (empty(Zb_Const_Tcc::$transactionManagerPackage[$tm])) {
			Bd_Log::warning("Error:[package of $tm empty], Detail:[tm:$tm]");
			return false;
		}

        // cancel 或 commit 必须存在一个 
		if (empty(Zb_Const_Tcc::$transactionManagerPackage[$tm][$rm]['confirm']) &&
			empty(Zb_Const_Tcc::$transactionManagerPackage[$tm][$rm]['cancel'])) {
			Bd_Log::warning("Error:[pacage $tm $rm confirm or cancel url empty], Detail:[tm:$tm rm:$rm]");
			return false;
		}

		return true;
    } 

	/**
     * @comment 提交事务
     *
	 * @param int $xid 全局事务ID
     * @param array $extParams 资源提交补充参数
     * return array
    **/
    public static function commit($xid, $extParams = array()) {

        // 1. check param
        if (empty($xid)) {
			Bd_Log::warning("Error:[commit xid empty], Detail:[]");
            return false; 
        }

        if (self::$sType == 'local'){
            return self::localCommit($xid, $extParams);
        }

		// 2. commit to TC Server
		$param = array(
			"xid"			=> $xid,
            'sType'         => self::$sType,
		);
        if($extParams && is_array($extParams)){
            $param['param'] = json_encode($extParams, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
        }

        $header = array("pathinfo"=>"/tcs/commit");

		$ret = self::invoke(self::SERVICE_NAME, $header, $param);
		if ($ret == false) {
			return false;
		}

		return $ret;
    }

	/**
     * @comment 回滚事务
     *
	 * @param	int		$xid			全局事务ID
     *
     * return array
    **/
    public static function cancel($xid) {

        // 1. check param
        if (empty($xid)) {
            Bd_Log::warning("Error:[cancel xid empty], Detail:[]");
            return false; 
        }

        if (self::$sType == 'local'){
            return self::localCancel($xid);
        }

		// 2. cancel to TC Server
		$param = array(
			"xid"			=> $xid,
            'sType'         => self::$sType,
		);

		$header = array("pathinfo"=>"/tcs/cancel");

		$ret = self::invoke(self::SERVICE_NAME, $header, $param);
		if ($ret == false) {
			return false;
		}

		return $ret;
    }

    /**
     * 获取最后的错误信息
     * @return array
     */
    public static function getLastErrInfo(){
        return self::$lastErrInfo;
    }


	/**
     * 统一调用RPC服务入口
     * @param $serviceName
     * @param $aHeader
     * @param $aParameter
	 *
     * @return mixed
    **/
    private static function invoke($serviceName,$aHeader, $aParameter) {

		ral_set_pathinfo($aHeader["pathinfo"]);

        $aResult = ral($serviceName, 'POST', $aParameter, 123);
        if ($aResult === false || empty($aResult)) {
            // ral调用失败，服务异常，需要重试
            $errorCode      = ral_get_errno();
            $errorMessage   = ral_get_error();
            $protocolCode   = ral_get_protocol_code();
            Bd_Log::warning(sprintf('Error:[service %s connect error], Detail:[errno:%s errmsg:%s protocol_status:%s]',
                    $serviceName,$errorCode,$errorMessage,$protocolCode)
            );
            self::$lastErrInfo = [
                'errNo'  => Zb_Util_ExceptionCodes::NETWORK_ERROR,
                'errMsg' => $errorMessage,
            ];
            return false;
        }

        $aResult            = json_decode($aResult, true);
        $errorCode          = intval($aResult['errNo']);
        $errorMessage       = strval($aResult['errStr']);
        if ($errorCode > 0) {
            Bd_Log::warning(sprintf('Error:[service %s process error], Detail:[errno:%s errmsg:%s]',
                    $serviceName,$errorCode,$errorMessage)
            );
            self::$lastErrInfo = [
                'errNo'  => $errorCode,
                'errMsg' => $errorMessage,
            ];
            return false;
        }

        return $aResult;
    }

    /**
     * 支持trace的RPC服务入口
     * @param $serviceName
     * @param $aHeader
     * @param $aParameter
     *
     * @return mixed
     **/
    private static function invokeTrace($serviceName, $aHeader, $aParameter) {
        $ret = Hk_Service_TraceStat::getInstance()->ralTrace($serviceName, 'POST', $aParameter, 123, $aHeader);

        Bd_Log::debug('requestApi_' . microtime(true), ral_get_errno(),['header:' => json_encode($aHeader), 'params:' => json_encode($aParameter), 'ret:' => strval($ret)]);

        if (false === $ret) {
            $errNo = ral_get_errno();
            $errMsg = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service $serviceName connect error], Detail:[errno:$errNo errmsg:$errMsg protocol_status:$protocol_status] params:" . json_encode($aParameter) . " header:" . json_encode($aHeader) . "]");
            self::$lastErrInfo = [
                'errNo'  => Zb_Util_ExceptionCodes::NETWORK_ERROR,
                'errMsg' => $errMsg,
            ];
            return false;
        }
		if (!is_array($ret)) {
        	$ret = json_decode($ret,true);
		}
        if (!isset($ret['errNo'])) {
            return false;
        }
        $errNo = intval($ret['errNo']);
        $errMsg = isset($ret['errStr'])?$ret['errStr']:$ret['errstr'];

        if ($errNo != 0) {
            if(!isset($ret['errStr']) && empty($errMsg)){
                $errMsg = $ret['errMsg']; // 库存服务改用go模块，错误信息不再是errStr而是errMsg
            }
            Bd_Log::warning("Error:[service $serviceName process error], Detail:[errno:$errNo errmsg:$errMsg params:" . json_encode($aParameter) . " header:" . json_encode($aHeader) . "]");
            self::$lastErrInfo = [
                'errNo'  => $errNo,
                'errMsg' => $errMsg,
            ];
            return false;
        }

        return $ret['data'];
    }

    private static function getSType(){
        if(Hk_Util_Navigator::isPressure()){
            $sType = Zb_Service_NCM::Get(Zb_Service_NCM::APP_YIKE,'trade','public','pressureTccServiceType');
        } else{
            $sType = Zb_Service_NCM::Get(Zb_Service_NCM::APP_YIKE,'trade','public','tccServiceType');
        }
        return $sType;
    }

    private static function localCommit($xid, $extParams){
        if(empty(self::$localResourceData)){
            Bd_Log::warning("Error:[resource is empty]");
            return false;
        }
        if($extParams){
            $logInfo = [
                self::$localStepConf['commit'],
                $xid,
                -1,
                json_encode($extParams, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
            ];
            $ret = self::writeLog($logInfo);
            if($ret === false){
                return false;
            }
        }
        $flag = 1;
        $succRM = [];
        foreach(self::$localResourceData as $resourceData){
            $rm = $resourceData[1];
            $commitUrl = $resourceData[2];
            $commitParam = $resourceData[4];
            if(empty($commitUrl)){
                continue;
            }
            $serviceName = Zb_Const_Tcc::$serviceConfig[$rm]['name'];
            $commitParam = json_decode($commitParam, true);
            $commitParam = self::makeReqParams($rm, $commitParam, $extParams);
            if(strncasecmp($serviceName, 'nmq-', 4) == 0){ //nmq调用
                $ret = Zb_Service_Nmq::sendCommand($commitParam['_cmd'], $commitParam, $commitParam['_topic']);
            }else{
                $header = array("pathinfo"=>$commitUrl);
                $ret = self::invoke($serviceName, $header, $commitParam);
            }
            if($ret === false){
                $flag = 0;
                break;
            }
            $succRM[] = $rm;
        }
        $logInfo = [
            self::$localStepConf['commit'],
            $xid,
            $flag,
            implode('|', $succRM),
        ];
        self::writeLog($logInfo);

        return array(
            'errNo'  => 0,
            'errStr' => 'success'
        );
    }

    private static function localCancel($xid){
        if(empty(self::$localResourceData)){
            Bd_Log::warning("Error:[resource is empty]");
            return false;
        }
        $flag = 1;
        $needCancelReource = [];
        foreach(self::$localResourceData as $resourceData){
            $rm = $resourceData[1];
            $cancelUrl = $resourceData[3];
            $cancelParam = $resourceData[5];
            if(empty($cancelUrl)){
                continue;
            }
            $needCancelReource[] = [$rm, $cancelUrl, $cancelParam];
            if(self::$localFailedRM != "" && $rm == self::$localFailedRM){
                break;
            }
        }
        $needCancelReource = array_reverse($needCancelReource);
        $tryedRM = [];
        foreach($needCancelReource as $cancelData){
            list($rm, $cancelUrl, $cancelParam) = $cancelData;
            $serviceName = Zb_Const_Tcc::$serviceConfig[$rm]['name'];
            $cancelParam = json_decode($cancelParam, true);
            $tryedRM[] = $rm;
            if(strncasecmp($serviceName, 'nmq-', 4) == 0){ //nmq调用
                $ret = Zb_Service_Nmq::sendCommand($cancelParam['_cmd'], $cancelParam, $cancelParam['_topic']);
            }else{
                $header = array("pathinfo"=>$cancelUrl);
                $ret = self::invoke($serviceName, $header, $cancelParam);
            }
            if($ret === false){
                $flag = 0;
                break;
            }
        }
        $logInfo = [
            self::$localStepConf['cancel'],
            $xid,
            $flag,
            implode('|', $tryedRM),
        ];
        self::writeLog($logInfo);
        if($flag == 0){
            return false;
        }
        return array(
            'errNo'  => 0,
            'errStr' => 'success'
        );
    }

    private static function makeReqParams($rm, $param, $extParams){
        if(isset($param['compress']) && $param['compress'] == "1"){
            $param = gzinflate(base64_decode($param['data']));
            $param = json_decode($param, true);
        }
        if(isset($extParams[$rm])){
            foreach($extParams[$rm] as $key=>$val){
                $param[$key] = $val;
            }
        }
        return $param;
    }

    private static function writeLog($logInfo){
        $ret = file_put_contents(self::$localLogFile, implode(self::$localLogSep, $logInfo) . "\n" , FILE_APPEND );
        if($ret === false){
            Bd_Log::warning('TCC Local mode writeLog fail[' . implode(self::$localLogSep, $logInfo) . ']');
            return false;
        }
        return true;
    }

}

