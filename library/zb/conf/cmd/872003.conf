#直播中台同步room数据
[noticeType]
type : int
must : 1
remark : 通知类型 1 创建 2 更新
[create]
type:map
must:1
remark: 创建参数
[.map]
[..roomId]
type : int
must : 0
remark : 房间id
[..roomType]
type : int
must : 0
remark : 房间类型
[..roomMode]
type : int
must : 0
remark : 房间模式 正课 or 旁听
[..teacherUid]
type : int
must : 0
remark : 主讲uid
[..lessonId]
type : int
must : 0
remark : 章节id
[..liveStatus]
type : int
must : 0
remark : 直播状态
[update]
type:map
must:1
remark: 更新参数
[.map]
[..roomId]
type : int
must : 0
remark : 房间id
[..lessonId]
type : int
must : 0
remark : 章节ID
[..liveStatus]
type : int
must : 0
remark : 直播状态
[..startTime]
type : int
must : 0
remark : 开始直播时间
[..endTime]
type : int
must : 0
remark : 结束直播时间