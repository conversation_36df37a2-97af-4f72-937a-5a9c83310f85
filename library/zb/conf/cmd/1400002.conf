#减课
[isGift]
type:int
must:0
remark:是否是赠课
[skuSourceId]
type:int
must:1
remark:商品业务线
[groupKey]
type:int
must:1
remark:时序保证
[studentUid]
type : int
must : 1
remark : 学生id
[courseId]
type : int
must : 1
remark : 课程id
[lessonIds]
type:string
must:1
remark:未上章节(退款章节)
[changeTo]
type:int
must:1
remark:调课旧课程id
[commitTime]
type:int
must:1
remark:提交时间
[tradeId]
type : int
must : 0
remark : 订单id
[subTradeId]
type : int
must : 0
remark : 子订单id
[brandId]
type:int
must:0
remark:商品品牌
[callBackParam]
type:map
must:0
remark:供回调ofc使用
[.map]
[..ofcId]
type : int
must : 0
remark : 履约id
[..behaviorId]
type : int
must : 0
remark : 履约行为id
[refundReason]
type:string
must:0
remark:退款原因
[originalCourseId]
type : int
must : 0
remark : 最初购买课程id
[originalPayTime]
type : int
must : 0
remark : 最初购买课程支付时间
[skuId]
type : int
must : 0
remark : 商品id
[saleChannelId]
type : int
must : 0
remark : 渠道id
