#挽单未分配工单入库通知

[studentUid]
type : int
must : 1
remark : 学生ID

[courseId]
type : int
must : 1
remark : 当前退款课程Id

[lockId]
type : int
must : 1
remark : 当前退款工单ID

[createTime]
type : int
must : 1
remark : 退款工单创建时间

[refundReason]
type : string
must : 1
remark : 退款原因

[refundFee]
type : int
must : 1
remark : 退款金额

[subTradeInfo]
type:map
must:1
remark:子订单信息
[.map]
[..subTradeId]
type : int
must : 1
remark : 子订单Id
[..tradeId]
type : int
must : 1
remark : 订单Id
[..tradeFee]
type : int
must : 1
remark : 订单金额
[..tradeTime]
type : int
must : 1
remark : 子订单交易时间
[..changeOrderType]
type : int
must : 1
remark : 订单转班类型
[..lastfrom]
type : string
must : 1
remark : 订单lastfrom

[orderDispatchHistory]
type:list
must:1
remark:订单历史处理记录
[.list]
type : map
must : 0
remark : 订单历史处理记录
[..map]
[...lockId]
type : int
must : 0
remark : 历史退款工单ID
[...dispatchUser]
type : int
must : 0
remark : 分配人员Uid
[...dispatchTime]
type : int
must : 0
remark : 分配时间
[...handleUid]
type : int
must : 0
remark : 实际处理人
[...handleTime]
type : int
must : 0
remark : 实际处理时间
[...handleMsg]
type : string
must : 0
remark : 处理的详细信息
