<?php

/**
 * nmq跳命令配置
 * class Zb_Util_NmqJump
 */
class Zb_Util_NmqJump
{
    private static $moduleMap = array(
        'dal' => 'dak',
        'dak' => 'dak',
        'dat' => 'dak',
    );
    const MODULE_DAL = 'dal';
    const MODULE_MISCOURSE = 'miscourse';
    private static $moduleHookMap = array(
        self::MODULE_DAL => 'https://oapi.dingtalk.com/robot/send?access_token=439f2eab0ec23f472f463c29d32c8c0e8f29459c8aa218b423b07884e808c657',
        self::MODULE_MISCOURSE => 'https://oapi.dingtalk.com/robot/send?access_token=7d79eb334bf51019556e508116b3e34a39d6cc4da11e2c6718dbacef718815fb',
    );
    private static $moduleHookKeyWords = [
        self::MODULE_MISCOURSE => 'miscourse',
    ];
    //需要跳过 true 不需要false
    public static function jumpJudge($module = ''){
        $clientIp =  Bd_Ip::getClientIp();
        $transId = $_REQUEST['transid'];
        if(!$module || !isset(self::$moduleMap[$module])) {
            return false;
        }
        $ncmModule = self::$moduleMap[$module];
        //获取ncm配置
        $jumplist = Zb_Service_NCM::Get(1,$ncmModule, 'public', 'jumpTransList');
        if(false === $jumplist) {
            return false;
        }
        $jumplist = json_decode($jumplist, true);
        $nowTrans = $clientIp.'_'.$module.'_'.$transId;
        if(in_array($nowTrans, $jumplist)){
            self::sendDingtalk('dal', '命令'.$nowTrans.'已跳过报警');
            return true;
        }
        return false;
    }
    //目前只支持关键字鉴权
    public static function sendDingtalk($module = 'dal', $message = ''){
        $webhook = isset(self::$moduleHookMap[$module]) ? self::$moduleHookMap[$module] : '' ;
        if($webhook == '') {
            return false;
        }
        //$message="我就是我, 是不一样的烟火";
        $message = self::addKeyWord($module, $message);
        $data = array ('msgtype' => 'text','text' => array ('content' => $message));
        $data_string = json_encode($data);

        $result = self::requestByCurl($webhook, $data_string);
        return $result;
    }

    /**
     * 给消息体添加机器人关键字
     * @param $module
     * @param $message
     * @return string
     */
    private static function addKeyWord($module, $message)
    {
        //消息中添加关键词，否则钉钉消息无法发送成功
        if (isset(self::$moduleHookKeyWords[$module]) && self::$moduleHookKeyWords[$module]) {
            $message .= "\nrobotKeyWords:" . self::$moduleHookKeyWords[$module];
        }
        return $message;
    }

    private static function requestByCurl($remote_server, $post_string) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $remote_server);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array ('Content-Type: application/json;charset=utf-8'));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 线下环境不用开启curl证书验证, 未调通情况可尝试添加该代码
        curl_setopt ($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }
}
