<?php
/**
 * Created by phplib.
 * Auther: <EMAIL>
 * Date: 2021/9/27 2:27 下午
 */

class Zb_Util_MoatAuth
{
    private static $Instance = [];
    private static $callerMap = [
        'zbcore_dar' => 'Zb_Util_Moatauth_ZbcoreDar',
        'zbcore_das' => 'Zb_Util_Moatauth_ZbcoreDas',
        'zbcore_dau' => 'Zb_Util_Moatauth_ZbcoreDau',
        'zbcore_dat' => 'Zb_Util_Moatauth_ZbcoreDat',
        'zbcore_dal' => 'Zb_Util_Moatauth_ZbcoreDal',
    ];


    /**
     * moat sign
     *
     * @param array $arrParams 请求参数
     *
     * @return bool
     */
    public static function Sign(&$arrParams, $querystring, $caller)
    {
        //无法鉴权 -- 鉴权类缺失
        if (!self::getInstance($caller)) {
            return true;
        }

        /** @var \Zb_Util_Moatauth_AuthTpl $objAuth */
        $objAuth = self::$Instance[$caller];

        if (!$objAuth->isNeedSign()) {
            return true;
        }
        $query = self::parseQueryString($querystring);
        return $objAuth->Sign($arrParams, $query);
    }

    /**
     * 通过caller 获取单例
     *
     * @param $caller
     *
     * @return bool
     */
    private static function getInstance($caller)
    {
        if (!isset(self::$callerMap[$caller])) {
            return false;
        }
        if (!class_exists(self::$callerMap[$caller])) {
            return false;
        }
        is_null(self::$Instance[$caller]) && self::$Instance[$caller] = new self::$callerMap[$caller];
        return true;
    }

    private static function parseQueryString($queryString)
    {
        parse_str($queryString, $arrParam);
        return $arrParam;
    }
}
