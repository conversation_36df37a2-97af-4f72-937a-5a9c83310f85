<?php

/**
 * Created by phplib.
 * Auther: <EMAIL>
 * Date: 2021/10/5 7:23 下午
 */
interface Zb_Util_Moatauth_AuthTpl
{
    /**
     * <p>前置检查是否需要加签</p>
     * @return bool
     * <p><b>false</b> : 不需要</p>
     * <p><b>true </b> : 需要</p>
     */
    public function isNeedSign();

    /**
     * @param $arrParams array 请求参数
     * @param $query header:querystring
     *
     * @return mixed
     */
    public function Sign(&$arrParams, $query);
}