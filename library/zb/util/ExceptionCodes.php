<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file ExceptionCodes.php
 * <AUTHOR>
 * @date 2017/9/21 20:26:46
 * @brief 直播课异常命令号
 *
 **/

class Zb_Util_ExceptionCodes {
    //通用异常（1-99999）
    const PARAM_ERROR                           = 1; //参数错误
    const NETWORK_ERROR                         = 2; //网络错误
    const USER_NOT_LOGIN                        = 3; //登录过期，请重新登录
    const DB_ERROR                              = 15; //db异常
    const ACTSCTRL_CUID                         = 20; //名字cuid频率控制策略
    const ACTSCTRL_UIP                          = 21; //名字uip频率控制策略
    const ACTSCTRL_UID                          = 22; //名字uid频率控制策略
    const IDALLOC_ERROR                         = 23; //id分配器错误
    const NMQ_COMMANDNO_ERROR                   = 24; //nmq命令号异常
    const NMQ_SEND_ERROR                        = 25; //nmq命令发送异常
    const COMMON_DELCACHE_CHECK_ERROR           = 26; //延迟删除缓存参数检查错误
    const COMMON_DELCACHE_PROCESS_ERROR         = 27; //延迟删除缓存处理错误

    //核心数据子系统（100000-199999）

    //dal(100000-109999)
    const DAL_COURSE_CHECK_ERROR                = 100001; //课程检查失败
    const DAL_COURSE_ADD_ERROR                  = 100002; //课程添加失败
    const DAL_COURSE_EDIT_ERROR                 = 100003; //课程修改失败
    const DAL_COURSE_CLOSE_ERROR                = 100004; //课程关闭失败

    const DAL_LESSON_CHECK_ERROR                = 101001; //章节检查失败
    const DAL_LESSON_ADD_ERROR                  = 101002; //章节添加失败
    const DAL_LESSON_FINISHED_ERROR             = 101003; //章节结束失败
    const DAL_LESSON_RESTART_ERROR              = 101004; //章节重开失败
    const DAL_LESSON_ADJUST_TIME_ERROR          = 101005; //章节时间调整失败
    const DAL_LESSON_DEL_ERROR                  = 101006; //章节删除失败
    const DAL_LESSON_COURSE_LOST_ERROR          = 101007; //章节课程关系不存在
    const DAL_LESSON_EDIT_ERROR                 = 101008; //章节修改失败

    const DAL_CPU_CHECK_ERROR                   = 102001; //课程产品检查失败
    const DAL_CPU_ADD_ERROR                     = 102002; //课程产品添加失败
    const DAL_CPU_ALREADY_EXISTS                = 102003; //课程产品已存在
    const DAL_CPU_GET_ERROR                     = 102004; //课程产品获取失败
    const DAL_CPU_UPDATE_ERROR                  = 102005; //课程产品更新失败
    const DAL_CPU_DELETE_ERROR                  = 102006; //课程产品删除失败

    const DAL_OUTLINE_CHECK_ERROR               = 103001; //大纲检查失败
    const DAL_OUTLINE_EDIT_ERROR                = 103002; //大纲修改失败
    const DAL_OUTLINE_ADD_ERROR                 = 103003; //大纲添加失败
    const DAL_OUTLINE_GET_ERROR                 = 103004; //大纲获取失败
    const DAL_OUTLINE_DELETE_ERROR              = 103005; //大纲删除失败

    const DAL_SERVICE_CHECK_ERROR               = 104001; //容器服务信息检查失败
    const DAL_SERVICE_OBTAIN_ERROR              = 104002; //容器服务信息获取失败
    const DAL_SERVICE_ADD_ERROR                 = 104003; //容器服务信息添加失败

    //dat(110000-119999)
    const DAT_TEACHER_LESSON_CHECK_ERROR        = 110000; //主讲-章节检查失败
    const DAT_TEACHER_LESSON_ADD_ERROR          = 110001; //主讲-章节添加失败
    const DAT_TEACHER_LESSON_DELETE_ERROR       = 110002; //主讲-章节删除失败
    const DAT_TEACHER_LESSON_FINISHED_ERROR     = 110003; //主讲-章节结束失败
    const DAT_COURSE_TEACHER_UPDATE_ERROR       = 110004; //课程-主讲更新失败
    const DAT_TEACHER_LESSON_UPDATE_ERROR       = 110005; //主讲章节更新失败

    //单品异常（1200001-129999）
    const DAK_COURSE_CHECK_ERROR                = 120001; //课程检查失败
    const DAK_LESSON_CHECK_ERROR                = 120002; //章节检查失败
    const DAK_SKU_ADD_ERROR                     = 120003; //单品添加失败
    const DAK_SKULIST_ADD_ERROR                 = 120004; //单品列表添加失败
    const DAK_SKUCOURSE_ADD_ERROR               = 120005; //单品课程关系添加失败
    const DAK_GET_SKU_ERROR                     = 120006; //获取单品信息失败
    const DAK_SKU_UPDATE_ERROR                  = 120007; //更新单品失败
    const DAK_SKULIST_UPDATE_ERROR              = 120008; //更新单品列表失败
    const DAK_SKU_NOT_EXIST                     = 120009; //单品不存在
    const DAK_SKU_OFFLINE_ERROR                 = 120010; //单品下线失败
    const DAK_SKULIST_OFFLINE_ERROR             = 120011; //单品列表下线失败
    const DAK_COURSE_NOT_EXIST                  = 120012; //单品课程不存在
    const DAK_SKU_ONLINE_ERROR                  = 120013; //商品上线失败
    const DAK_SKU_SHOW_ERROR                    = 120014; //商品显示失败
    const DAK_SKU_HIDE_ERROR                    = 120015; //商品隐藏失败
    const DAK_SKUCOURSE_ADD_DELETE              = 120016; //单品课程关系删除失败
    const DAK_SKU_GIFT_ADD_ERROR                = 120017; //单品赠品添加失败
    const DAK_SKULIST_GIFT_ADD_ERROR            = 120018; //单品赠品列表添加失败
    const DAK_CPU_SYNC_SPU_ERROR                = 120019; //CPU同步SPU失败
    const DAK_SYNC_PLATFORM_SPU_ERROR           = 120020; //SPU同步商品平台失败
    const DAK_SYNC_PLATFORM_SPU_PARAM_ERROR     = 120021; //SPU同步商品平台参数错误
    const DAK_SYNC_PLATFORM_GOODS_SKU_ERROR     = 120022; //同步GoodsSKU ES服务写入失败
    const DAK_SYNC_PLATFORM_GOODS_SPU_ERROR     = 120023; //同步GoodsSPU ES服务写入失败
    const DAK_SYNC_PLATFORM_GOODS_SKU_RET_ERROR = 120024; // SKU同步商品平台失败
    const DAK_SYNC_PLATFORM_GOODS_SPU_PARAMS_ERROR = 120025; // SKU同步商品平台参数错误


    //das(130000-139999)
    const DAS_COURSE_CHECK_ERROR                 = 130001; //学生-课程检查失败
    const DAS_COURSE_ADD_ERROR                   = 130002; //学生-课程添加失败
    const DAS_LESSON_CHECK_ERROR                 = 130003; //学生-章节检查失败
    const DAS_LESSON_ADD_ERROR                   = 130004; //学生-章节添加失败
    const DAS_COURSE_LESSON_GAIN_ERROR           = 130005; //课程章节获取失败
    const DAS_COURSE_UPDATE_ERROR                = 130006; //学生课程更新失败
    const DAS_LESSON_UPDATE_ERROR                = 130007; //学生章节更新失败
    const DAS_COMMAND_DEAL_ERROR                 = 130008; //das命令处理失败
    const DAS_DELAY_ADD_GET_LOCK_ERROR           = 130009; //延迟加课获取锁失败
    const DAS_DELAY_REDUCE_GET_LOCK_ERROR        = 130010; //延迟减课获取锁失败
    const DAS_DELAY_MOCK                         = 130011; //延迟减课模拟

    //履约服务错误码（176001-178999）
    const OFC_SUBTRADEID_INVALID                 = 176001;//无效履约子订单编号
    const OFC_SUBTRADEID_UNABLE                  = 176002;//无可履约子订单
    const OFC_SUBTRADEMAP_FAILED                 = 176003;//履约参数构建失败
    const OFC_PRODUCT_TYPE_INVALID               = 176004;//无效履约产品类型

    const OFC_CONF_QUERY_ERROR                   = 176101;//履约配置查询失败
    const OFC_CONF_NOT_EXIST                     = 176102;//履约配置不存在
    const OFC_CONF_HAS_EXIST                     = 176103;//履约配置已存在
    const OFC_CONF_ADD_FAILED                    = 176104;//履约配置添加失败
    const OFC_CONF_EDIT_FAILED                   = 176105;//履约配置编辑失败
    const OFC_CONF_COMMIT_FAILED                 = 176106;//履约配置提交失败
    const OFC_CONF_PUB_FAILED                    = 176107;//履约配置发布失败
    const OFC_CONF_VALUE_ERROR                   = 176108;//履约配置值错误

    const OFC_EXECUTE_FAIL                       = 176201;//履约执行失败
    const OFC_TEST_FAIL                          = 176202;//交易测试失败

    const OFC_CALLER_INVALID                     = 177005;//无效履约调用者

    const OFC_UPDATE_FAILED                      = 178001;//履约更新失败
    const OFC_FIX_FAILED                         = 178004;//履约修复失败
    const OFC_USER_INVALID                       = 178002;//用户信息错误
    const OFC_INFO_ERROR                         = 178003;//履约信息错误

    //订单快照服务错误码（190001-199999）
    const OSS_SNAP_QUERY_ERROR                   = 190001;//订单快照查询失败
    const OSS_SNAP_NOT_EXIST                     = 190002;//订单快照不存在
    const OSS_SNAP_INIT_ERROR                    = 190003;//订单快照初始化错误
    const OSS_SNAP_PAY_ERROR                     = 190004;//订单快照支付错误
    const OSS_SNAP_CLOSED_ERROR                  = 190005;//订单快照关单错误
    const OSS_SNAP_STATUS_ERROR                  = 190006;//订单快照状态错误
    const OSS_SNAP_CHECK_ERROR                   = 190007;//订单快照检查错误
    const OSS_SNAP_LOCK_ERROR                    = 190008;//锁订单快照错误
    const OSS_SNAP_UNLOCK_ERROR                  = 190009;//解锁订单快照错误
    const OSS_SNAP_FLAG_UPDATE_ERROR             = 190010;//订单快照事务标记更新错误
    const OSS_TCC_EMPTY_CANCEL_ERROR             = 190011;//下单失败关单空cancel

    //数据字典系统错误码（179000 - 179999）
    const DDS_QUERY_ERROR                        = 179001;//数据字典查询错误
    const DDS_DICT_ERROR                         = 179002;//字典数据错误
    const DDS_ENUM_ERROR                         = 179003;//枚举数据错误
    const DDS_OWNER_ERROR                        = 179004;//归属数据错误
    const DDS_DICT_REPEAT                        = 179005;//数据字典重复
    const DDS_SUBMIT_TOO_MUCH                    = 179006;//修改项过多
    const DDS_OWNER_HASH_BIND                    = 179007;//归属人已经分组
    const DDS_OWNER_NOT_BIND                     = 179008;//不得绑定
    const DDS_ADD_FAIL                           = 179009;//添加失败
    const DDS_EDIT_FAIL                          = 179010;//编辑失败
    const DDS_CODE_PRODUCE_FAIL                  = 179011;//代码生成失败
    const DDS_DOCS_PRODUCE_FAIL                  = 179012;//文档生成失败
    const DDS_OWNER_MUST_RD                      = 179013;//归属必须是RD身份
    const DDS_SUBMIT_REPEAT                      = 179014;//提交重复，重新提交
    
    //httpdns策略中心错误码（180001 - 180999）
    const HTTPDNS_MAP_EMPTY                      = 180001;//HTTPDNS MAP为空
    const HTTPDNS_MAP_PARAM_ERROR                = 180002;//HTTPDNS MAP参数错误
    const HTTPDNS_DOMAIN_STATUS_ERROR            = 180003;//HTTPDNS 域名状态错误
    const HTTPDNS_STRATEGY_MAP_ERROR             = 180004;//HTTPDNS 策略MAP格式错误
    const HTTPDNS_STRATEGY_TYPE_ERROR            = 180005;//HTTPDNS 策略类型错误
    const HTTPDNS_SWITCH_MAP_ERROR               = 180006;//HTTPDNS 切换MAP格式错误
    const HTTPDNS_WHITELIST_MAP_ERROR            = 180007;//HTTPDNS 白名单MAP格式错误
    const HTTPDNS_AUTH_ERROR                     = 180008;//HTTPDNS 授权错误
    const HTTPDNS_DOMAIN_NOT_EXIST               = 180009;//域名不存在
    const HTTPDNS_EDIT_FAIL                      = 180010;//编辑失败
    const HTTPDNS_ADD_FAIL                       = 180011;//添加失败
    

    //订单异常（300001-399999）
    const DAR_TRADE_CREATE_ERROR                 = 300001; //订单创建失败
    const DAR_TRADE_PAY_ERROR                    = 300002; //订单支付回调处理失败
    const DAR_TRADE_CHANGE_ERROR                 = 300003; //订单调课回调处理失败
    const DAR_TRADE_ST_REFUND_ERROR              = 300004; //订单开始退款错误
    const DAR_TRADE_ED_REFUND_ERROR              = 300005; //订单结束退款错误
    const DAR_TRADE_ADD_UPDATE_ERROR             = 300006; //订单寄送地址更新错误
    const DAR_TRADE_QUERY_ERROR                  = 300007; //订单查询失败
    const DAR_TRADE_CANCEL_ERROR                 = 300008; //订单取消失败

    //子订单异常（301001-301999）
    const DAR_SUBTRADE_UPDATE_BATCHID_ERROR        = 301001;//子订单更新批次号错误

    const DAR_SUBTRADE_QUERY_ERROR                  = 301002;//子订单数据查询失败
    const DAR_SUBTRADE_BATCHID_ERROR                = 301003;//子订单批次号错误
    const DAR_SUBTRADE_UPDATE_REMINDTIME_ERROR     = 301004;//子订单更新催单时间错误
    const DAR_SUBTRADE_STATUS_ERROR                = 301005;//子订单状态错误
    const DAR_SUBTRADE_CANCEL_ERROR                 = 301008; //子订单取消失败

    //MisCourse课程基础数据(400001-499999)
    /******4.0.1  MisCourse-SYSTEM & USER(400001-400199)******/
    const MIS_ADD_COURSESYSTEM_FAIL                     = 400001;
    const MIS_ADD_CLASSTYPE_FAIL                        = 400002;
    const MIS_ADD_BOOKVER_FAIL                          = 400003;
    const MIS_ADD_COURSEMODULE_FAIL                     = 400004;
    const MIS_CHECK_COURSESYSTEM_REPEAT                 = 400005;
    const MIS_CHECK_CLASSTYPE_REPEAT                    = 400006;
    const MIS_CHECK_BOOKVER_REPEAT                      = 400007;
    const MIS_CHECK_COURSEMODULE_REPEAT                 = 400008;
    const MIS_CHECK_ARES_TASK_NOT_MATCHING              = 400009;
    const MIS_CHECK_ARES_TASK_CONTAINER_NOT_MATCHING    = 400010;
    const MIS_CHECK_COURSE_TYPE_NO_PRIVILEGE            = 400011;
    const MIS_CHECK_INNER_NO_PRIVILEGE                  = 400012;
    const MIS_CHECK_NO_PRIVILEGE_THIS_COURSE_TYPE       = 400013;
    const MIS_SEND_110036_FAIL                          = 400014;
    const MIS_ARES_SYNC_FAIL                            = 400015;
    const MIS_ARES_CPU_TASK_FAIL                        = 400016;
    const MIS_ARES_COURSE_TASK_FAIL                     = 400017;
    const MIS_ARES_CREATE_TASK_FAIL                     = 400018;
    const MIS_ARES_SYNC_SERVICE_FAIL                    = 400019;
    const MIS_ARES_GET_TASK_FAIL                        = 400020;
    const MIS_CHECK_DETECT_STRING_ERROR                 = 400021;
    const MIS_CHECK_UPLOAD_FILE_CNT_ERROR               = 400022;
    const MIS_CHECK_UPLOAD_FILE_GREATER_ERROR           = 400023;
    const MIS_CHECK_UPLOAD_FILE_LESS_ERROR              = 400024;
    const MIS_CHECK_SOURCE_ERROR                        = 400025;
    const MIS_DEL_FREE_EXAM_FAIL                        = 400026;
    /******4.0.2  MisCourse-CPU(400200-400299)******/
    const MIS_CHECK_CPU_ID_ERROR                        = 400200;
    const MIS_CHECK_CPU_NOT_EXIST                       = 400201;
    const MIS_ADD_CPU_FAIL                              = 400202;
    const MIS_UPDATE_CPU_FAIL                           = 400203;
    const MIS_DEL_CPU_FAIL                              = 400204;
    const MIS_ADD_GPU_FAIL                              = 400205;
    const MIS_CHECK_RELEASE_CPU_ERROR                   = 400206;
    const MIS_CHECK_CPU_TYPE_ERROR                      = 400207;
    const MIS_CHECK_CPU_NAME_ERROR                      = 400209;
    const MIS_CHECK_CPU_TEST_BIND_ERROR                 = 400211;
    const MIS_CHECK_CPU_LESSON_MATCH_ERROR              = 400212;
    const MIS_CHECK_CPU_LESSON_CORE_ERROR               = 400213;
    const MIS_CHECK_CPU_SUGGEST_GRADE_ERROR             = 400214;
    const MIS_CHECK_CPU_SUGGEST_SUBJECT_ERROR           = 400215;
    const MIS_CHECK_CPU_NAME_LEN_ERROR                  = 400216;
    const MIS_CHECK_CPU_YEAR_ERROR                      = 400217;
    const MIS_CHECK_CPU_SEASON_ERROR                    = 400218;
    const MIS_CHECK_CPU_SYSTEM_ERROR                    = 400219;
    const MIS_CHECK_CPU_MODULE_ERROR                    = 400220;
    const MIS_CHECK_CPU_BOOKVER_ERROR                   = 400221;
    const MIS_CHECK_CPU_HASMATERIALSERVICE_ERROR        = 400222;
    const MIS_CHECK_CPU_HASHANDOUTSSERVICE_ERROR        = 400223;
    const MIS_CHECK_CPU_EXAMSERVICE_ERROR               = 400224;
    const MIS_CHECK_CPU_BRANDID_ERROR                   = 400225;
    const MIS_CHECK_CPU_ISINNER_ERROR                   = 400226;
    const MIS_CHECK_CPU_GRADE_ERROR                     = 400227;
    const MIS_CHECK_CPU_SUBJECT_ERROR                   = 400228;
    const MIS_CHECK_CPU_SUGGESTEDGRADEIDS_ERROR         = 400229;
    const MIS_CHECK_CPU_SUGGESTEDSUBJECTIDS_ERROR       = 400230;
    const MIS_CHECK_CPU_LEVEL_ERROR                     = 400231;
    const MIS_CHECK_CPU_GRADESUBJECTRELATION_ERROR      = 400232;
    const MIS_CHECK_CPU_COURSESYSTEMRELATION_ERROR      = 400233;
    const MIS_UPDATE_CPU_LOCK_STATUS_FAIL               = 400234;
    const MIS_GET_SPU_EMPTY                             = 400235;
    const MIS_CHECK_CPU_CANADDCOURSE_ERROR              = 400236;
    const MIS_CHECK_CPU_CANONLINECOURSE_ERROR           = 400237;
    const MIS_CHECK_CPU_CANADDOUTLINE_ERROR             = 400238;
    const MIS_CHECK_CPU_PLAYFORM_ERROR                  = 400239;

    /******4.0.3  MisCourse-OUTLINE(400300-400399)******/
    const MIS_CHECK_OUTLINE_ID_ERROR                    = 400300;
    const MIS_CHECK_OUTLINE_NOT_EXIST                   = 400301;
    const MIS_CHECK_OUTLINE_NAME_ERROR                  = 400302;
    const MIS_CHECK_OUTLINE_TYPE_ERROR                  = 400303;
    const MIS_CHECK_OUTLINE_PLAYTYPE_ERROR              = 400304;
    const MIS_CHECK_OUTLINE_PICS_URL_ERROR              = 400305;
    const MIS_ADD_OUTLINE_FAIL                          = 400306;
    const MIS_BATCH_ADD_OUTLINE_FAIL                    = 400307;
    const MIS_UPDATE_OUTLINE_FAIL                       = 400308;
    const MIS_UPDATE_OUTLINE_SEQNUM_FAIL                = 400309;
    const MIS_DEL_OUTLINE_FAIL                          = 400310;
    const MIS_CHECK_OUTLINE_SUBJECT_ERROR               = 400311;
    const MIS_CHECK_OUTLINE_SERVICE_ERROR               = 400312;
    const MIS_CHECK_OUTLINE_CORE_CNT_ERROR              = 400313;
    const MIS_RELEASE_OUTLINE_FAIL                      = 400314;
    const MIS_GET_OUTLINE_FAIL                          = 400315;
    const MIS_CHECK_OUTLINE_LESSON_CNT_FAIL             = 400316;
    const MIS_CHECK_OUTLINE_STAGETEST_NAME_ERROR        = 400317;
    const MIS_CHECK_OUTLINE_SMALLCLASS_ERROR            = 400318;
    const MIS_CHECK_SECONDOUTLINE_CNT_ERROR             = 400319;
    const MIS_CHECK_SECONDOUTLINE_NAME_LENS_ERROR       = 400320;
    const MIS_CHECK_OUTLINE_ERROR                       = 400321;
    const MIS_BATCH_ADD_OUTLINE_MAX_CNT_FAIL            = 400322;
    const MIS_CHECK_OUTLINE_BEFORERECORD_ERROR          = 400323;
    const MIS_CHECK_OUTLINE_NEWCOURSETYPE_ERROR         = 400324;

    /******4.0.4  MisCourse-COURSE(400400-400499)******/
    const MIS_CHECK_COURSE_ID_ERROR                     = 400400;
    const MIS_CHECK_COURSE_NOT_EXIST                    = 400401;
    const MIS_GET_COURSE_FAIL                           = 400402;
    const MIS_ADD_COURSE_FAIL                           = 400403;
    const MIS_UPDATE_COURSE_FAIL                        = 400404;
    const MIS_DEL_COURSE_FAIL                           = 400405;
    const MIS_CHECK_SEASONCATEGORY_ERROR                = 400406;
    const MIS_CHECK_COURSE_NAME_ERROR                   = 400407;
    const MIS_CHECK_COURSE_LEARNSEASON_ERROR            = 400408;
    const MIS_CHECK_COURSE_STUDENTMAXCNT_ERROR          = 400409;
    const MIS_CHECK_COURSE_PRICE_ERROR                  = 400410;
    const MIS_CHECK_COURSE_COURSETAGS_ERROR             = 400411;
    const MIS_CHECK_COURSE_SPECIALSELLTYPE_ERROR        = 400412;
    const MIS_CHECK_COURSE_COURSESERVICE_ERROR          = 400413;
    const MIS_CHECK_COURSE_SCHEDULE_ERROR               = 400414;
    const MIS_CHECK_COURSE_ASSISTANT_ERROR              = 400415;
    const MIS_CHECK_COURSE_CALLTEXT_ERROR               = 400416;
    const MIS_CHECK_COURSE_GROUPTEXT_ERROR              = 400417;
    const MIS_CHECK_COURSE_GROUPPROJECT_ERROR           = 400418;
    const MIS_CHECK_COURSE_SCTAGS_ERROR                 = 400419;
    const MIS_ADD_COURSE_ATTRIBUTE_FAIL                 = 400420;
    const MIS_ADD_COURSE_ATTRIBUTETAGS_FAIL             = 400421;
    const MIS_CHECK_COURSE_LESSON_ERROR                 = 400422;
    const MIS_GET_COURSE_LESSON_FAIL                    = 400423;
    const MIS_DEL_COURSE_LESSON_RELATION_FAIL           = 400424;
    const MIS_ADD_COURSE_LESSON_RELATION_FAIL           = 400425;
    const MIS_CHECK_COURSE_LESSON_NOT_RELATED           = 400426;
    const MIS_UPDATE_COURSE_LESSON_RELATION_FAIL        = 400427;
    const MIS_CHECK_COURSE_CAN_ONLINE_ERROR             = 400428;
    const MIS_UPDATE_COURSE_ONLINE_ERROR                = 400429;
    const MIS_CHECK_COURSE_TEACHER_ERROR                = 400430;
    const MIS_CHECK_COURSE_SCSERVICE_ERROR              = 400431;
    const MIS_CHECK_COURSE_SERVICE_NONSUPPORT           = 400432;

    /******4.0.5  MisCourse-LESSON(400500-400599)******/
    const MIS_CHECK_LESSON_ID_ERROR                     = 400500;
    const MIS_CHECK_LESSON_NOT_EXIST                    = 400501;
    const MIS_ADD_LESSON_FAIL                           = 400502;
    const MIS_UPDATE_LESSON_FAIL                        = 400503;
    const MIS_DEL_LESSON_FAIL                           = 400504;
    const MIS_CHECK_SHARE_LESSON_ERROR                  = 400505;
    const MIS_CHECK_LESSON_TIME_CONFLICT                = 400506;
    const MIS_CHECK_LESSON_CNT_GREATER                  = 400507;
    const MIS_CHECK_LESSON_CNT_LESS                     = 400508;
    const MIS_CHECK_LESSON_OUTLINE_NOT_MATCHING         = 400509;
    const MIS_CHECK_LESSON_CORELESSONTIME_ERROR         = 400510;
    const MIS_CHECK_LESSON_CORELESSONDATE_ERROR         = 400511;
    const MIS_CHECK_LESSON_OTHERLESSONTIME_ERROR        = 400512;
    const MIS_CHECK_LESSON_OTHERLESSONDATE_ERROR        = 400513;
    const MIS_CHECK_LESSON_CORELESSON_DATE_CYCLE_ERROR  = 400514;
    const MIS_CHECK_LESSON_OTHERLESSON_LOCKTIME_ERROR   = 400515;
    const MIS_CHECK_LESSON_CORELESSON_LOCKTIME_ERROR    = 400516;
    const MIS_CHECK_LESSON_PLAYTYPE_NOT_MATCHING        = 400517;
    const MIS_GET_LESSON_FAIL                           = 400518;
    const MIS_CHECK_LESSON_TEACHER_MISSING              = 400519;
    const MIS_CHECK_LESSON_TIME_MISSING                 = 400520;
    const MIS_CHECK_LESSON_STOPTIME_LESS                = 400521;
    const MIS_CHECK_LESSON_CAN_DEL_ERROR                = 400522;
    const MIS_CHECK_LESSON_NOT_FINISHED                 = 400523;
    const MIS_CHECK_SHARE_LESSON_PLAYTYPE_ERROR         = 400524;
    const MIS_CHECK_LESSON_RELATED_LIVETASK_ERROR       = 400525;
    const MIS_CHECK_LESSON_BANXUEINFO_ERROR             = 400526;

    /******4.0.6  MisCourse-SERVICE(400600-400699)******/
    const MIS_CHECK_SERVICE_ID_ERROR                    = 400600;
    const MIS_CHECK_SERVICE_NOT_EXIST                   = 400601;
    const MIS_GET_SERVICE_FAIL                          = 400602;
    const MIS_ADD_SERVICE_FAIL                          = 400603;
    const MIS_UPDATE_SERVICE_FAIL                       = 400604;
    const MIS_DEL_SERVICE_FAIL                          = 400605;
    const MIS_CHECK_CONTAINER_ID_ERROR                  = 400606;
    const MIS_CHECK_SERVICE_LIVE_ERROR                  = 400607;
    const MIS_GET_ARES_LOCAL_TASK_FAIL                  = 400608;
    const MIS_ADD_ARES_LOCAL_TASK_FAIL                  = 400609;
    const MIS_UPDATE_ARES_LOCAL_TASK_FAIL               = 400610;
    const MIS_CHECK_SERVICE_MCH_ERROR                   = 400611;
    const MIS_CHECK_SERVICE_QQ_WX_CONFLICT              = 400612;
    const MIS_CHECK_SERVICE_ASSISTANT_WX_CONFLICT       = 400613;
    const MIS_CHECK_SERVICE_FUDAO_CHUJING_CONFLICT      = 400614;
    const MIS_CHECK_SERVICE_WX_CHILD_ERROR              = 400615;
    const MIS_CHECK_SERVICE_HAS_ASSISTANT_ERROR         = 400616;
    const MIS_CHECK_SERVICE_COCOS_LIVE_ERROR            = 400617;
    const MIS_CHECK_SERVICE_FUDAO_CHUJING_NONSUPPORT    = 400618;
    /******4.0.7  MisCourse-ACTION LOG(400700-400799)******/
    const MIS_GET_ACTION_LOG_FAIL                       = 400700;
    const MIS_ADD_ACTION_LOG_FAIL                       = 400701;
    const MIS_UPDATE_ACTION_LOG_FAIL                    = 400702;
    const MIS_DEL_ACTION_LOG_FAIL                       = 400703;
    /******4.0.8  MisCourse-导入导出(400800-400899)******/
    const MIS_GET_TASK_FAIL                             = 400800;
    /******4.0.9  MisCourse-OTHER (400900-400999)******/
    const MIS_OTHER_ERROR                               = 400900;
    /******4.0.10  MisCourse-教辅 (401000-401099)******/
    const MIS_CHECK_MATERIAL_ID_ERROR                   = 401000;
    const MIS_CHECK_MATERIAL_NOT_EXIST                  = 401001;
    const MIS_GET_MATERIAL_FAIL                         = 401002;
    const MIS_GET_MATERIAL_RELATION_FAIL                = 401003;
    const MIS_ADD_MATERIAL_FAIL                         = 401004;
    const MIS_ADD_MATERIAL_RELATION_FAIL                = 401005;
    const MIS_UPDATE_MATERIAL_FAIL                      = 401006;
    const MIS_DEL_MATERIAL_FAIL                         = 401007;
    const MIS_DEL_MATERIAL_RELATION_FAIL                = 401008;
    const MIS_CHECK_MATERIAL_STATUS_ERROR               = 401009;
    const MIS_CHECK_MATERIAL_BIND_ERROR                 = 401010;
    const MIS_UPDATE_MATERIAL_STATUS_ERROR              = 401011;
    const MIS_BIND_MATERIAL_FAIL                        = 401012;
    const MIS_UNBIND_MATERIAL_FAIL                      = 401013;
    const MIS_CHECK_MATERIAL_EARLIEST_TIME_FAIL         = 401014;
    const MIS_CHECK_MATERIAL_EARLIEST_TYPE_FAIL         = 401015;
    const MIS_CHECK_MATERIAL_LATEST_TIME_FAIL           = 401016;
    const MIS_CHECK_MATERIAL_LATEST_TYPE_FAIL           = 401017;
    /******4.0.11  MisCourse-主讲&辅导老师 (401100-401199)******/
    const MIS_CHECK_TEACHER_ID_ERROR                    = 401100;
    const MIS_CHECK_TEACHER_NOT_EXIST                   = 401101;
    const MIS_CHECK_TEACHER_PLAYTYPE_NOT_MATCHING       = 401102;
    const MIS_CHECK_TEACHER_REPEAT_MATCHING             = 401103;
    const MIS_CHECK_TEACHER_PHONE_ERROR                 = 401104;
    /******4.0.12  MisCourse-商品 (401200-401299)******/
    const MIS_CHECK_SKU_ONSALE_TIME_ERROR               = 401200;
    const MIS_CHECK_SKU_FULLSALE_TIME_ERROR             = 401201;
    const MIS_CHECK_SALE_USER_RANGE_ERROR               = 401202;

    /******4.1.1 CourseSearch (401000-401999)******/

    /******4.2.1 CourseList (402000-402999)******/

    //物流异常
    const WMS_INTERCRPING_FAILED               = 500001;
    const WMS_NOT_ENTER                        = 500002;
    const WMS_EDIT_FAILED                      = 500003;

    //billing
    const BILLING_RESOURCE_ERR                 = 600001;
    const BILLING_RES_LISTING_ERR              = 600002;
    const BILLING_RES_PRODUCT_ERR              = 600003;

    //策略相关
    const ONLINEBIZ_IS_NO                      = 700001;
    const ONLINEBIZ_TIME_ERROR                 = 700002;
    const APPOINT_TIME_ERROR                   = 700003;
    const LESSON_INFO_EMPTY                    = 700004;
    const ONLINEBIZ_STATUS_NOT                 = 700005;
    const SKUID_SALEBIZ_ISSET                  = 700006;
    const LESSON_TIME_INFO_ERROR               = 700007;
    const SALEBIZ_IS_NO                         = 700008;
    const UPDATE_ONLINEBIZ_FAIL                = 700009;
    const UPDATE_SALEBIZ_FAIL                  = 700010;
    const ADD_SKUONLINEBIZ_FAIL                = 700011;
    const ADD_SKUSALEMODEL_FAIL                = 700012;
    const GET_LESSION_FAIL                     = 700013;
    const GET_SKUONLINEBIZ_FAIL                = 700014;
    const DEL_SKUONLINEBIZ_FAIL                = 700015;
    const DEL_SKUSALEMODEL_FAIL                = 700016;
    const UPDATE_SKUONLINEBIZ_STATUS_FAIL     = 700017;
    const UPDATE_SKUSALEMODEL_STATUS_FAIL     = 700018;




    public static $errMsg = array(
        //通用异常
        self::PARAM_ERROR                       => '参数错误',
        self::NETWORK_ERROR                     => '网络错误',
        self::USER_NOT_LOGIN                    => '登录过期，请重新登录',
        self::DB_ERROR                          => 'db异常',
        self::ACTSCTRL_CUID                     => '名字cuid频率控制策略',
        self::ACTSCTRL_UIP                      => '名字uip频率控制策略',
        self::ACTSCTRL_UID                      => '名字uid频率控制策略',
        self::IDALLOC_ERROR                     => 'id分配异常',
        self::NMQ_COMMANDNO_ERROR               => 'nmq命令号异常',
        self::NMQ_SEND_ERROR                    => 'nmq命令发送异常',
        self::COMMON_DELCACHE_CHECK_ERROR       => '延迟删除缓存参数检查错误',
        self::COMMON_DELCACHE_PROCESS_ERROR     => '延迟删除缓存处理错误',

        //核心数据子系统
        //dal
        self::DAL_COURSE_CHECK_ERROR            => '课程检查失败',
        self::DAL_COURSE_ADD_ERROR              => '课程添加失败',
        self::DAL_COURSE_EDIT_ERROR             => '课程修改失败',
        self::DAL_COURSE_CLOSE_ERROR            => '课程关闭失败',

        self::DAL_LESSON_CHECK_ERROR            => '章节检查失败',
        self::DAL_LESSON_ADD_ERROR              => '章节添加失败',
        self::DAL_LESSON_FINISHED_ERROR         => '章节结束失败',
        self::DAL_LESSON_RESTART_ERROR          => '章节重开失败',
        self::DAL_LESSON_ADJUST_TIME_ERROR      => '章节时间调整失败',
        self::DAL_LESSON_DEL_ERROR              => '章节删除失败',

        self::DAL_LESSON_COURSE_LOST_ERROR      => '章节课程关系不存在',

        self::DAL_CPU_CHECK_ERROR               => '课程产品检查失败',
        self::DAL_CPU_ADD_ERROR                 => '课程产品添加失败',
        self::DAL_CPU_ALREADY_EXISTS            => '课程产品已存在',
        self::DAL_CPU_GET_ERROR                 => '课程产品获取失败',
        self::DAL_CPU_UPDATE_ERROR              => '课程产品更新失败',
        self::DAL_CPU_DELETE_ERROR              => '课程产品删除失败',

        self::DAL_OUTLINE_CHECK_ERROR           => '大纲检查失败',
        self::DAL_OUTLINE_EDIT_ERROR            => '大纲修改失败',
        self::DAL_OUTLINE_ADD_ERROR             => '大纲添加失败',
        self::DAL_OUTLINE_GET_ERROR             => '大纲获取失败',
        self::DAL_OUTLINE_DELETE_ERROR          => '大纲删除失败',

        self::DAL_SERVICE_CHECK_ERROR           => '容器服务信息检查失败',
        self::DAL_SERVICE_OBTAIN_ERROR          => '容器服务信息获取失败',
        self::DAL_SERVICE_ADD_ERROR             => '容器服务信息添加失败',

        //dat
        self::DAT_TEACHER_LESSON_CHECK_ERROR    => '主讲-章节检查失败',
        self::DAT_TEACHER_LESSON_ADD_ERROR      => '主讲-章节添加失败',
        self::DAT_TEACHER_LESSON_DELETE_ERROR   => '主讲-章节删除失败',
        self::DAT_TEACHER_LESSON_FINISHED_ERROR => '主讲-章节结束失败',

        //单品异常
        self::DAK_COURSE_CHECK_ERROR            => '课程检查失败',
        self::DAK_LESSON_CHECK_ERROR            => '章节检查失败',
        self::DAK_SKU_ADD_ERROR                 => '单品添加失败',
        self::DAK_SKULIST_ADD_ERROR             => '单品列表添加失败',
        self::DAK_SKUCOURSE_ADD_ERROR           => '单品课程关系添加失败',
        self::DAK_GET_SKU_ERROR                 => '获取单品信息失败',
        self::DAK_SKU_UPDATE_ERROR              => '更新单品失败',
        self::DAK_SKULIST_UPDATE_ERROR          => '更新单品列表失败',
        self::DAK_SKU_NOT_EXIST                 => '单品不存在',
        self::DAK_SKU_OFFLINE_ERROR             => '单品下线失败',
        self::DAK_SKULIST_OFFLINE_ERROR         => '单品列表下线失败',
        self::DAK_COURSE_NOT_EXIST              => '单品课程不存在',
        self::DAK_SKU_ONLINE_ERROR              => '商品上线失败',
        self::DAK_SKU_SHOW_ERROR                => '商品显示失败',
        self::DAK_SKU_HIDE_ERROR                => '商品隐藏失败',
        self::DAK_SKU_GIFT_ADD_ERROR            => '单品赠品添加失败',
        self::DAK_SKULIST_GIFT_ADD_ERROR        => '单品赠品列表添加失败',
        self::DAK_CPU_SYNC_SPU_ERROR            => 'CPU同步SPU失败',
        self::DAK_SYNC_PLATFORM_SPU_ERROR       => 'SPU同步商品平台失败',
        self::DAK_SYNC_PLATFORM_SPU_PARAM_ERROR => 'SPU同步商品平台参数错误',
        self::DAK_SYNC_PLATFORM_GOODS_SKU_ERROR => '同步GoodsSKU ES服务写入失败',
        self::DAK_SYNC_PLATFORM_GOODS_SPU_ERROR => '同步GoodsSPU ES服务写入失败',
        self::DAK_SYNC_PLATFORM_GOODS_SKU_RET_ERROR => 'SKU同步商品平台失败',
        self::DAK_SYNC_PLATFORM_GOODS_SPU_PARAMS_ERROR =>'SKU同步商品平台参数错误',


        //das
        self::DAS_COURSE_CHECK_ERROR            => '学生-课程检查失败',
        self::DAS_COURSE_ADD_ERROR              => '学生-课程添加失败',

        self::DAS_LESSON_CHECK_ERROR            => '学生-章节检查失败',
        self::DAS_LESSON_ADD_ERROR              => '学生-章节添加失败',

        self::DAS_COURSE_LESSON_GAIN_ERROR      => '课程-章节获取失败',
        self::DAS_COMMAND_DEAL_ERROR            => 'das命令处理失败',

        self::DAS_DELAY_ADD_GET_LOCK_ERROR      => '延迟加课-获取锁失败',
        self::DAS_DELAY_REDUCE_GET_LOCK_ERROR   => '延迟减课-获取锁失败',
        self::DAS_DELAY_MOCK                    => '延迟减课模拟',

        // dar
        self::DAR_TRADE_CREATE_ERROR            => '创建订单错误',
        self::DAR_TRADE_PAY_ERROR               => '支付订单错误',
        self::DAR_TRADE_CHANGE_ERROR            => '调课订单错误',
        self::DAR_TRADE_ST_REFUND_ERROR         => '开始退款订单错误',
        self::DAR_TRADE_ED_REFUND_ERROR         => '结束退款订单错误',
        self::DAR_TRADE_QUERY_ERROR             => '订单查询失败',
        self::DAR_TRADE_CANCEL_ERROR            => '订单取消失败',

        self::DAR_SUBTRADE_QUERY_ERROR          => '子订单查询失败',
        self::DAR_SUBTRADE_BATCHID_ERROR        => '批次号错误',
        self::DAR_SUBTRADE_UPDATE_BATCHID_ERROR => '更新子订单物流批次号失败',
        self::DAR_SUBTRADE_UPDATE_REMINDTIME_ERROR => '更新子订单物流催单时间失败',
        self::DAR_SUBTRADE_STATUS_ERROR         => '子订单状态错误',
        self::DAR_SUBTRADE_CANCEL_ERROR         => '子订单取消失败',

        //课程基础数据
        self::MIS_ADD_COURSESYSTEM_FAIL                     => '添加课程体系失败',
        self::MIS_ADD_CLASSTYPE_FAIL                        => '添加课程类型失败',
        self::MIS_ADD_BOOKVER_FAIL                          => '添加教材版本失败',
        self::MIS_ADD_COURSEMODULE_FAIL                     => '添加课程模块失败',
        self::MIS_CHECK_COURSESYSTEM_REPEAT                 => '添加的体系在系统中已经存在',
        self::MIS_CHECK_CLASSTYPE_REPEAT                    => '添加的班型在系统中已经存在',
        self::MIS_CHECK_BOOKVER_REPEAT                      => '添加的教材版本在系统中已经存在',
        self::MIS_CHECK_COURSEMODULE_REPEAT                 => '添加的课程模块在系统中已经存在',
        self::MIS_CHECK_ARES_TASK_NOT_MATCHING              => '任务不存在',
        self::MIS_CHECK_ARES_TASK_CONTAINER_NOT_MATCHING    => '任务不匹配',
        self::MIS_CHECK_COURSE_TYPE_NO_PRIVILEGE            => '用户无该课程类型权限',
        self::MIS_CHECK_INNER_NO_PRIVILEGE                  => '只有“线上课程管理组”的用户拥有操作权限',
        self::MIS_SEND_110036_FAIL                          => '110036信令发送失败',
        self::MIS_ARES_SYNC_FAIL                            => '同步ares系统失败',
        self::MIS_ARES_CPU_TASK_FAIL                        => 'ares课程产品发布任务异常',
        self::MIS_ARES_COURSE_TASK_FAIL                     => 'ares课程发布任务异常',
        self::MIS_ARES_CREATE_TASK_FAIL                     => '创建任务流实例失败',
        self::MIS_ARES_SYNC_SERVICE_FAIL                    => '同步容器服务项任务失败',
        self::MIS_ARES_GET_TASK_FAIL                        => '获取任务列表异常',
        self::MIS_CHECK_DETECT_STRING_ERROR                 => '校验敏感词汇失败',
        self::MIS_CHECK_UPLOAD_FILE_CNT_ERROR               => '上传文件大于5个文件',
        self::MIS_CHECK_UPLOAD_FILE_GREATER_ERROR           => '上传文件大于50MB，请重新上传',
        self::MIS_CHECK_UPLOAD_FILE_LESS_ERROR              => '上传文件小于78KB，请重新上传',
        self::MIS_CHECK_SOURCE_ERROR                        => 'source参数无效',
        self::MIS_DEL_FREE_EXAM_FAIL                        => '删除免测关系失败',
        /******4.0.2  MisCourse-CPU(400200-400299)******/
        self::MIS_CHECK_CPU_ID_ERROR                        => 'cpuId参数错误',
        self::MIS_CHECK_CPU_NOT_EXIST                       => '该课程产品不存在',
        self::MIS_ADD_CPU_FAIL                              => '添加课程产品失败',
        self::MIS_UPDATE_CPU_FAIL                           => '更新课程产品失败',
        self::MIS_DEL_CPU_FAIL                              => '删除课程产品失败',
        self::MIS_ADD_GPU_FAIL                              => '生成GPU失败',
        self::MIS_CHECK_RELEASE_CPU_ERROR                   => '课程产品不可发布',
        self::MIS_CHECK_CPU_TYPE_ERROR                      => '无效的classType参数',
        self::MIS_CHECK_CPU_NAME_ERROR                      => '课程产品名称中未含有体系或班型',
        self::MIS_CHECK_CPU_TEST_BIND_ERROR                 => '当前课程没有绑定报前测或报后测',
        self::MIS_CHECK_CPU_LESSON_MATCH_ERROR              => '当前课程产品章节结构异常,核心课数量小于提升课',
        self::MIS_CHECK_CPU_LESSON_CORE_ERROR               => '班课产品中必须含有核心课章节',
        self::MIS_CHECK_CPU_SUGGEST_GRADE_ERROR             => '班课产品推荐年级不能为空',
        self::MIS_CHECK_CPU_SUGGEST_SUBJECT_ERROR           => '班课产品推荐学科不能为空',
        self::MIS_CHECK_CPU_NAME_LEN_ERROR                  => '课程产品名称不能未空',
        self::MIS_CHECK_CPU_YEAR_ERROR                      => '无效的年份',
        self::MIS_CHECK_CPU_SEASON_ERROR                    => '不存在的季节',
        self::MIS_CHECK_CPU_SYSTEM_ERROR                    => '无效的system参数',
        self::MIS_CHECK_CPU_MODULE_ERROR                    => '无效的module参数',
        self::MIS_CHECK_CPU_BOOKVER_ERROR                   => '无效的bookVer参数',
        self::MIS_CHECK_CPU_HASMATERIALSERVICE_ERROR        => '无效的hasMaterialService参数',
        self::MIS_CHECK_CPU_HASHANDOUTSSERVICE_ERROR        => '无效的hasHandoutsService参数',
        self::MIS_CHECK_CPU_EXAMSERVICE_ERROR               => '无效的examService参数',
        self::MIS_CHECK_CPU_BRANDID_ERROR                   => '无效的brandId参数',
        self::MIS_CHECK_CPU_ISINNER_ERROR                   => '无效的isInner参数',
        self::MIS_CHECK_CPU_GRADE_ERROR                     => '无效的grade参数',
        self::MIS_CHECK_CPU_SUBJECT_ERROR                   => '无效的subject参数',
        self::MIS_CHECK_CPU_SUGGESTEDGRADEIDS_ERROR         => '无效的suggestedGradeIds参数',
        self::MIS_CHECK_CPU_SUGGESTEDSUBJECTIDS_ERROR       => '无效的suggestedSubjectIds参数',
        self::MIS_CHECK_CPU_LEVEL_ERROR                     => '无效的level参数',
        self::MIS_CHECK_CPU_GRADESUBJECTRELATION_ERROR      => '不存在的年级学科关系',
        self::MIS_CHECK_CPU_COURSESYSTEMRELATION_ERROR      => '不存在的体系班型关系',
        self::MIS_UPDATE_CPU_LOCK_STATUS_FAIL               => '更新课程产品锁定状态失败',
        self::MIS_GET_SPU_EMPTY                             => '创建SPU信息延迟,请稍后重试',
        self::MIS_CHECK_CPU_CANADDCOURSE_ERROR              => '当前课程产品不可关联班级',
        self::MIS_CHECK_CPU_CANONLINECOURSE_ERROR           => '当前课程产品状态未完成，不可上线',
        self::MIS_CHECK_CPU_CANADDOUTLINE_ERROR             => '当前课程产品不可添加大纲',
        self::MIS_CHECK_CPU_PLAYFORM_ERROR                  => '无效的playForm参数',
        /******4.0.3  MisCourse-OUTLINE(400300-400399)******/
        self::MIS_CHECK_OUTLINE_ID_ERROR                    => 'outlineId参数无效',
        self::MIS_CHECK_OUTLINE_ERROR                       => 'outline参数无效',
        self::MIS_BATCH_ADD_OUTLINE_MAX_CNT_FAIL            => '批量大纲信息创建一次不能超过50个',
        self::MIS_CHECK_OUTLINE_NOT_EXIST                   => '大纲不存在',
        self::MIS_CHECK_OUTLINE_NAME_ERROR                  => 'outlineName参数无效',
        self::MIS_CHECK_OUTLINE_TYPE_ERROR                  => 'outlineType参数无效',
        self::MIS_CHECK_OUTLINE_PLAYTYPE_ERROR              => 'playType参数无效',
        self::MIS_CHECK_OUTLINE_PICS_URL_ERROR              => '封面图片链接不能为空',
        self::MIS_ADD_OUTLINE_FAIL                          => '添加大纲失败',
        self::MIS_BATCH_ADD_OUTLINE_FAIL                    => '批量添加大纲失败',
        self::MIS_UPDATE_OUTLINE_FAIL                       => '更新大纲失败',
        self::MIS_UPDATE_OUTLINE_SEQNUM_FAIL                => '调整大纲顺序失败',
        self::MIS_DEL_OUTLINE_FAIL                          => '删除大纲失败',
        self::MIS_CHECK_OUTLINE_SUBJECT_ERROR               => '大纲学科无效，请编辑大纲修改学科',
        self::MIS_CHECK_OUTLINE_SERVICE_ERROR               => '大纲服务项异常',
        self::MIS_CHECK_OUTLINE_CORE_CNT_ERROR              => '核心大纲数不能为0',
        self::MIS_RELEASE_OUTLINE_FAIL                      => '发布大纲失败',
        self::MIS_GET_OUTLINE_FAIL                          => '获取大纲信息失败',
        self::MIS_CHECK_OUTLINE_LESSON_CNT_FAIL             => '大纲数量与生成章节数不匹配',
        self::MIS_CHECK_OUTLINE_STAGETEST_NAME_ERROR        => '阶段测名称不能为空',
        self::MIS_CHECK_OUTLINE_SMALLCLASS_ERROR            => 'smallClass参数错误',
        self::MIS_CHECK_SECONDOUTLINE_CNT_ERROR             => '二级大纲最多支持五个小节',
        self::MIS_CHECK_SECONDOUTLINE_NAME_LENS_ERROR       => '二级大纲最多支持输入50个汉字',
        self::MIS_CHECK_OUTLINE_BEFORERECORD_ERROR          => '计划录制时间参数无效',
        self::MIS_CHECK_OUTLINE_NEWCOURSETYPE_ERROR         => 'newCourseType无效',
        /******4.0.4  MisCourse-COURSE(400400-400499)******/
        self::MIS_CHECK_COURSE_ID_ERROR                     => 'courseId参数错误',
        self::MIS_CHECK_COURSE_NOT_EXIST                    => '班级不存在',
        self::MIS_GET_COURSE_FAIL                           => '获取班级信息失败',
        self::MIS_ADD_COURSE_FAIL                           => '添加班级失败',
        self::MIS_UPDATE_COURSE_FAIL                        => '更新班级失败',
        self::MIS_DEL_COURSE_FAIL                           => '删除班级失败',
        self::MIS_CHECK_SEASONCATEGORY_ERROR                => '期数与季节不匹配',
        self::MIS_CHECK_COURSE_NAME_ERROR                   => '无效的courseName参数',
        self::MIS_CHECK_COURSE_LEARNSEASON_ERROR            => '无效的learnSeason参数',
        self::MIS_CHECK_COURSE_STUDENTMAXCNT_ERROR          => '无效的studentMaxCnt参数',
        self::MIS_CHECK_COURSE_PRICE_ERROR                  => '售价不可为负',
        self::MIS_CHECK_COURSE_COURSETAGS_ERROR             => '无效的courseTags参数',
        self::MIS_CHECK_COURSE_SPECIALSELLTYPE_ERROR        => '无效的specialSellType参数',
        self::MIS_CHECK_COURSE_COURSESERVICE_ERROR          => '无效的courseService(服务项)参数',
        self::MIS_CHECK_COURSE_SCHEDULE_ERROR               => '无效的scheduleService(排灌班方式)参数',
        self::MIS_CHECK_COURSE_ASSISTANT_ERROR              => '无效的assistantSubservice(班主任服务项)参数',
        self::MIS_CHECK_COURSE_CALLTEXT_ERROR               => '无效的callText(称呼文案)参数',
        self::MIS_CHECK_COURSE_GROUPTEXT_ERROR              => '无效的groupText(入群文案)参数',
        self::MIS_CHECK_COURSE_GROUPPROJECT_ERROR           => '无效的groupServiceProject(群服务项)参数',
        self::MIS_CHECK_COURSE_SCTAGS_ERROR                 => '无效的scServiceTags(SC服务标签)参数',
        self::MIS_ADD_COURSE_ATTRIBUTE_FAIL                 => '创建课程属性失败',
        self::MIS_ADD_COURSE_ATTRIBUTETAGS_FAIL             => '创建课程标签失败',
        self::MIS_CHECK_COURSE_LESSON_ERROR                 => '当前课程没有关联的章节',
        self::MIS_GET_COURSE_LESSON_FAIL                    => '获取对应课程章节关系失败',
        self::MIS_DEL_COURSE_LESSON_RELATION_FAIL           => '删除课程与章节对应关系失败',
        self::MIS_ADD_COURSE_LESSON_RELATION_FAIL           => '添加课程与章节关联关系失败',
        self::MIS_CHECK_COURSE_LESSON_NOT_RELATED           => '课程与章节关联关系不存在',
        self::MIS_UPDATE_COURSE_LESSON_RELATION_FAIL        => '更新课程与章节关联关系失败',
        self::MIS_CHECK_COURSE_CAN_ONLINE_ERROR             => '当前班级不可上线',
        self::MIS_UPDATE_COURSE_ONLINE_ERROR                => '班级上线失败',
        self::MIS_CHECK_COURSE_TEACHER_ERROR                => '主讲异常',
        self::MIS_CHECK_COURSE_SCSERVICE_ERROR              => '无效的scService(督学服务服务项集合)参数',
        self::MIS_CHECK_COURSE_SERVICE_NONSUPPORT           => '含有不支持课程服务',

        /******4.0.5  MisCourse-LESSON(400500-400599)******/
        self::MIS_CHECK_LESSON_ID_ERROR                     => '章节ID无效',
        self::MIS_CHECK_LESSON_NOT_EXIST                    => '章节不存在',
        self::MIS_ADD_LESSON_FAIL                           => '添加章节失败',
        self::MIS_UPDATE_LESSON_FAIL                        => '更新章节失败',
        self::MIS_DEL_LESSON_FAIL                           => '删除章节失败',
        self::MIS_CHECK_SHARE_LESSON_ERROR                  => '共享章节信息校验失败',
        self::MIS_CHECK_LESSON_TIME_CONFLICT                => '上课日期和时间存在冲突',
        self::MIS_CHECK_LESSON_CNT_GREATER                  => '上课次数大于大纲数量，请修改上课日期、上课周期',
        self::MIS_CHECK_LESSON_CNT_LESS                     => '上课次数小于大纲数量，请修改上课日期、上课周期',
        self::MIS_CHECK_LESSON_OUTLINE_NOT_MATCHING         => '上课时间与大纲结构不匹配',
        self::MIS_CHECK_LESSON_CORELESSONTIME_ERROR         => '核心课上课时间无效',
        self::MIS_CHECK_LESSON_CORELESSONDATE_ERROR         => '核心课开始、结束日期有误',
        self::MIS_CHECK_LESSON_OTHERLESSONTIME_ERROR        => '提升课上课时间无效',
        self::MIS_CHECK_LESSON_OTHERLESSONDATE_ERROR        => '提升课开始、结束日期有误',
        self::MIS_CHECK_LESSON_CORELESSON_DATE_CYCLE_ERROR  => '核心课实际上课时间不能为空，请调整上课日期或周期',
        self::MIS_CHECK_LESSON_OTHERLESSON_LOCKTIME_ERROR   => '提升课解锁时间有误',
        self::MIS_CHECK_LESSON_CORELESSON_LOCKTIME_ERROR    => '核心课解锁时间有误',
        self::MIS_CHECK_LESSON_PLAYTYPE_NOT_MATCHING        => '章节授课类型与大纲类型不匹配',
        self::MIS_GET_LESSON_FAIL                           => '获取章节信息失败',
        self::MIS_CHECK_LESSON_TEACHER_MISSING              => '章节主讲老师信息缺失',
        self::MIS_CHECK_LESSON_TIME_MISSING                 => '章节开课时间信息缺失',
        self::MIS_CHECK_LESSON_STOPTIME_LESS                => '章节结束时间小于等于开始时间',
        self::MIS_CHECK_LESSON_CAN_DEL_ERROR                => '当前章节不能被删除',
        self::MIS_CHECK_LESSON_NOT_FINISHED                 => '当前章节未结束',
        self::MIS_CHECK_SHARE_LESSON_PLAYTYPE_ERROR         => '关联章节的播放类型不能为伪直播',
        self::MIS_CHECK_LESSON_RELATED_LIVETASK_ERROR       => '关联章节的录播课任务无效',

        /******4.0.6  MisCourse-SERVICE(400600-400699)******/
        self::MIS_CHECK_SERVICE_ID_ERROR                    => 'serviceId参数错误',
        self::MIS_CHECK_SERVICE_NOT_EXIST                   => '不存在的服务项',
        self::MIS_GET_SERVICE_FAIL                          => '获取大纲信息失败',
        self::MIS_ADD_SERVICE_FAIL                          => '添加服务项失败',
        self::MIS_UPDATE_SERVICE_FAIL                       => '更新服务项失败',
        self::MIS_DEL_SERVICE_FAIL                          => '删除服务项失败',
        self::MIS_CHECK_CONTAINER_ID_ERROR                  => 'containerId参数错误',
        self::MIS_CHECK_SERVICE_LIVE_ERROR                  => '直播间参数异常',
        self::MIS_GET_ARES_LOCAL_TASK_FAIL                  => '创建ares任务项失败',
        self::MIS_ADD_ARES_LOCAL_TASK_FAIL                  => '获取ares任务项失败',
        self::MIS_UPDATE_ARES_LOCAL_TASK_FAIL               => '更新ares任务项失败',
        self::MIS_CHECK_SERVICE_MCH_ERROR                   => '积分账户参数有误',
        self::MIS_CHECK_SERVICE_QQ_WX_CONFLICT              => 'QQ群服务和微信群服务不可同时勾选',
        self::MIS_CHECK_SERVICE_ASSISTANT_WX_CONFLICT       => '班主任服务和微信群服务不可同时勾选',
        self::MIS_CHECK_SERVICE_FUDAO_CHUJING_CONFLICT      => '班主任课中出镜与课后出镜服务互斥',
        self::MIS_CHECK_SERVICE_WX_CHILD_ERROR              => '微信群服务必须包含微信群子服务项',
        self::MIS_CHECK_SERVICE_HAS_ASSISTANT_ERROR         => '出镜课程必须有班主任或督学服务',
        self::MIS_CHECK_SERVICE_COCOS_LIVE_ERROR            => 'cocos课件，不支持该直播间',
        self::MIS_CHECK_SERVICE_FUDAO_CHUJING_NONSUPPORT    => '不支持班主任出镜',
        /******4.0.7  MisCourse-ACTION LOG(400700-400799)******/
        self::MIS_GET_ACTION_LOG_FAIL                       => '获取日志失败',
        self::MIS_ADD_ACTION_LOG_FAIL                       => '添加日志失败',
        self::MIS_UPDATE_ACTION_LOG_FAIL                    => '更新日志失败',
        self::MIS_DEL_ACTION_LOG_FAIL                       => '删除日志失败',
        /******4.0.8  MisCourse-导入导出(400800-400899)******/
        self::MIS_GET_TASK_FAIL                             => '获取记录列表异常',
        /******4.0.9  MisCourse-OTHER (400900-404999)******/
        self::MIS_OTHER_ERROR                               => '未知错误',
        /******4.0.10  MisCourse-教辅 (401000-401099)******/
        self::MIS_CHECK_MATERIAL_ID_ERROR                   => '教辅id无效',
        self::MIS_CHECK_MATERIAL_NOT_EXIST                  => '教辅信息不存在',
        self::MIS_GET_MATERIAL_FAIL                         => '获取教辅信息失败',
        self::MIS_GET_MATERIAL_RELATION_FAIL                => '获取容器的教辅关系信息失败',
        self::MIS_ADD_MATERIAL_FAIL                         => '添加教辅信息失败',
        self::MIS_ADD_MATERIAL_RELATION_FAIL                => '添加教辅关系信息失败',
        self::MIS_UPDATE_MATERIAL_FAIL                      => '更新教辅信息失败',
        self::MIS_DEL_MATERIAL_FAIL                         => '删除教辅信息失败',
        self::MIS_DEL_MATERIAL_RELATION_FAIL                => '删除容器的教辅关系失败',
        self::MIS_CHECK_MATERIAL_STATUS_ERROR               => '当前课程已勾选绑定教辅，尚未绑定，请前往操作确认绑定并校验，对外发布后不可修改',
        self::MIS_CHECK_MATERIAL_BIND_ERROR                 => '当前课程没有绑定教材教辅型',
        self::MIS_UPDATE_MATERIAL_STATUS_ERROR              => '更新教辅服务状态失败',
        self::MIS_BIND_MATERIAL_FAIL                        => '绑定教辅失败',
        self::MIS_UNBIND_MATERIAL_FAIL                      => '解绑教辅失败',
        self::MIS_CHECK_MATERIAL_EARLIEST_TYPE_FAIL         => '批次的最早寄送时间类型不一致',
        self::MIS_CHECK_MATERIAL_EARLIEST_TIME_FAIL         => '批次的最早寄送时间类型不一致',
        self::MIS_CHECK_MATERIAL_LATEST_TYPE_FAIL           => '批次的最晚寄送时间类型不一致',
        self::MIS_CHECK_MATERIAL_LATEST_TIME_FAIL           => '批次的最晚寄送时间不一致',
        /******4.0.11  MisCourse-主讲&辅导老师 (401100-401199)******/
        self::MIS_CHECK_TEACHER_ID_ERROR                    => '主讲老师id无效',
        self::MIS_CHECK_TEACHER_NOT_EXIST                   => '主讲老师不存在',
        self::MIS_CHECK_TEACHER_PLAYTYPE_NOT_MATCHING       => '老师类型与直播类型不匹配',
        self::MIS_CHECK_TEACHER_REPEAT_MATCHING             => '老师姓名重复',
        self::MIS_CHECK_TEACHER_PHONE_ERROR                 => '老师手机号码无效',
        /******4.0.12  MisCourse-商品 (401200-401299)******/
        self::MIS_CHECK_SKU_ONSALE_TIME_ERROR               => '商品上架时间不能晚于预售期开始时间',
        self::MIS_CHECK_SKU_FULLSALE_TIME_ERROR             => '全员开售时间不能晚于截止报名时间',
        self::MIS_CHECK_SALE_USER_RANGE_ERROR               => '老生窗口期结束时间不能晚于全员开售时间',
        //物流相关错误
        self::WMS_INTERCRPING_FAILED            => '物流截单失败',
        self::WMS_NOT_ENTER                     => '物流订单尚未流入',
        self::WMS_EDIT_FAILED                   => '物流订单修改失败',

        //Billing 相关
        self::BILLING_RESOURCE_ERR              => '依赖资源错误',

        //策略相关
        self::ONLINEBIZ_IS_NO                   => '策略不存在',
        self::ONLINEBIZ_TIME_ERROR              => '上架时间大于下架时间',
        self::APPOINT_TIME_ERROR                => '售卖开始时间大于售卖结束时间',
        self::LESSON_INFO_EMPTY                 => '章节信息为空',
        self::ONLINEBIZ_STATUS_NOT              => '该策略已应用',
        self::LESSON_TIME_INFO_ERROR            => '章节时间信息错误',
        self::SKUID_SALEBIZ_ISSET               => '该商品已存在策略',
        self::SALEBIZ_IS_NO                      => '策略不存在',
        self::UPDATE_ONLINEBIZ_FAIL             => "修改上架策略信息数据库操作失败",
        self::UPDATE_SALEBIZ_FAIL               => "修改售卖策略信息数据库操作失败",
        self::ADD_SKUONLINEBIZ_FAIL             => "添加商品绑定上架策略失败",
        self::ADD_SKUSALEMODEL_FAIL             => "添加售卖策略信息数据库操作失败",
        self::GET_LESSION_FAIL                   => "获取课程章节信息失败",
        self::GET_SKUONLINEBIZ_FAIL              =>"查询商品上架绑定表数据库操作失败",
        self::DEL_SKUONLINEBIZ_FAIL              =>"删除商品上架绑定表数据库操作失败",
        self::DEL_SKUSALEMODEL_FAIL              =>"删除商品售卖类型表数据库操作失败",
        self::UPDATE_SKUONLINEBIZ_STATUS_FAIL   =>"修改上架策略状态表操作失败",
        self::UPDATE_SKUSALEMODEL_STATUS_FAIL   =>"修改售卖策略状态表操作失败",

        //履约服务
        self::OFC_SUBTRADEID_INVALID              => '无效履约子订单编号',
        self::OFC_SUBTRADEID_UNABLE               => '无可履约子订单',
        self::OFC_SUBTRADEMAP_FAILED              => '履约服务参数构建错误',
        self::OFC_PRODUCT_TYPE_INVALID            => '无效履约产品类型',

        self::OFC_CONF_QUERY_ERROR                => '履约配置查询失败',
        self::OFC_CONF_NOT_EXIST                  => '履约配置不存在',
        self::OFC_CONF_HAS_EXIST                  => '履约配置已存在',
        self::OFC_CONF_ADD_FAILED                 => '履约配置添加失败',
        self::OFC_CONF_EDIT_FAILED                => '履约配置编辑失败',
        self::OFC_CONF_COMMIT_FAILED              => '履约配置提交失败',
        self::OFC_CONF_PUB_FAILED                 => '履约配置发布失败',
        self::OFC_CONF_VALUE_ERROR                => '履约配置值错误',

        self::OFC_EXECUTE_FAIL                    => '履约执行失败',
        self::OFC_TEST_FAIL                       => '履约测试失败',


        self::OFC_CALLER_INVALID                  => '无效履约调用者',

        self::OFC_UPDATE_FAILED                   => '履约更新失败',
        self::OFC_USER_INVALID                    => '无效用户',
        self::OFC_INFO_ERROR                      => '履约信息错误',
        
        //httpdns策略中心错误码（180001 - 180999）
        self::HTTPDNS_MAP_EMPTY                   => 'HTTPDNS MAP为空',
        self::HTTPDNS_MAP_PARAM_ERROR             => 'HTTPDNS MAP参数错误',
        self::HTTPDNS_DOMAIN_STATUS_ERROR         => 'HTTPDNS 域名状态错误',
        self::HTTPDNS_STRATEGY_MAP_ERROR          => 'HTTPDNS 策略MAP格式错误',
        self::HTTPDNS_STRATEGY_TYPE_ERROR         => 'HTTPDNS 策略类型错误',
        self::HTTPDNS_SWITCH_MAP_ERROR            => 'HTTPDNS 切换MAP格式错误',
        self::HTTPDNS_WHITELIST_MAP_ERROR         => 'HTTPDNS 白名单MAP格式错误',
        self::HTTPDNS_AUTH_ERROR                  => 'HTTPDNS 授权错误',
        self::HTTPDNS_DOMAIN_NOT_EXIST            => '域名不存在',
        self::HTTPDNS_EDIT_FAIL                   => '编辑失败',
        self::HTTPDNS_ADD_FAIL                    => '添加失败',

        //订单快照服务
        self::OSS_SNAP_QUERY_ERROR                => '订单快照查询数据库失败',
        self::OSS_SNAP_NOT_EXIST                  => '订单快照不存在',
        self::OSS_SNAP_INIT_ERROR                 => '订单快照初始化操作数据库失败',
        self::OSS_SNAP_PAY_ERROR                  => '订单快照支付操作数据库失败',
        self::OSS_SNAP_CLOSED_ERROR               => '订单快照关单操作数据库失败',
        self::OSS_SNAP_STATUS_ERROR               => '订单快照状态错误',
        self::OSS_SNAP_CHECK_ERROR                => '订单快照检查错误',
        self::OSS_SNAP_LOCK_ERROR                 => '锁订单快照操作数据库失败',
        self::OSS_SNAP_UNLOCK_ERROR               => '解锁订单快照操作数据库失败',
        self::OSS_SNAP_FLAG_UPDATE_ERROR          => '订单快照事务标记更新数据库失败',

        self::DDS_QUERY_ERROR                     => '数据字典查询错误',
        self::DDS_DICT_ERROR                      => '字典数据错误',
        self::DDS_ENUM_ERROR                      => '枚举数据错误',
        self::DDS_OWNER_ERROR                     => '归属数据错误',
        self::DDS_DICT_REPEAT                     => '数据字典重复',
        self::DDS_SUBMIT_TOO_MUCH                 => '数据字典修改项过多',
        self::DDS_OWNER_HASH_BIND                 => '归属人已经绑定',
        self::DDS_OWNER_NOT_BIND                  => '不得绑定',
        self::DDS_ADD_FAIL                        => '添加失败',
        self::DDS_EDIT_FAIL                       => '编辑失败',
        self::DDS_CODE_PRODUCE_FAIL               => '代码生成失败',
        self::DDS_DOCS_PRODUCE_FAIL               => '档生成失败',
        self::DDS_OWNER_MUST_RD                   => '用户身份必须是RD',
        self::DDS_SUBMIT_REPEAT                   => '提交重复，请重新提交',
    );

    /**
     * 获取错误信息
     *
     * @param  int  $errno  异常命令号
     * @return string
     */
    public static function getErrMsg($errno) {
        if (isset(self::$errMsg[$errno])) {
            return self::$errMsg[$errno];
        }

        return '未知错误';
    }
}
