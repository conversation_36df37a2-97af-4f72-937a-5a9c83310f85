<?php

class Zb_Util_ZbServiceTools {
    const METHOD_POST           = 'post';
    const RAL_BKEY              = 123;

    const ZBCORE_LOCAL_SERV     = 'zyb-http';

    //按service和path获取ral的extra字段，供直接调用ral的请求使用
    public static function getExtra($service, $arrHeader)
    {
        //默认extra
        $extra = ['bk'=>123];

        if (!isset($arrHeader['pathinfo'])) {
            return $extra;
        }

        //zbcore模块的path特殊，走genRalExtra的按模块控制的逻辑
        //newgoodsplatform的path过长，无法配置ncm，走genRalExtra逻辑
        if ($service == 'zbcore' || $service == 'zborder' || $service == 'sposs' || $service == 'newgoodsplatform') {
            return self::genRalExtra($service, $arrHeader, true);
        }

	//path需要对"/","-"进行字符串替换，ncm和Bd_conf都无法支持"/","-"
        $path = trim(str_replace(['/', '-'], '_', $arrHeader['pathinfo']), '_');
        $service = strtolower(str_replace(['/', '-'], '_', $service));
        //service和path组成key
        $ncmKey = sprintf('apiExtra_%s_%s', $service, $path);
        $confKey = sprintf('%s_%s', $service, $path);

        $extraOrigin = Bd_Conf::getConf('/zb/api/extra/origin');
        if ($extraOrigin == 'ncm') {
            $extraInfo = Zb_Service_NCM::get(Zb_Service_NCM::APP_YIKE, 'trade', 'api', $ncmKey);
            if (!$extraInfo) {
                $extraInfo = Bd_Conf::getConf("/zb/api/apiExtra/$confKey");
            }
        }
        elseif ($extraFrom == 'local') {
            $extraInfo = Bd_Conf::getConf("/zb/api/apiExtra/$confKey");
        }

        $extra = is_array($extraInfo) ? array_merge($extra, $extraInfo) : $extra;

        return $extra;
    }

    /**
     * @param string $service
     * @param string $method
     * @param $arrParams
     * @param $arrHeader
     * @return bool
     */

    public static function requestApi ($service = 'zyb-http',$method = 'POST',$arrParams,$arrHeader,$needLog = 0) {
        ################ TraceStat begin
        $ret = Hk_Service_TraceStat::getInstance()->ralTrace($service, $method, $arrParams, 123, $arrHeader);
        /* TraceStat 修改前的方法
        $ret = ral($service, $method, $arrParams, 123, $arrHeader);
         */
        ################ TraceStat end
        Bd_Log::debug('requestApi_' . microtime(true), ral_get_errno(),['header:' => json_encode($arrHeader), 'params:' => json_encode($arrParams), 'ret:' => strval($ret)]);

        if (false === $ret) {
            $errno = ral_get_errno();
            $errmsg = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service $service connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status] params:" . json_encode($arrParams) . " header:" . json_encode($arrHeader) . "]");

            return false;
        }
        if (!is_array($ret)) {
            $ret = json_decode($ret,true);
        }
        if (!isset($ret['errNo'])) {
            return false;
        }
        $errno = intval($ret['errNo']);
        $errmsg = strval($ret['errstr']);

        if ($errno > 0) {
            Bd_Log::warning("Error:[service $service process error], Detail:[errno:$errno errmsg:$errmsg params:" . json_encode($arrParams) . " header:" . json_encode($arrHeader) . "]");

            return false;
        }

        return $ret['data'];
    }

    /**
     * 获取数组中的某列的值
     * PHP < 5.5.0  不支持array_column
     *
     * @param $input      二维数组
     * @param $column_key 取出哪列作为新数组的值
     * @param $index_key  取出哪列作为新数组的键
     * @return array
     * <AUTHOR>
     */
    public static function arrayColumn ($input,$column_key,$index_key = null) {
        if ($index_key !== null) {
            $keys = array();
            $i = 0;
            if (!empty($input) && is_array($input)) {
                foreach ($input as $row) {
                    if (array_key_exists($index_key,$row)) {
                        if (is_numeric($row [$index_key]) || is_bool($row [$index_key])) {
                            $i = max($i,(int)$row [$index_key] + 1);
                        }
                        $keys [] = trim($row [$index_key]);
                    } else {
                        $keys [] = $i++;
                    }
                }
            }
        }
        if ($column_key !== null) {
            $values = array();
            $i = 0;
            if (!empty($input) && is_array($input)) {
                foreach ($input as $row) {
                    if (is_array($row) && array_key_exists($column_key,$row)) {
                        $values [] = $row [$column_key];
                        $i++;
                    } elseif (isset($keys)) {
                        array_splice($keys,$i,1);
                    }
                }
            }
        } else {
            $values = $input ? array_values($input) : array();
        }
        if ($index_key !== null) {
            return array_combine($keys,$values);
        }

        return $values;
    }


    /**
     * 生成 Header 信息
     *
     * @param string    $uriPath    请求路径
     * @param string    $module     请求模块
     * @param string    $entity     请求实体
     * @param string    $api        请求方法
     * @return array
     */
    public static function getHeaders($uriPath, $module='', $entity='', $api='', $isNeedAuth = 0) {
        $requestId  = sprintf("%s_%d",uniqid("rpc"),time());

        $query      = array(
            'requestId' => $requestId,
            'token'     => Zb_Util_ZbServiceTools::token(),
            'caller'        => MAIN_APP,
            'tm'        => time(),
        );
        if (!empty($module)) {
            $query['module'] = $module;
        }
        if (!empty($entity)) {
            $query['entity'] = $entity;
        }
        if (!empty($api)) {
            $query['api'] = $api;
        }

        if ($isNeedAuth === 1) { // 获取鉴权

            $tmp = explode('/', $uriPath);
            $target = $tmp[1];

            $ocsUriPath = $uriPath;
            if (isset($query['module']) && isset($query['entity']) && isset($query['api'])) {
                $ocsUriPath .= '/'.$query['module'].'/'.$query['entity'].'/'.$query['api'];
            }
            $ocsInfo = Zb_Service_OCS::getToken(MAIN_APP, $target, $ocsUriPath);
            $query['ocsSign'] = $ocsInfo['ocsSign'];
            $query['tm']      = $ocsInfo['tm'];
        }


        $query = array_map('urlencode', $query); // 传输过程中，+ 会被自动解析为空格。使用 urlencode 避免该问题，+ => %2B ; 空格 => +
        $arrHeader = array(
            "XRequestApp" => MAIN_APP,
            "querystring" => http_build_query($query),
            "pathinfo" => $uriPath,
        );

        return $arrHeader;
    }


    /**
     * 调用rpc service
     * 已经记录调用日志，请不要在前端再次记录rpc相关调用日志
     *
     * @param string    $service        后端服务名称
     * @param string    $method         后端服务方法
     * @param array     $input          服务参数列表
     * @param array     $header         向后端通过httpHeader透传的数据
     * @return array
     */
    public static function call ($service, $method, array $input, array $header) {
        $service = self::getZbcoreService($service);
        $arrExtra   = self::genRalExtra($service, $header);
        $arg = array(
            "type"      => "rpc",
            "srvName"   => $service,
            "method"    => $method,
            "input"     => json_encode($input),
            "header"    => json_encode($header),
        );
        $input['raw']   = $input;
        //moat sign
        Zb_Util_MoatAuth::Sign($input, $header["querystring"], $service)
        || Bd_Log::warning(sprintf('make moat sign fail, detail: %s', json_encode($arg)));

        // ral调用后端service
        //ral_set_logid(Bd_Log::genLogID());
        ################ TraceStat begin
        $response = Hk_Service_TraceStat::getInstance()->ralTrace($service, 'POST', $input, $arrExtra, $header);
        /* TraceStat 修改前的方法
        $response = ral($service, 'POST', $input, self::RAL_BKEY, $header);
         */
        ################ TraceStat end
        if (false === $response) {
            $errno = ral_get_errno();
            $arg["errmsg"] = ral_get_error();
            $arg["httpcode"] = ral_get_protocol_code();
            $arg["output"] = self::errorResponse();
            Bd_Log::warning("rpc servcie call failed",$errno,$arg,1);
            return $arg["output"];
        }
        if (!is_array($response)) {
            $response = json_decode($response,true);
        }

        return $response;
    }

    public static function post($service, array $input,array $header) {
        return self::call($service, 'POST', $input, $header);
    }

    public static function get($service, array $input,array $header) {
        return self::call($service, 'GET', $input, $header);
    }

    public static function multiPost($service, array $input,array $header) {
        $input = array('raw'=>$input);
        //moat sign
        Zb_Util_MoatAuth::Sign($input, $header["querystring"], $service)
        || Bd_Log::warning(sprintf('make moat sign fail, detail: %s', json_encode(
            [
                'server' => $service,
                'input'  => $input,
                'header' => $header,
            ]
        )));

        return array($service, Zb_Util_ZbServiceTools::METHOD_POST, $input, Zb_Util_ZbServiceTools::RAL_BKEY, $header);
    }

    /**
     * 返回错误信息
     *
     * @return array    错误信息
     */
    public static function errorResponse() {
        return Zb_Const_ServiceError::apiError(Zb_Const_ServiceError::NETWORK_ERROR);
    }

    /**
     * 一期不支持，功能开发完成后，考虑安全问题时，完善
     *
     * @param   array       $input  传递的参数
     * @return string
     */
    public static function sign($input, $uriPath) {
        // 根据 MAIN_APP 获取 签名秘钥

        $callerModule = $input['caller'];
        $targetModule = $input['target'];
        $tm = $input['tm'];


        if (isset($input['module']) && isset($input['entity']) && isset($input['api'])) {
            $uriPath .= '/'.$input['module'].'/'.$input['entity'].'/'.$input['api'];
        }
        $uriPathToken = md5($uriPath);

        $SK = Bd_Conf::getConf("/auth/{$callerModule}_key/{$targetModule}/{$uriPathToken}");

        $sign = md5($targetModule.$callerModule.$tm.$SK);

        return $sign;
    }

    /**
     * 一期不支持，功能开发完成后，考虑安全问题时，完善
     *
     * @return string
     */
    public static function token() {
        // 根据 MAIN_APP 获取 访问token
        return '';
    }

    /**
     * 获取ral service，封装目的：本地集群调用走 local 127.0.0.1
     * 场景： 2019署秋售卖，zbcore 集群内服务间调用， 降低网络开销
     *
     * 不要随意使用！！！！！
     *
     * <AUTHOR>
     * @param string $curService
     * @return string
     */
    private static function getZbcoreService($curService) {
        // 关闭service 本地调用 @胡文华 2019-05-13 11:12:22
        return $curService;

        $service    = $curService;
        $zbcoreApps = ['zbcore', 'trade', 'listing'];
        $zbcoreServ = ['zbcore', 'zborder', 'zbtrade'] ;

//        // cron job 集群不走本地ral 调用
//        $ignoreIP   = [
//            "**************",
//            "**************",
//            "*************",
//            "**************",
//            "**************",
//            "**************",
//            "**************",
//            "*************",
//            "*************",
//            "*************",
//            "*************",
//            "*************",
//            "*************",
//            "*************",
//            "*************",
//            "*************",
//            "*************",
//            "*************"];

        $zbCoreIP = [
            //tips2
            "*************",
            //zbCore
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************",
            "**************"
        ];

        $localIp = Hk_Util_Ip::getLocalIp();
        if (!in_array($localIp, $zbCoreIP)) {
            return $service;
        }

        if (in_array(strtolower(MAIN_APP), $zbcoreApps) && in_array(strtolower($curService), $zbcoreServ)) {
            $service = self::ZBCORE_LOCAL_SERV;
        }
        ral_set_log(RAL_LOG_MODULE, defined("MAIN_APP") ? MAIN_APP : 'unknown-app');

        return $service;
    }

    //force=true时，不检验当前模块是否在上游中
    private static function genRalExtra($curService, $header, $force=false) {

        $extra      = array(
            'bk' => 123
        );

        //在ncm或conf里获取需要进行精细控制的上下游。必须上下游同时满足要求，再获取具体的超时重试控制。
        //fromAndTo必须在Bd_Conf获取，否则未安装ncm的机器会报错
        try {

            $fromAndTo = Bd_Conf::getConf("/zb/api/zbSdkExtra/zbsdkExtraFromAndTo");
            $fromArr = $fromAndTo['from'];
            $toArr = $fromAndTo['to'];
            if ($fromArr && $toArr) {
                if ((in_array(strtolower(MAIN_APP), $fromArr) || $force) && in_array(strtolower($curService), $toArr)) {
                    $zbsdkExtraInfo = Zb_Service_NCM::get(Zb_Service_NCM::APP_YIKE, 'trade', 'api', 'zbsdkExtra'.ucfirst($curService));
                    if (!$zbsdkExtraInfo) {
                        $zbsdkExtraInfo = Bd_Conf::getConf('/zb/api/zbSdkExtra/'.$curService);
                    }
                    $extra = is_array($zbsdkExtraInfo) ? array_merge($extra, $zbsdkExtraInfo) : $extra;
                }
            }
        }
        catch(Exception $e) {
            Bd_Log::warning('Caught exception: ' . ' file: ' . $e->getFile() . ' Line' . $e->getLine() . ' Message:' . $e->getMessage());
        }
        //20191122 关闭
        return $extra;

        if(empty($header)){
            return $extra;
        }

        $requestApp = $header['XRequestApp'];
        $uriPath = $header['pathinfo'];
        $queryString = $header['querystring'];


        parse_str($queryString, $queryArr);

        if(empty($queryArr)){
            return $extra;
        }

        $zbCoreApps = ['zbcore', 'trade', 'listing'];
        $zbCoreServ = ['zbcore', 'zborder', 'zbtrade', 'zbbiz'] ;

        if (!in_array(strtolower(MAIN_APP), $zbCoreApps) || !in_array(strtolower($curService), $zbCoreServ)) {
            return $extra;
        }
        $query = array_map('urldecode', $queryArr);

        // 如果是zbcore/api/api 则组装 uriPath
        //根据 uriPath 设置
        if(strcmp($uriPath, '/zbcore/api/api') === 0){
            //如果是zbcore/api/api，根据caller target entity api  设置 caller=trade&tm=1569652984&module=dar&entity=fixOrder&api=fixOrder&target=zbcore
            // /target/module/entity/api
            $uriPath = '/'.$query['target'].'/'.$query['module'].'/'.$query['entity'].'/'.$query['api'];
        }

        $uriPath = strtolower($uriPath);

        $extra      = array(
            'bk' => 123
        );

//        if (!empty($bk)) {
//            $extra['bk']    = $bk;
//        }
//
//        $bkArr = array();

        $retryArr = array(
            '/trade/api/purchase' => 1,
            '/zbcore/dar/170020/inittrade' => 1,
            '/zbcore/dar/170021/paysuccess' => 1,
            '/zbbiz/trade/gettradebizlist' => 1,
        );

        $retry      = $retryArr[$uriPath];

        if ($retry > 0) {
            $extra['retry'] = $retry;
        }


        $cTimeoutArr = array(
            '/trade/api/purchase' => 0,
            '/zbcore/dar/170020/inittrade' => 0,
            '/zbcore/dar/170021/paysuccess' => 0,
        );
        $cTimeout   = $cTimeoutArr[$uriPath];
        if ($cTimeout > 0) {
            $extra['ctimeout']  = $cTimeout;
        }

        $wTimeoutArr = array(
            '/trade/api/purchase' => 0,
            '/zbcore/dar/170020/inittrade' => 0,
            '/zbcore/dar/170021/paysuccess' => 0,
        );
        $wTimeout   = $wTimeoutArr[$uriPath];
        if ($wTimeout > 0) {
            $extra['wtimeout']  = $wTimeout;
        }

        $rTimeoutArr = array(
            '/trade/api/purchase' => 0,
            '/zbcore/dar/170020/inittrade' => 0,
            '/zbcore/dar/170021/paysuccess' => 0,
        );
        $rTimeout   = $rTimeoutArr[$uriPath];
        if ($rTimeout > 0) {
            $extra['rtimeout']  = $rTimeout;
        }

        return $extra;
    }

    /**
     * @param $skuId
     * @param $spuId
     * @param $params  其它要拼接在url上的参数
     * @return string
     */
    public static function spliceMixDetail($skuId, $spuId, $params)
    {
        $mixDetail = 'https://www.zybang.com/static/hy/yike-sell-app-vue/mix-detail.html?';
        $encode    = Hk_Util_IdCrypt::encodeQid($skuId);
        $query = array(
            'ZybHideTitle'       => 1,
            'hideNativeTitleBar' => 1,
            'hideNav'            => 1,
            'ZybBlockimage'      => 1,
            'skuId'              => $encode,
            'spuId'              => $spuId,
        );
        foreach ($params as $key => $value) {
            $query[$key] = $value;
        }
        $mixDetail .= http_build_query($query);
        return $mixDetail;
    }
}
