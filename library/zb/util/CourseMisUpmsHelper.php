<?php

/**
 * @link http://wiki.afpai.com/pages/viewpage.action?pageId=29986751
 */

/**
 * Class Zb_Util_CourseMisUpmsHelper
 * 课程后台使用
 */
class Zb_Util_CourseMisUpmsHelper
{
    const IPS_SID = "sellmis";
    const IPS_SECRET = "b12947e3df8d674908ece09770517a89";
    const IPS_DOCKER_SECRET = "57ebe3d664eb0b50b15bd4e4138b00ec";
    const IPS_PATH = "/";
    const FUDAO_SALT = "e7e9dac92afa910971352788d0a1cf37";
    const FUDAO_SALT_TEST = "1338d1211ea54c2e8833a55117da6153";
    private $_uname = '';
    private $_onlineSystemId = 39;
    private $_dockerSystemId = 2034;
    private $_systemId = 0;
    public function __construct()
    {
        //判断环境 配置sysid
        $sysId = $this->_onlineSystemId;
        //$strIdc = Bd_Conf::getConf('idc/cur');
        $strIdc = Zb_Util_Helper::getRunEnv();
        if (strtolower($strIdc) == 'test') {
            $sysId = $this->_dockerSystemId;
        }
        $this->_systemId = $sysId;
        $salt = self::FUDAO_SALT;
        $secret = self::IPS_SECRET;
        if (strtolower($strIdc) == 'test') {
            $salt = self::FUDAO_SALT_TEST;
            $secret = self::IPS_DOCKER_SECRET;
        }
        if(isset($_REQUEST['spFrom']) && $_REQUEST['spFrom'] == 'fudaoClass') {
            $token = md5($_REQUEST['spFrom'].'_'.$salt);
            if($token == $_REQUEST['token'] && isset($_REQUEST['uname'])) {
                $this->_uname = $_REQUEST['uname'];
                return true;
            }
        }
        //uname获取
        //目前只走内网验证 后期可以加上外网登录
        $ips = new Saaslib_Service_IPSV2(self::IPS_SID, $secret, self::IPS_PATH);
        $baseUserInfo = $ips->getSession();
        if (empty($baseUserInfo['uname'])) {
            //未登陆
            throw new Zb_Util_Exception(3, array('not login'));
        }

		//$this->tempUserDisable($baseUserInfo['uname']);

        $this->_uname = $baseUserInfo['uname'];
        return true;
    }

	/**
	 * 用户的登录的信息
	 * @param $arrAuthInfo
	 */
	private function tempUserDisable($uname) {

		if (!in_array($uname, [
			'yangfan05',
			'fanfujian',
			'liyixiong',
			'xueyangyang', 'haohongfan', 'zhangyang18', 'sunhaoran', 'wuheping', 'changzhiwei', 'tenggang',
			'wenlifang', 'wangjian01', 'xujinxing', 'liyixiong', 'lihaibo', 'yangwei08', 'guoyingchun', 'qiaoli', 'wangjie25', 'zengrui', 'sunshuang', 'jiangrui06', 'zhaoshuo', 'ouyangjun', 'zhangxiao', 'hanjiafeng'])) {

			throw new Zb_Util_Exception(99999999999, '系统上线维护中,请稍后重试,紧急情况可联系对应系统产品,谢谢配合');
		}
	}

    public function getUserInfo(){
        //验签
        $salt = self::FUDAO_SALT;
//        $strIdc = Bd_Conf::getConf('idc/cur');
        $strIdc = Zb_Util_Helper::getRunEnv();
        if (strtolower($strIdc) == 'test') {
            $salt = self::FUDAO_SALT_TEST;
        }
        if(isset($_REQUEST['spFrom']) && $_REQUEST['spFrom'] == 'fudaoClass') {
            $token = md5($_REQUEST['spFrom'].'_'.$salt);
            if($token == $_REQUEST['token']) {
                $baseUserInfo = array(
                    'uname' => $this->_uname,
                    'memberId' => 1,
                    'realName' => $this->_uname,
                    'phone' => 0,
                );
                $dataStr = '{"source_list":["119"],"isInner":["1","2"],"gradeId":["61","62","63","64","11","12","13","14","15","16","2","3","4","5","6","7","71"],"subjectId":["1","2","3","4","5","6","7","8","9","10","11","12","17","18","19","20","21","22","23","24","25","26","27"],"newCourseType":["33","43"],"cpuType":[],"giveNature":[],"giveCategoryId":[]}';
                $dataPrivileges = json_decode($dataStr,true);
                return array(
                    'baseUserInfo' => $baseUserInfo,
                    'dataPrivileges' => $dataPrivileges,
                );
            }
        }
        $baseUserInfo = $this->_getBaseUserInfo();
        $dataPrivileges = $this->_getDataPrivileges();
        //构造fudao dataprivileges
        return array(
            'baseUserInfo'   => $baseUserInfo,
            'dataPrivileges' => $dataPrivileges,
            //'menuPrivileges' => $menuPrivileges,
        );
    }
    /*
     * 获取基本用户信息
     */
    private function _getBaseUserInfo(){
        $pathInfo = "/upms/api/getuserinfo";    //请求接口
        $userData = self::doRequest($pathInfo, []);
        unset($userData['datetime']);
        unset($userData['platformList']);
        return $userData;
    }

    /**
     *获取数据权限
     */
    private function _getDataPrivileges(){
        //调用upms的接口获取数据权限接口
        $pathInfo = "/upms/api/getdatainfo";
        $authData = static::doRequest($pathInfo, []);
        if ($authData) {
            $formAuthData = [];
            foreach($authData['dictAuth'] as $key => $dataInfo) {
                //专题课特殊处理 权限系统不支持0值 所以用-1 但是这里专0
                if($key == 'newCourseType') {
                    foreach($dataInfo as $dataIndex => $val) {
                        if($val['value'] == -1) {
                            $dataInfo[$dataIndex]['value'] = 0;
                        }
                    }
                }
                $formAuthData[$key] = array_column($dataInfo, 'value');
            }
            return $formAuthData;
        }
        return [];
    }
    /**
     * 获取菜单权限
     */
    private function _getMenuPrivileges(){
        //调用upms的接口获取数据权限接口
        $pathInfo = "/upms/api/getmenuinfo";
        $menuAuthData = static::doRequest($pathInfo, []);
        if ($menuAuthData) {
            return $menuAuthData;
        }
        return [];
    }

    private function doRequest($pathInfo, $params)
    {
        $server = 'upms';
        //调用upms的接口获取用户权限接口
        $header = array(
            'pathinfo' => $pathInfo,
        );
        $arrParams = array(
            'systemId' => $this->_systemId,
            'uname' => $this->_uname,
        );
        $arrParams = array_merge($arrParams, $params);
        $ret = ral($server, 'POST', $arrParams, 123, $header);
        $ret = json_decode($ret, true);
        if ($ret['errNo'] == 0) {
            return $ret['data'];
        }
        throw new Zb_Util_Exception('-1003', array('errStr' => $ret['errStr']));
        return false;
    }
}
