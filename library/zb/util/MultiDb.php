<?php

/**
 * Copyright (c) 2019 zuoyebang.com, Inc. All Rights Reserved
 * @author: liu<PERSON><PERSON>@zuoyebang.com
 * @file: zb/util/MultiDb.php
 * @date: 2019/08/19
 * @file: 下午5:24
 * @desc: 提供分库的操作基类，实现到多库多表的路由
 */


/**
 * Class Zb_Util_MultiDb
 */
class Zb_Util_MultiDb extends Hk_Common_BaseMultiDao {
    /** @var int 单库模式 */
    const TYPE_DATABASE_PARTITION_SINGLE = 1;
    /** @var int 分表索引按照固定大小来进行库的索引*/
    const TYPE_DATABASE_PARTITION_MOD = 2;
    /** @var int 分表索引取模后确定分库索引*/
    const TYPE_DATABASE_PARTITION_MUL = 3;

    /** @var int 分表模式，特殊模式表示不分表， 当分表设置为此时，分库模式必需为TYPE_DATABASE_PARTITION_SINGLE*/
    const TYPE_TABLE_PARTION_SINGLE = 3;

    /** @var int 分表模式 */
    protected $_dbPartitionType = self::TYPE_DATABASE_PARTITION_SINGLE;
    /** @var string 待连接数据库配置名称前缀 如 配置为db_name, 如果经分库路由后索引为2， 最终待连接数据库配置名称将会拼接为db_name2 */
    protected $_dbNamePrefix;
    /** @var  int 待连接的数据表分库值*/
    protected $_dbPartitionNum;

    /** @var bool 标记是否可以由该类对象进行事务操作, 默认不可进行事务操作，如需操作需在实现类中设置为true */
    protected $isTransactionObj = false;

    /** @var array 缓存数据库实例的连接 */
    private static $_dbs = [];

    public function __construct() {
        parent::__construct();
    }

    public function getCntByConds($partionValue, $arrConds) {
        $this->setDb($partionValue);
        return parent::getCntByConds($partionValue, $arrConds);
    }

    public function getListByConds($partionValue, $arrConds, $arrFields, $arrOptions = NULL, $arrAppends = NULL, $strIndex = NULL) {
        $this->setDb($partionValue);
        return parent::getListByConds($partionValue, $arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
    }

    public function getRecordByConds($partionValue, $arrConds, $arrFields, $arrOptions = NULL, $arrAppends = NULL, $strIndex = NULL) {
        $this->setDb($partionValue);
        return parent::getRecordByConds($partionValue, $arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
    }


    public function insertRecords($partionValue, $arrFields, $options = NULL, $onDup = NULL){
        $this->setDb($partionValue);
        return parent::insertRecords($partionValue, $arrFields, $options, $onDup);
    }

    public function updateByConds($partionValue, $arrConds, $arrFields, $arrOptions = NULL, $arrAppends = NULL) {
        $this->setDb($partionValue);
        return parent::updateByConds($partionValue, $arrConds, $arrFields, $arrOptions, $arrAppends);
    }

    /**
     * @return bool|int
     * @throws Hk_Util_Exception
     */
    public function getInsertId(){
        if ($this->_dbPartitionType != self::TYPE_DATABASE_PARTITION_SINGLE) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "Forbidden method");
        }
        return parent::getInsertId();
    }
    
    /**
     * @return string
     * @throws Hk_Util_Exception
     */
    public function getLastSQL() {
        if ($this->_dbPartitionType != self::TYPE_DATABASE_PARTITION_SINGLE) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "Forbidden method");
        }
        return parent::getLastSQL();
    }

    /**
     * @deprecated  禁止业务中进行数据删除
     * @return int|void
     * @throws Hk_Util_Exception
     */
    public function deleteByConds($partionValue, $arrConds) {
        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "Forbidden method");
    }

    /**
     * @deprecated
     * @param string $sql
     * @return array|false|void
     * @throws Hk_Util_Exception
     */
    public function query($sql) {
        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "禁止执行Query方法");
    }

    /**
     * 包装query方法，通过分表key 来执行指定SQL
     * @param $partitionValue
     * @param $sql
     * @return mixed
     * @throws Hk_Util_Exception
     */
    public function querySpecified($partitionValue, $sql) {
        $this->setDb($partitionValue);
        $res = $this->_db->query($sql);
        return $res;
    }

    /**
     * @param $partitionValue
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function setPartionTable($partitionValue){
        $tableIndex = $this->getTableIndex($partitionValue);
        switch ($this->_partionType) {
            case self::TYPE_TABLE_PARTION_SINGLE:
                $this->_table = $this->_tableName;
                break;
            default:
                $this->_table = $this->_tableName . intval($tableIndex);
                break;
        }

        Zb_Util_Log::debug("MultiBase Set tableName : {$this->_table}");
        return true;
    }

    /**
     * 设置获取连接时的真实dbTag
     * @param $partitionValue
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function setPartitionDb($partitionValue) {
        $dbIndex = $this->getDbIndex($partitionValue);
        switch ($this->_dbPartitionType) {
            case self::TYPE_DATABASE_PARTITION_SINGLE:
                $this->_dbName = $this->_dbNamePrefix;
                break;
            default:
                $this->_dbName = $this->_dbNamePrefix . intval($dbIndex);
                break;
        }
        Zb_Util_Log::debug("MultiBase Set dbName : {$this->_dbName}");

        return true;
    }

    /**
     * 获取分表索引， 如果为单表模式时返回0
     * @param $partitionValue
     * @return int
     * @throws Hk_Util_Exception
     */
    private function getTableIndex($partitionValue) {
        switch ($this->_partionType) {
            case self::TYPE_TABLE_PARTION_MUL:
                $tableIndex = intval($partitionValue) / $this->_partionNum;
                break;
            case self::TYPE_TABLE_PARTION_MOD:
                $tableIndex = intval($partitionValue) % $this->_partionNum;
                break;
            case self::TYPE_TABLE_PARTION_SINGLE:
                $tableIndex = 0;
                break;
            default:
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "分表配置错误");
        }
        return intval($tableIndex);
    }

    /**
     * 获取分库索引，如果为单库模式时返回0
     * @param $partitionValue
     * @return int
     * @throws Hk_Util_Exception
     */
    private function getDbIndex($partitionValue) {
        $tableIndex = $this->getTableIndex($partitionValue);
        switch ($this->_dbPartitionType) {
            case self::TYPE_DATABASE_PARTITION_SINGLE:
                $dbIndex = 0;
                break;
            case self::TYPE_DATABASE_PARTITION_MOD:
                $dbIndex = intval($tableIndex % $this->_dbPartitionNum);
                break;
            case self::TYPE_DATABASE_PARTITION_MUL:
                $dbIndex = intval($tableIndex) / $this->_dbPartitionNum;
                break;
            default:
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "分库配置错误");
        }
        return intval($dbIndex);
    }

    /**
     * 根据当前partition值设定待使用数据库连接
     * @param $partitionValue
     * @throws Hk_Util_Exception
     */
    private function setDb($partitionValue) {
        $this->setPartitionDb($partitionValue);
        if (!isset(self::$_dbs[$this->_dbName])) {
            $db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
            if (empty($db)) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "get connection failed");
            }
            self::$_dbs[$this->_dbName] = $db;
            Zb_Util_Log::debug("Init DB {$this->_dbName}");
        }
        $this->_db = self::$_dbs[$this->_dbName];
    }

    /**
     * 不可在事务中使用，请在事务外使用
     * @throws Hk_Util_Exception
     * @param $partitionValue
     * @return  boolean
     */
    public  function refreshDb($partitionValue) {
        if (!Zb_Util_Helper::isCli()) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "仅可在CLI模式执行");
        }

        $this->setPartitionDb($partitionValue);
        $db = Hk_Service_Db::getDB($this->_dbName, true,   $this->_logFile, $this->_autoRotate);
        if (empty($db)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "get connection failed");
        }

        // 指定到要断开的db连接 断开连接
        if (isset(self::$_dbs[$this->_dbName])) {
            $oldDb = self::$_dbs[$this->_dbName];
            if (!empty($oldDb)) {
                $this->_db = $oldDb;
                $this->closeDB();
            }
        }

        self::$_dbs[$this->_dbName] = $db;
        Zb_Util_Log::debugEasy("Refresh_Db_Success", $this->_dbName);
        return true;
    }

    /**
     * @deprecated
     * @throws Hk_Util_Exception
     */
    public function startTransaction() {
        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "Forbidden Method");
    }

    /**
     * @deprecated
     * @throws Hk_Util_Exception
     */
    public function commit() {
        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "Forbidden Method");
    }

    /**
     * @deprecated
     * @throws Hk_Util_Exception
     */
    public function rollback() {
        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "Forbidden Method");
    }

    /**
     * 开启事务, 请注意，该方法不支持事务嵌套,如业务中可能存在事务嵌套，需自行实现功能满足事务嵌套需求
     * @param $partitionValue
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function begin($partitionValue) {
        if (!$this->isTransactionObj) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "Dao对象未开启事务功能不可调用该方法");
        }
        $this->setDb($partitionValue);
        return $this->_db->startTransaction();
    }

    /**
     * 提交事务，请注意，该方法不支持事务嵌套,如业务中可能存在事务嵌套，需自行实现功能满足事务嵌套需求
     * @param $partitionValue
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function end($partitionValue) {
        if (!$this->isTransactionObj) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "Dao对象未开启事务功能不可调用该方法");
        }
        $this->setDb($partitionValue);
        return $this->_db->commit();
    }

    /**
     * 回滚事务，请注意，该方法不支持事务嵌套,如业务中可能存在事务嵌套，需自行实现功能满足事务嵌套需求
     * @param $partitionValue
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function cancel($partitionValue) {
        if (!$this->isTransactionObj) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, "Dao对象未开启事务功能不可调用该方法");
        }
        $this->setDb($partitionValue);
        return $this->_db->rollback();
    }

    /**
     * 修复SQL中的转义
     * @param int $partitionValue
     * @param string $input
     * @return mixed
     * @throws Hk_Util_Exception
     */
    public function escapeString($partitionValue, $input)
    {
        $this->setDb($partitionValue);
        return $this->_db->escapeString($input);
    }
}