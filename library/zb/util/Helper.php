<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Helper.php
 * <AUTHOR>
 * @date   2018/2/8 下午8:32
 * @brief
 **/
 

class Zb_Util_Helper {
    public static function getRunEnv(){
        //先判断环境
        if(Hk_Util_Env::isDockerPlatform()){
            return Hk_Util_Env::getRunEnv();
        } else {
            return ral_get_idc();
        }
    }
    /**
     * 校验参数必填项工具
     *
     * @param  array $params
     * @param  array $requireKeys
     * @return bool
     */
    public static function paramsRequired($params, $requireKeys) {
        if (!is_array($params) || !is_array($requireKeys) || empty($params) || empty($requireKeys)) {
            Bd_Log::warning("Error:[params and requireKeys must array], Detail:[params:"
                . json_encode($params) . " requireKeys:" . json_encode($requireKeys) . "]");
            return false;
        }

        foreach($requireKeys as $key) {
            if (!isset($params[$key])) {
                Bd_Log::warning("Error:[param $key lost], Detail:[requireKeys:".json_encode($requireKeys)."]");
                return false;
            }
        }

        return true;
    }

    /**
     * 更新 JSON 类型的数据表字段，比如 extData 等
     *
     * @param  array $arrNew
     * @param  array $arrOld
     * @return json|bool
     */
    public static function updateJsonKVCol($arrNew, $arrOld) {
        if (!is_array($arrNew) || !is_array($arrOld)) {
            Bd_Log::warning("Error:[param must array], Detail:[arrNew:".json_encode($arrNew)." arrOld:".json_encode($arrOld)."]");
            return false;
        }

        if (empty($arrOld)) {
            return json_encode($arrNew);
        }

        foreach($arrNew as $key => $value) {
            $arrOld[$key] = $value;
        }

        return json_encode($arrOld);
    }

    /**
     * @param $add array 待替换数组数据
     * @param $base array 待被替换数组数据
     * @return false|string
     */
    public static function replaceJson($add, $base) {
        if (!is_array($add) || !is_array($base)) {
            Bd_Log::warning("Error:[param must array], Detail:[add:".json_encode($add)." base:".json_encode($base)."]");
            return false;
        }
        $base = array_replace($base, $add);
        return json_encode($base, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
    }

    /**
     * 更新 KV 类型的数据表字段，比如 array('lessonCnt'=>10,'lessonUsed'=>2,...);
     *
     * @param  array    $arrNew
     * @param  array    $arrOld
     * @return array|bool
     */
    public static function updateExtDataKVCol($arrNew, $arrOld) {
        if (!is_array($arrNew) || !is_array($arrOld)) {
            Bd_Log::warning("Error:[param must array], Detail:[arrNew:".json_encode($arrNew)." arrOld:".json_encode($arrOld)."]");
            return false;
        }

        if (empty($arrOld)) {
            return $arrNew;
        }

        foreach ($arrNew as $key => $value) {
            $arrOld[$key] = $value;
        }

        return $arrOld;
    }

    /**
     * 更新 List 类型的数据表字段，比如 array(123=>array('fee'=>100,...), ...)
     *
     * @param  array    $arrNew
     * @param  array    $arrOld
     * @return array|bool
     */
    public static function updateExtDataListCol($arrNew, $arrOld) {
        if (!is_array($arrNew) || !is_array($arrOld)) {
            Bd_Log::warning("Error:[param must array], Detail:[arrNew:".json_encode($arrNew)." arrOld:".json_encode($arrOld)."]");
            return false;
        }

        if (empty($arrOld)) {
            return $arrNew;
        }

        foreach($arrNew as $id=>$item) {
            foreach($item as $k=>$v) {
                $arrOld[$id][$k] = $v;
            }
        }

        return $arrOld;
    }


    /**
     * 二维数组按指定 Key 排序
     * @param $data
     * @param $key
     * @param int $sortType
     * @param int $sortFlag
     * @return bool
     */
    public static function multiArrSort($data, $key, $sortType=SORT_DESC, $sortFlag=SORT_REGULAR) {

        $arrSortType = array(SORT_ASC, SORT_DESC);
        $arrSortFlag = array(SORT_REGULAR, SORT_NUMERIC, SORT_STRING, SORT_LOCALE_STRING, SORT_NATURAL, SORT_FLAG_CASE);

        if (!in_array($sortType, $arrSortType)) {
            return false;
        }
        if (!in_array($sortFlag, $arrSortFlag)) {
            return false;
        }

        $arrKeyVal = array();
        foreach($data as $row) {
            if (!isset($row[$key])) {
                return false;
            }
            $arrKeyVal[] = $row[$key];
        }
        $keys = array_keys($data);
        array_multisort($arrKeyVal, $sortType, $sortFlag, $data, $keys);
        $arr = array_combine($keys, $data);
        return $arr;
    }

    /**
     * 判断当前php执行的模式是否为 cli 模式
     * @return bool
     */
    public static function isCli() {
        static $isCli;
        if (!is_null($isCli)) {
            return $isCli;
        }
        $mod = php_sapi_name();
        $isCli = strtolower($mod) == 'cli';
        return $isCli;
    }
}
