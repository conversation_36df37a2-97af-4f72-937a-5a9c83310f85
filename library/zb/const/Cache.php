<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Cache.php
 * <AUTHOR>
 * @date 2017/9/11 20:26:46
 * @brief 直播课cache前缀
 *
 **/

class Zb_Const_Cache {
    const PREFIX_KV_OUTLINE = 'PREFIX_KV_OUTLINE_VP13_'; // 大纲实体
    const PREFIX_KV_CPU     = 'PREFIX_KV_CPU_VP13_';     //课程产品实体
    const PREFIX_KV_COURSE  = 'PREFIX_KV_COURSE_VP19_';  //课程实体
    const PREFIX_KV_LESSON  = 'PREFIX_KV_LESSON_VP20_';  //章节实体
    const PREFIX_KV_SHARELESSON = 'PREFIX_KV_SHARELESSON_V2_';//章节共享关系
    const PREFIX_LIST_LESSON_SUBLESSON = 'PREFIX_KV_LESSON_SUBLESSON_V2_';//章节共享关系
    const PREFIX_LIST_OUTLINE_SUBOUTLINE = 'PREFIX_KV_OUTLINE_SUBOUTLINE_V2_';//章节共享关系
    const PREFIX_KV_SKU     = 'PREFIX_KV_SKU_VP7_RPC_';     //单品实体
    const PREFIX_KV_PRC_SKU = 'PREFIX_KV_SKU_RPC_';     //单品实体
    const PREFIX_KV_SKULIST = 'PREFIX_KV_SKULIST_'; //列表实体, 存储一个SkuList 表中一行记录
    const PREFIX_KV_STUDENT = 'PREFIX_KV_STUDENT_'; //学生实体
    const PREFIX_STU_EXT_LOCK = 'PREFIX_STU_EXT_LOCK_'; //学生扩展数据加锁
    const PREFIX_KV_TEACHER = 'PREFIX_KV_TEACHER_'; //老师实体
    const PREFIX_LIST_COURSE_LESSON  = 'PREFIX_LIST_COURSE_LESSON_VP9_'; //课程关联的章节列表
    const PREFIX_LIST_LESSON_COURSE  = 'PREFIX_LIST_LESSON_COURSE_VP9_'; //章节关联的课程列表
    const PREFIX_LIST_CPU_COURSE = 'PREFIX_LIST_CPU_COURSE_VP9_'; //课程产品关联的课程列表
    const PREFIX_LIST_OUTLINE_LESSON = 'PREFIX_LIST_OUTLINE_LESSON_VP9_'; //大纲关联的章节列表
    const PREFIX_LIST_CPU_OUTLINE = 'PREFIX_LIST_CPU_OUTLINE_VP12_'; //课程产品与大纲关联关系
    const PREFIX_LIST_TEACHER_LESSON = 'PREFIX_LIST_TEACHER_LESSON_VP10_'; //章节关联的主讲列表
    const PREFIX_LIST_TEACHER_LESSON_LIST = 'PREFIX_LIST_TEACHER_LESSON_VP9_';  // 根据老师分页获取到老师对应的章节
    const PREFIX_LIST_TEACHER_COURSE_LIST = 'PREFIX_LIST_TEACHER_COURSE_VP9_';  // 根据老师分页获取到老师对应的章节
    const PREFIX_LIST_TEACHER_COURSE_CONDS = 'PREFIX_LIST_CONDS_TEACHER_COURSE_VP9_'; // 根据搜索条件分页获取到老师对应的章节
    const PREFIX_KV_COURSE_MATERIAL_RELATION = 'PREFIX_KV_COURSE_MATERIAL_RELATION_VP9_'; // 教辅关系
    const PREFIX_KV_SHARE_LESSON_ROOM_LESSON = 'PREFIX_KV_SHARELESSON_ROOM_LESSON_NM_';//纳米-单章节共享章节列表

    // 合规上报, course与教委数据关联关系, key=skuId
    const PREFIX_LIST_COURSEGOV_SKU = 'PREFIX_LIST_COURSEGOV_SKU_V1_';

    // 合规上报, course与教委数据关联关系, key=jwCourseId
    const PREFIX_KV_COURSEGOV_JWCOURSE = 'PREFIX_KV_COURSEGOV_JWCOURSE_V1_';

    // 合规上报, course与教委数据关联关系, key=courseId
    const PREFIX_LIST_COURSEGOV_COURSE = 'PREFIX_LIST_COURSEGOV_COURSE_V1_';

    // ares 新增
    const PREFIX_LIST_SERVICE_INFO = 'PREFIX_LIST_SERVICE_INFO_VP7_'; //根据容器id 获取容器对应的服务

    const PREFIX_LIST_COURSE_CONDS         = 'PREFIX_LIST_COURSE_CONDS_VP7_'; // 根据课程信息查询课程列表

    const PREFIX_LIST_SKUID_SIMPLE          = 'PREFIX_LIST_SKUID_SIMPLE_VP7_';    //单品ID列表,(根据显示状态和分页)
    const PREFIX_LIST_SKUID_SIMPLE_KEYS     = 'PREFIX_LIST_SKUID_SIMPLE_KEYS_VP7';//单品ID列表缓存Keys, 用于删除在线ID缓存

    const PREFIX_LIST_SKUID_ONLINE          = 'PREFIX_LIST_SKUID_ONLINE_';    //在线单品ID列表,单品列表页的缓存
    const PREFIX_LIST_SKUID_ONLINE_KEYS     = 'PREFIX_LIST_SKUID_ONLINE_KEYS_VP7';//在线单品ID列表缓存Keys，批量删除缓存

    const PREFIX_LIST_SKUID_ONLINE_CNT      = 'PREFIX_LIST_SKUID_ONLINE_CNT_VP7_';    //在线单品总数量
    const PREFIX_LIST_SKUID_ONLINE_CNT_KEYS = 'PREFIX_LIST_SKUID_ONLINE_CNT_KEYS_VP7';//在线单品数量缓存Keys，批量删除缓存

    const PREFIX_LIST_SKULIST_SEARCH          = 'PREFIX_LIST_SKULIST_SEARCH_VP7_';    //通用SKULIST Search缓存
    const PREFIX_LIST_SKULIST_SEARCH_KEYS     = 'PREFIX_LIST_SKULIST_SEARCH_KEYS_VP7';//通用SKULIST Search缓存Keys，批量删除缓存

    const PREFIX_LIST_SKU_COURSEID          = 'PREFIX_LIST_SKU_COURSEID_VP7_'; //单品对应的课程ID数组
    const PREFIX_LIST_COURSE_SKUID          = 'PREFIX_LIST_COURSE_SKUID_VP7_'; //课程对应的单品ID数组

    const PREFIX_LIST_SKUID_WITHFILTER      = 'PREFIX_LIST_SKUID_WITHFILTER_VP7_';      //单品过滤条件的缓存
    const PREFIX_LIST_SKUID_WITHFBANGRADE   = 'PREFIX_LIST_SKUID_WITHBANGRADE_VP7_';      //班课单品年级的缓存
    const PREFIX_LIST_SKUID_SEASON_WITHFBANGRADE   = 'PREFIX_LIST_VP7_SKUID_SEASON_%d_WITHBANGRADE_%d';      //特殊学季班课单品年级的缓存
    const PREFIX_LIST_SKUID_WITHFILTER_KEYS = 'PREFIX_LIST_SKUID_WITHFILTER_KYES_VP7'; //过滤条件缓存的keys，批量删除
    const PREFIX_LIST_SKU_CATE_GROUP        = 'PREFIX_LIST_SKU_CATE_GROUP_VP7';//
    const PREFIX_KV_PRETRADE_SKU            = 'PREFIX_KV_PRETRADE_SKU_'; // 用户指定单品的预付订单信息 需要userId, skuId
    const PREFIX_KV_PRETRADE                = 'PREFIX_KV_PRETRADE_'; // 指定预付订单信息 preTradeId
    const PREFIX_LIST_PRETRADE              = 'PREFIX_LIST_PRETRADE_'; // 指定预付订单信息 userId
    const PREFIX_LIST_PRETRADE_ORDER        = 'PREFIX_LIST_PRETRADE_ORDER_'; // 指定预付订单信息 userId

    const PREFIX_KV_SUBTRADE                = 'PREFIX_KV_SUBTRADE_'; //指定子订单信息 subTradeId
    const PREFIX_KV_SUBTRADE_ID_SC          = 'PREFIX_KV_SUBTRADE_ID_SC_'; //学生课程订单缓存, studentUid_courseId
    const PREFIX_LIST_SUBTRADE              = 'PREFIX_LIST_SUBTRADE_'; //指定主订单的全部子订单信息 tradeId
    const PREFIX_LIST_SUBTRADE_SKU          = 'PREFIX_LIST_SUBTRADE_SKU_'; // 根据SKU ID 查询的子订单列表
    const PREFIX_LIST_SUBTRADE_ORDER        = 'PREFIX_LIST_SUBTRADE_ORDER_'; // 根据支付流水ORDER ID 查询的子订单列表
    const PREFIX_LIST_USER_HOLDING_LIST     = 'PREFIX_LIST_USER_HOLDING_LIST_'; // 根据支付流水ORDER ID 查询的子订单列表

    const PREFIX_KV_TRADE                   = 'PREFIX_KV_TRADE_'; // 单个主订单基本信息
    const PREFIX_KV_TRADE_ALL               = 'PREFIX_KV_TRADE_ALL_'; // 带有子订单的主订单基本信息
    const PREFIX_LIST_TRADE                 = 'PREFIX_LIST_TRADE_'; // 指定用户和状态的主订单列表
    const PREFIX_KV_TRADE_ORDERID           = 'PREFIX_KV_TRADE_ORDERID_'; //指定流水ID 的主订单


    // 重新设计订单缓存
    const PREFIX_LIST_TRADE_V1              = 'PREFIX_LIST_TRADE_V1_'; // 用户主订单ID列表，存储Array格式
    const PREFIX_KV_TRADE_V1                = 'PREFIX_KV_TRADE_V1_'; // 单个订单基本信息，存储Array格式
    const PREFIX_KV_SUBTRADE_V1             = 'PREFIX_KV_SUBTRADE_V1_'; // 单个子订单订单基本信息, 包括有，存储Array格式
    const PREFIX_KV_SUBTRADE_SC_STATUS_LIST = 'PREFIX_KV_SUBTRADE_SC_STATUS_LIST_'; // 用户课程id+状态维度保存的用户子订单列表 #待清理
    const PREFIX_KV_USER_COURSE_ID_SUBTRADELIST = 'PREFIX_KV_USER_COURSE_ID_SUBTRADELIST_'; // 用户课程id子订单列表缓存
    const PREFIX_KV_PRETRADE_ID_SC          = 'PREFIX_KV_PRETRADE_ID_SC_'; //学生课程订单缓存, studentUid_courseId
    const PREFIX_KV_PRETRADE_V1             = 'PREFIX_KV_PRETRADE_V1_'; // 单个预付子订单订单基本信息, 包括有，存储Array格式
    const PREFIX_LIST_PRETRADE_V1           = 'PREFIX_LIST_PRETRADE_V1_'; // 用户预付子订单订单列表, 存储Array格式
    const PREFIX_KV_SUB_STATUS_UID_CID      = 'PREFIX_KV_SUB_STATUS_UID_CID_'; // 用户课程购买状态
    const PREFIX_KV_SUB_STATUS_UID_SKUID    = 'PREFIX_KV_SUB_STATUS_UID_SKUID_'; // 用户商品购买状态


    const PREFIX_KV_COURSE_LESSON_CONVERGE  = 'PREFIX_KV_COURSE_LESSON_CONVERGE_VP7_'; // 课程章节汇聚信息
    const PREFIX_KV_COURSE_TEACHERUIDS      = 'PREFIX_KV_COURSE_TEACHERUIDS_'; // 课程主讲UIDs


    const PREFIX_KV_STUDENT_COURSE_VER          = 'PREFIX_KV_MULTI_STUDENT_COURSE_VER_'; // 用户的课程章节数据版本号

    const PREFIX_LIST_STU_COURSE_ATTENDED       = 'PREFIX_LIST_MULTI_STU_COURSE_ATTENDED_'; // 用户已上课程列表
    const PREFIX_LIST_STU_COURSE_ATTENDED_CNT       = 'PREFIX_KV_MULTI_STU_COURSE_ATTENDED_CNT_'; // 用户已上课程数量

    const PREFIX_CNT_COURSE_STU = 'PREFIX_CNT_MULTI_COURSE_STU_'; // 课程学生数量缓存
    const PREFIX_CNT_LESSON_STU = 'PREFIX_CNT_MULTI_LESSON_STU_'; // 章节学生数量缓存

    const PREFIX_LIST_STU_COURSE_BYCONDS       = 'PREFIX_LIST_MULTI_STU_COURSE_BYCONDS_'; // 根据多查询条件查询该学生的课程信息
    const PREFIX_LIST_STU_COURSE_BYCONDS_CNT       = 'PREFIX_KV_MULTI_STU_COURSE_BYCONDS_CNT_'; // 根据多查询条件统计该学生课程数量

    const PREFIX_LIST_STU_COURSE_FINISHED       = 'PREFIX_LIST_MULTI_STU_COURSE_FINISHED_'; // 用户已完成课程列表
    const PREFIX_LIST_STU_COURSE_FINISHED_CNT       = 'PREFIX_KV_MULTI_STU_COURSE_FINISHED_CNT_'; // 用户已完成课程数量

    const PREFIX_LIST_STU_COURSE_UNFINISHED     = 'PREFIX_LIST_MULTI_STU_COURSE_UNFINISHED_'; // 用户未完成课程列表
    const PREFIX_LIST_STU_COURSE_UNFINISHED_CNT     = 'PREFIX_KV_MULTI_STU_COURSE_UNFINISHED_CNT_'; // 用户未完成课程数量

    const PREFIX_LIST_STU_COURSE_UNSTART     = 'PREFIX_LIST_MULTI_STU_COURSE_UNSTART_'; // 用户未完成/进行中课程列表
    const PREFIX_LIST_STU_COURSE_UNSTART_CNT     = 'PREFIX_LIST_MULTI_STU_COURSE_UNSTART_CNT_'; // 用户未完成/进行中课程数量

    const PREFIX_LIST_STU_COURSE_VALID     = 'PREFIX_LIST_MULTI_STU_COURSE_VALID_'; // 用户有效的课程列表
    const PREFIX_LIST_STU_COURSE_TYPE      = 'PREFIX_LIST_MULTI_STU_COURSE_TYPE_'; // 用户有效的课程列表 指定课程类型
    const PREFIX_LIST_STU_COURSE_TYPE_CNT  = 'PREFIX_LIST_MULTI_STU_COURSE_TYPE_CNT_'; // 用户有效的课程列表 指定课程类型 数量

    const PREFIX_LIST_STU_COURSE_REGISTERED     = 'PREFIX_LIST_MULTI_STU_COURSE_REGISTERED_'; // 用户已报名课程列表
    const PREFIX_LIST_STU_COURSE_REGISTERED_CNT     = 'PREFIX_KV_MULTI_STU_COURSE_REGISTERED_CNT_'; // 用户已报名课程数量

    const PREFIX_LIST_STU_COURSE_FULL     = 'PREFIX_LIST_MULTI_STU_COURSE_FULL_'; // 用户全量课程列表
    const PREFIX_LIST_STU_COURSE_FULL_CNT     = 'PREFIX_KV_MULTI_STU_COURSE_FULL_CNT_'; // 用户全量课程数量

    const PREFIX_LIST_STU_COURSE_FILTER         = 'PREFIX_LIST_MULTI_STU_COURSE_FILTER_'; // 用户已完成课程列表过滤器

    const PREFIX_LIST_STU_COURSE_LAST           = 'PREFIX_LIST_MULTI_STU_COURSE_LAST_'; // 用户最近已报名课程列表
    const PREFIX_KV_STU_COURSE_LAST_CNT         = 'PREFIX_KV_MULTI_STU_COURSE_LAST_CNT_'; // 用户已报名课程数量

    const PREFIX_KV_STU_COURSE    = 'PREFIX_KV_MULTI_STU_COURSE_'; // 用户课程信息, student_uid, course_id
    const PREFIX_KV_STU_LESSON    = 'PREFIX_KV_MULTI_STU_LESSON_'; // 用户章节信息, student_uid, lesson_id

    const PREFIX_LIST_STU_LESSON_FINISHED       = 'PREFIX_LIST_MULTI_STU_LESSON_FINISHED_'; // 用户已完成章节列表
    const PREFIX_LIST_STU_LESSON_FINISHED_CNT       = 'PREFIX_KV_MULTI_STU_LESSON_FINISHED_CNT_'; // 用户已完成章节数量

    const PREFIX_LIST_STU_LESSON_UNFINISHED       = 'PREFIX_LIST_STU_LESSON_UNFINISHED_'; // 用户未完成章节列表
    const PREFIX_LIST_STU_LESSON_UNFINISHED_CNT       = 'PREFIX_KV_STU_LESSON_UNFINISHED_CNT_'; // 用户未完成章节数量

    const PREFIX_KV_CHANGECOURSE_NMQ_ABSTRACT   ='PREFIX_KV_CHANGECOURSE_NMQ_ABSTRACT_';    //调课命令重复发送过滤

    // dat 相关缓存
    const PREFIX_KV_TEACHER_COURSE_BY_COURSE = 'PREFIX_KV_TEACHER_COURSE_BY_COURSE_'; // dat主讲课程 课程索引缓存

    const ZB_MEMCACHE_SERVER_NAME           = 'zhiboke';

    // 退款流水维度数据缓存
    const PREFIX_LIST_REFUND_SUMMARY        = 'PREFIX_LIST_REFUND_SUMMARY_'; // 用户退款流水列表，存储ID，JSON格式 [123,234,...]
    const PREFIX_LIST_REFUND_SUMMARY_CL     = 'PREFIX_LIST_REFUND_SUMMARY_CL_'; // 用户退课流水列表控制，包含有CacheVersion CV
    const PREFIX_KV_REFUND_SUMMARY          = 'PREFIX_KV_REFUND_SUMMARY_'; // 用户退款流水汇总信息，JSON格式
    const PREFIX_KV_REFUND_DETAIL           = 'PREFIX_KV_REFUND_DETAIL_'; // 用户退款流水明细信息，JSON格式

    const PREFIX_KV_SPUID_SKUIDS            = 'PREFIX_KV_SPUID_SKUIDS_VP7_'; // spuId 对应下 skuIds 列表, JSON格式
    const PREFIX_KV_RPC_SKULIST             = 'PREFIX_KV_SKULIST_RPC_';      //  skuList 列表, JSON格式

     const PREFIX_KV_FIELDTAGS_FIELDID       = 'PREFIX_KV_FIELDTAGS_FIELDID_'; // fieldId 对应下 fieldTags 列表, JSON格式
    const PREFIX_KV_FIELDTAGS_FIELDKEY      = 'PREFIX_KV_FIELDTAGS_FIELDKEY_'; // fieldKey 对应下 fieldTags 列表, JSON格式
    const PREFIX_KV_CATEGORYFIELD_CATEGORYID  = 'PREFIX_KV_CATEGORYFIELD_CATEGORYID_'; //  CategoryfieldId 对应下 Categoryfield  列表, JSON格式


    // 商品服务spu/sku 缓存key
    const PREFIX_KV_GOODS_SPU_KEY  = 'PREFIX_KV_GOODS_SPU_KEY_';    //spu信息 json格式
    const PREFIX_KV_GOODS_SKU_KEY  = 'PREFIX_KV_GOODS_SKU_KEY_';    //sku信息 json格式
    const PREFIX_KV_GOODS_SPU_SKU_KEY  = 'PREFIX_KV_GOODS_SPU_SKU_KEY_';  //spu下关联的skuId

    // 新商品平台spu/sku缓存key
    const PREFIX_KV_NEW_GOODS_SPU_KEY  = 'PREFIX_KV_NEW_GOODS_SPU_KEY_';    //spu信息 json格式
    const PREFIX_KV_NEW_GOODS_SKU_KEY  = 'PREFIX_KV_NEW_GOODS_SKU_KEY_V1_';    //sku信息 json格式
    const PREFIX_KV_NEW_GOODS_SPU_SKU_KEY  = 'PREFIX_KV_NEW_GOODS_SPU_SKU_KEY_';  //spu下关联的skuId


    // Dau 缓存相关
    const PREFIX_LIST_DAU_TEACHER_CACHE_VERSION = 'PREFIX_LIST_DAU_CACHE_VERSION'; // dau 主讲老师列表缓存版本
    const PREFIX_LIST_DAU_TEACHERLIST_BY_NAME = 'PREFIX_LIST_DAU_TEACHERLIST_BY_NAME_'; // dau 主讲姓名关于列表的缓存
    const PREFIX_LIST_DAU_TEACHERLIST_BY_GRADE_SUBJECT = 'PREFIX_LIST_DAU_TEACHERLIST_BY_GRADE_SUBJECT_'; // dau 主讲老师关于年级和学科的列表
}
