<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file    Tcc.php
 * <AUTHOR>
 * @date    2019-08-02
 * @brief   TCC 资源配置
 **/
class Zb_Const_Tcc
{

    /**
     * @comment 资源服务 ral 配置名称
     **/
    public static $serviceConfig = array(
        "stock"           => array(
            "name" => "newgoodsplatform",
        ),
        "stockV2"         => array(
            "name" => "goodsservice",
        ),
        "accelerate"         => array(
            "name" => "accelerate",
        ),
        "sellTask"        => array(
            "name" => "goodsservice",
        ),
        "coupon"          => array(
            "name" => "zbcoupon",
        ),
        "oss"             => array(
            "name" => "sp_oss",
        ),
        "limitBiz"        => array(
            "name" => "zbbiz",
        ),
        "opm"             => array(
            "name" => "nmq-zhibo",
        ),
        "pay"             => array(
            "name" => "zybpay",
        ),
        "payCombine"      => array(
            "name" => "zybpay",
        ),
        "points"          => array(
            "name" => "pointmall",
        ),
        "callback"        => array(
            "name" => "nmq-zhibo",
        ),
        "payunity"        => array(
            "name" => "pay-unity",
        ),
        "ois"             => array(
            "name" => "sp_ois",
        ),
        "listing"         => array(
            "name" => "nmq-zhibo",
        ),
        'cdkey'           => array(
            'name' => 'actplat',
        ),
        "actStock"        => array(
            "name" => "zbbiz",
        ),
        "bindId"          => array(
            "name" => "sp_asc",
        ),
        'review'          => array(
            'name' => 'sp_oas',
        ),
        'ark'             => array(
            'name' => 'sp_ark',
        ),
        'refund'          => array(
            'name' => 'zybpay',
        ),
        'odc'             => array(
            'name' => 'sp_odc',
        ),
        'pandora'         => array(
            'name' => 'sp_pandora',
        ),
        "yayaPoints"      => array(
            "name" => 'kid-station',
        ),
        'distributeStock' => array( //分销
            'name' => 'actplat',
        ),
        'currency'        => array( // 积分抵扣
            'name' => 'payment-currency',
        ),
        'opmRpc'          => array( // opm直连
            'name' => 'sp_opm',
        ),
        'ossFinish'       => array( //oss快照完成
            "name" => "sp_oss",
        ),
        'payCurrency'     => array( // 支付的积分服务
            'name' => 'payment-currency',
        ),
        'bbCurrency'      => array( // 支付的积分服务
            'name' => 'payment-currency',
        ),
        'hulk'            => array(  //中央库存
            'name' => 'hulk',
        ),
        'dar'             => array(
            'name' => 'zbcore_dar',
        ),
        'ofc'             => array(
            'name' => 'zbofc',
        ),
        "cancelNmq"       => array(
            "name" => "nmq-zhibo",
        ),
        "cancelSkuNmq"       => array(
            "name" => "nmq-zhibo",
        ),
        'callbackAya'        => array(
            'name' => 'sp_aya',
        ),
        "cancelMqOrs"        => array(
            'name' => 'sp_ors',
        ),
    );

    /**
     * @comment 事务管理器(TM)包含的资源管理器(RM)的配置. 一个TM下包括一个或多个RM
     * example: array(
     *                                "purchase"                => array(                                //
     *                                事务管理器(TM)名称配置,如 下单业务是purchase
     *                                        "stock"                => array(                                //
     *                                        库存资源名称,注意和$serviceConfig下资源名称保持一致.
     *                                                "prepare"        => "xxx",                        // stock资源服务提供的
     *                                                try, confirm, cancel 3个方法
     *                      "confirm"        => "xxx",
     *                                                "cancel"        => "xxx",
     *                  ),
     *                                        "coupon"                => array(),
     *              ),
     *
     *                        )
     **/
    public static $transactionManagerPackage = array(
        "purchase"       => array(
            "oss"             => array(
                "prepare" => "/zbcore/api/api/?module=oss&entity=ordering&api=initSnap",
                "confirm" => "/zbcore/api/api/?module=oss&entity=ordering&api=commitSnap",
                "cancel"  => "/zbcore/api/api/?module=oss&entity=reverse&api=closeSnap",
            ),
            "limitBiz"        => array(
                "prepare" => "/zbbiz/tradeapi/commitbiz",
                "confirm" => "",
                "cancel"  => "/zbbiz/tradeapi/cancelbiz",
            ),
            "pay"             => array(
                "prepare" => "/pay/cashier/createpayorder",
                "confirm" => "",
                "cancel"  => "/pay/cashier/closepayorder",
            ),
            "payCombine"      => array(
                "prepare" => "/pay/cashier/createpayorderbatch",
                "confirm" => "",
                "cancel"  => "/pay/cashier/closepayorder",
            ),
            "stock"           => array(
                "prepare" => "/newgoodsplatform/goodsskuapi/locksalecnt",
                "confirm" => "",
                "cancel"  => "/newgoodsplatform/goodsskuapi/unlocksalecnt",
            ),
            "stockV2"         => array(
                "prepare" => "/goodsservice/api/stock/try",
                "confirm" => "",
                "cancel"  => "/goodsservice/api/stock/cancel",
            ),
            "accelerate"         => array(
                "prepare"	=> "",
                "confirm" => "/accelerate/instant/set",
                "cancel"	=> "",
            ),
            "sellTask"        => array(
                "prepare" => "/goodsservice/api/salegoals/try",
                "confirm" => "",
                "cancel"  => "/goodsservice/api/salegoals/cancel",
            ),
            "coupon"          => array(
                "prepare" => "/zbcoupon/api/commitcoupon",
                "confirm" => "",
                "cancel"  => "/zbcoupon/api/cancelcoupon",
            ),
            "opm"             => array(
                "prepare" => "",
                "confirm" => "/nmq",
                "cancel"  => "",
            ),
            "points"          => array(
                "prepare" => "/pointmall/points/payv30",
                "confirm" => "",
                "cancel"  => "/pointmall/points/paycancel",
            ),
            "callback"        => array(
                "prepare" => "",
                "confirm" => "/nmq",
                "cancel"  => "",
            ),
            'cdkey'           => array(
                'prepare' => '/cdkey/api/chargeoff',
                'confirm' => '',
                'cancel'  => '/cdkey/api/closeorder',
            ),
            "actStock"        => array(
                "prepare" => "/zbbiz/tradeapi/trystock",
                "confirm" => "",
                "cancel"  => "/zbbiz/tradeapi/cancelstock",
            ),
            "yayaPoints"      => array(
                "prepare" => "/kidstation/incentive/orderReduceGold",
                "confirm" => "",
                "cancel"  => "/kidstation/incentive/orderAddGold",
            ),
            'distributeStock' => array(  //分销
                "prepare" => '/afxmis/api/salethresholdlock',
                "confirm" => '',
                "cancel"  => '/afxmis/api/salethresholdunlock',
            ),
            'pandora'         => array(
                "prepare" => '/pandora/api/equity/lock',
                "confirm" => '',
                "cancel"  => '/pandora/api/equity/rollback',
            ),
            'currency'        => array(    // 积分抵扣
                "prepare" => '/currency/integral/deductioncash',
                "confirm" => '/currency/integral/deductioncashcommit',
                "cancel"  => '/currency/integral/deductioncashcancel',
            ),
            'opmRpc'          => array(
                "prepare" => "",
                "confirm" => "/opm/api/v1/register",
                "cancel"  => "",
            ),
            'payCurrency'     => array(
                'prepare' => '/currency/integral/consume',
                'confirm' => '',
                'cancel'  => '/currency/integral/refund',
            ),
            'bbCurrency'      => array(
                'prepare' => '/currency/integral/consume',
                'confirm' => '/currency/integral/consumecommit',
                'cancel'  => '/currency/integral/consumecancel',
            ),
            'callbackAya' => array(
                "prepare" => "",
                "confirm" => "/aya/mq/cmd173009",
                "cancel"  => "",
            ),
        ),
        "payNotify"      => array(
            "stock"           => array(
                "prepare" => "",
                "confirm" => "/newgoodsplatform/goodsskuapi/writeoffsalecnt",
                "cancel"  => "",
            ),
            "stockV2"         => array(
                "prepare" => "",
                "confirm" => "/goodsservice/api/stock/confirm",
                "cancel"  => "",
            ),
            "accelerate"         => array(
                "prepare"	=> "",
                "confirm" => "/accelerate/instant/set",
                "cancel"	=> "",
            ),
            "sellTask"        => array(
                "prepare" => "",
                "confirm" => "/goodsservice/api/salegoals/confirm",
                "cancel"  => "",
            ),
            "oss"             => array(
                "prepare" => "/zbcore/api/api/?module=oss&entity=order&api=lock",
                "confirm" => "/zbcore/api/api/?module=oss&entity=ordering&api=paySnap",
                "cancel"  => "/zbcore/api/api/?module=oss&entity=order&api=unlock",
            ),
            "limitBiz"        => array(
                "prepare" => "",
                "confirm" => "/zbbiz/tradeapi/commitbiz",
                "cancel"  => "",
            ),
            "opm"             => array(
                "prepare" => "",
                "confirm" => "/nmq",
                "cancel"  => "",
            ),
            "actStock"        => array(
                "prepare" => "",
                "confirm" => "/zbbiz/tradeapi/confirmstock",
                "cancel"  => "",
            ),
            'distributeStock' => array(
                "prepare" => '',
                "confirm" => '/afxmis/api/salethresholddeduct',
                "cancel"  => '',
            ),
            'pandora'         => array(
                "prepare" => '',
                "confirm" => '/pandora/api/equity/commit',
                "cancel"  => '',
            ),
            'opmRpc'          => array(
                "prepare" => "",
                "confirm" => "/opm/api/v1/resume",
                "cancel"  => "",
            ),
            'ossFinish'       => array(
                "prepare" => "",
                "confirm" => '/zbcore/api/api/?module=oss&entity=ordering&api=finishSnap',
                "cancel"  => "",
            ),
        ),
        "closeOrder"     => array(
            "oss"             => array(
                "prepare" => "/zbcore/api/api/?module=oss&entity=order&api=lock",
                "confirm" => "/zbcore/api/api/?module=oss&entity=reverse&api=closeSnap",
                "cancel"  => "/zbcore/api/api/?module=oss&entity=order&api=unlock",
            ),
            "limitBiz"        => array(
                "prepare" => "",
                "confirm" => "/zbbiz/tradeapi/cancelbiz",
                "cancel"  => "",
            ),
            "stock"           => array(
                "prepare" => "",
                "confirm" => "/newgoodsplatform/goodsskuapi/unlocksalecnt",
                "cancel"  => "",
            ),
            "stockV2"         => array(
                "prepare" => "",
                "confirm" => "/goodsservice/api/stock/cancel",
                "cancel"  => "",
            ),
            "accelerate"         => array(
                "prepare"	=> "",
                "confirm" => "/accelerate/instant/set",
                "cancel"	=> "",
            ),
            "sellTask"        => array(
                "prepare" => "",
                "confirm" => "/goodsservice/api/salegoals/cancel",
                "cancel"  => "",
            ),
            "coupon"          => array(
                "prepare" => "",
                "confirm" => "/zbcoupon/api/cancelcoupon",
                "cancel"  => "",
            ),
            "points"          => array(
                "prepare" => "",
                "confirm" => "/pointmall/points/paycancel",
                "cancel"  => "",
            ),
            "opm"             => array(
                "prepare" => "",
                "confirm" => "/nmq",
                "cancel"  => "",
            ),
            "actStock"        => array(
                "prepare" => "",
                "confirm" => "/zbbiz/tradeapi/cancelstock",
                "cancel"  => "",
            ),
            'distributeStock' => array(
                "prepare" => '',
                "confirm" => '/afxmis/api/salethresholdunlock',
                "cancel"  => '',
            ),
            'pandora'         => array(
                "prepare" => '',
                "confirm" => '/pandora/api/equity/rollback',
                "cancel"  => '',
            ),
            'currency'        => array(    // 积分
                "prepare" => '',
                "confirm" => '/currency/integral/deductioncashrefund',
                "cancel"  => '',
            ),
            'opmRpc'          => array(
                "prepare" => "",
                "confirm" => "/opm/api/v1/resume",
                "cancel"  => "",
            ),
            'ossFinish'       => array(
                "prepare" => "",
                "confirm" => '/zbcore/api/api/?module=oss&entity=ordering&api=finishSnap',
                "cancel"  => "",
            ),
            'payCurrency'     => array(
                'prepare' => '',
                'confirm' => '/currency/integral/refund',
                'cancel'  => '',
            ),
        ),
        "draw"           => array(
            "payunity" => array(
                "prepare" => "/payunity/invoice/create",
                "confirm" => "/payunity/invoice/transaction",
                "cancel"  => "/payunity/invoice/transaction",
            ),
            "ois"      => array(
                "prepare" => "",
                "confirm" => "/ois/api/create",
                "cancel"  => "",
            ),
            "listing"  => array(
                "prepare" => "",
                "confirm" => "/nmq",
                "cancel"  => "",
            ),
        ),
        'refundOrder'    => array(
            "stock"     => array(
                'prepare' => '/newgoodsplatform/goodsskuapi/locksalecnt',
                'confirm' => '/newgoodsplatform/goodsskuapi/writeoffsalecnt',
                'cancel'  => '',
            ),
            "stockV2"   => array(
                "prepare" => "/goodsservice/api/stock/try",
                "confirm" => "/goodsservice/api/stock/confirm",
                "cancel"  => "/goodsservice/api/stock/cancel",
            ),
            "sellTask"  => array(
                "prepare" => "/goodsservice/api/salegoals/try",
                "confirm" => "/goodsservice/api/salegoals/confirm",
                "cancel"  => "/goodsservice/api/salegoals/cancel",
            ),
            'coupon'    => array(
                'prepare' => '',
                'confirm' => '/zbcoupon/api/cancelcoupon',
                'cancel'  => '',
            ),
            'limitBiz'  => array(
                'prepare' => '',
                'confirm' => '/zbbiz/tradeapi/cancelbiz',
                'cancel'  => '',
            ),
            'refund'    => array(
                'prepare' => '',
                'confirm' => '/pay/refund/applybatchrefund',
                'cancel'  => '',
            ),
            'review'    => array(
                'prepare' => '/oas/flow/lockfail',
                'confirm' => '/oas/flow/confirmfail',
                'cancel'  => '/oas/flow/rollbackfail',
            ),
            'ark'       => array(
                'prepare' => '/ark/api/intercept/try',
                'confirm' => '/ark/api/intercept/commit',
                'cancel'  => '/ark/api/intercept/cancel',
            ),
            'odc'       => array(
                'prepare' => '',
                'confirm' => '/odc/flow/addcancelreason',
                'cancel'  => '',
            ),
            'currency'  => array(    // 积分
                "prepare" => '',
                "confirm" => '/currency/integral/deductioncashrefund',
                "cancel"  => '',
            ),
            'hulk'      => array(    //中央库存 虚拟预占释放
                "prepare" => '',
                "confirm" => '/hulk/api/inventory/invented/reduce',
                "cancel"  => '',
            ),
            "cancelNmq" => array(
                "prepare" => "",
                "confirm" => "/nmq",
                "cancel"  => "",
            ),
            'cancelMqOrs' => array(
                "prepare" => "",
                "confirm" => "/ors/mq/cmd174007",
                "cancel"  => "",
            ),
        ),
        'afterResend'    => array(
            "stock"   => array(
                "prepare" => "/newgoodsplatform/goodsskuapi/locksalecnt",
                "confirm" => "/newgoodsplatform/goodsskuapi/writeoffsalecnt",
                "cancel"  => "/newgoodsplatform/goodsskuapi/unlocksalecnt",
            ),
            "stockV2" => array(
                "prepare" => '/goodsservice/api/stock/try',
                "confirm" => '/goodsservice/api/stock/confirm',
                "cancel"  => '/goodsservice/api/stock/cancel',
            ),
            "bindId"  => array(
                "prepare" => "/asc/api/bindid",
                "confirm" => "",
                "cancel"  => "/asc/api/unbindid",
            ),
            "opm"     => array(
                "prepare" => "",
                "confirm" => "/nmq",
                "cancel"  => "",
            ),
            'opmRpc'          => array(
                "prepare" => "",
                "confirm" => "/opm/api/v1/register",
                "cancel"  => "",
            ),
        ),
        'interceptOrder' => array(
            "stockV2"  => array(
                "prepare" => "/goodsservice/api/stock/try",
                "confirm" => "/goodsservice/api/stock/confirm",
                "cancel"  => "/goodsservice/api/stock/cancel",
            ),
            "sellTask" => array(
                "prepare" => "/goodsservice/api/salegoals/try",
                "confirm" => "/goodsservice/api/salegoals/confirm",
                "cancel"  => "/goodsservice/api/salegoals/cancel",
            ),
            'hulk'     => array(    //中央库存 虚拟预占释放
                "prepare" => '',
                "confirm" => '/hulk/api/inventory/invented/reduce',
                "cancel"  => '',
            ),
            "dar"      => array(
                "prepare" => "/zbcore/api/api/?module=dar&entity=flow&api=lock",
                "confirm" => "/zbcore/api/api/?module=dar&entity=flow&api=intercept",
                "cancel"  => "/zbcore/api/api/?module=dar&entity=flow&api=unlock",
            ),
            "ofc"      => array(
                "prepare" => "/ofc/wms/freeze",
                "confirm" => "/ofc/wms/intercept",
                "cancel"  => "/ofc/wms/unfreeze",
            ),
            "cancelSkuNmq" => array(
                "prepare" => "",
                "confirm" => "/nmq",
                "cancel"  => "",
            ),
        ),
    );
}