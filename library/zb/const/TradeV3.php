<?php

/**
 * Class Zb_Const_TradeV3 交易3.0常量
 */
class Zb_Const_TradeV3
{
    //交易版本
    const TRADE_VERSION = 3;

    // 交易资源配置
    const TRADE_RESOURCE_STOCK		= 'stock';			//库存
    // 交易资源配置
    const TRADE_RESOURCE_STOCKV2    = 'stockV2';	    //库存
    const TRADE_RESOURCE_SELLTASK   = 'sellTask';	    //销售任务
	const TRADE_RESOURCE_ACTSTOCK	= 'actStock';		//活动库存,如秒杀活动
    const TRADE_RESOURCE_COUPON		= 'coupon';			//优惠券
    const TRADE_RESOURCE_LIMITBUY   = 'limitBiz';		//限购
    const TRADE_RESOURCE_OSS	    = 'oss';			//交易快照
    const TRADE_RESOURCE_POINTS	    = 'points';			//帮帮币或学分
    const TRADE_RESOURCE_OPM	    = 'opm';			//订单管道
    const TRADE_RESOURCE_PAY	    = 'pay';			//支付中心
    const TRADE_RESOURCE_PAYCOMBINE	    = 'payCombine';			//支付中心
    const TRADE_RESOURCE_CALLBACK   = 'callback';	    //0元支付回调
    const TRADE_RESOURCE_CALLBACK_AYA = 'callbackAya';	    //0元支付回调临时改成通过aya发mq
    const TRADE_RESOURCE_CANCEL     = 'cancelNmq';	    //174007命令
    const TRADE_RESOURCE_CANCEL_ORS = 'cancelMqOrs';	    //临时改成通过ors发mq174007命令
    const TRADE_RESOURCE_CDKEY		= 'cdkey';			// 兑换码
    const TRADE_RESOURCE_REFUND     = 'refund';			// 退款
    const TRADE_RESOURCE_OAS        = 'review';			// OAS 审核
    const TRADE_RESOURCE_ARK        = 'ark';			//履约
    const TRADE_RESOURCE_ODC        = 'odc';			//订单中心
    const TRADE_RESOURCE_YAYAPOINTS	= 'yayaPoints';		//鸭鸭积分商城
    const TRADE_RESOURCE_DISTRIBUTESTOCK = 'distributeStock';//分销库存
    const TRADE_RESOURCE_PANDORA	  = 'pandora';		//权益
    const TRADE_RESOURCE_CURRENCY	  = 'currency';	    //积分
    const TRADE_RESOURCE_OPM_RPC	  = 'opmRpc';			//订单管道rpc调用
    const TRADE_RESOURCE_OSS_FINISH	  = 'ossFinish';		//关单/支付回调 快照事务完成rpc调用
    const TRADE_RESOURCE_PAY_CURRENCY = 'payCurrency';	//帮帮识字积分商城资源 会下线
    const TRADE_RESOURCE_BB_CURRENCY  = 'bbCurrency';	//帮帮识字积分商城资源 替换payCurrency
    const TRADE_RESOURCE_PLATO      = 'plato';
    const TRADE_RESOURCE_HULK         = 'hulk';         //中央库存
    const TRADE_RESOURCE_DAR          = 'dar';          //dar 2.0订单
    const TRADE_RESOURCE_OFC          = 'ofc';          //ofc 2.0履约
    const TRADE_RESOURCE_CANCELSKU    = 'cancelSkuNmq';	//174204命令(订单双写同步)
    const TRADE_RESOURCE_ACC          = 'accelerate';   //accelerate 3.0订单加速
    //订单管道事务状态
    const OSS_TX_STATUS_INIT     = 0;  //初始态
    const OSS_TX_STATUS_DOING    = 1;  //进行态
    const OSS_TX_STATUS_FINISH   = 2;  //完成态
    const OSS_TX_STATUS_FAIL     = 3;  //失败态，应该不存在
    const OSS_TX_STATUS_INVALID  = 99; //异常态，应该不存在

    //赠品类型
    const GIFT_TYPE_NORMAL          = 1;      //赠品
    const GIFT_TYPE_COURSE          = 2;      //课程商品
    const GIFT_TYPE_YIKE_COUPON     = 3;      //一课优惠券
    const GIFT_TYPE_PLATFORM_COUPON = 4;      //平台优化券
    const GIFT_TYPE_MALL_GOODS      = 5;      //商城商品

    //支付币种
    const CURRENCY_TYPE_RMB     = 1;    //人民币
    const CURRENCY_TYPE_SCORE   = 2;    //学分
    const CURRENCY_TYPE_BBB     = 3;    //帮帮币
	const CURRENCY_TYPE_YYXZ    = 4;    //鸭鸭习字币
    const CURRENCY_TYPE_YYYW    = 5;    //鸭鸭语文币
    const CURRENCY_TYPE_YYYY    = 6;    //鸭鸭魔法石（英语）
    const CURRENCY_TYPE_BBSTAR  = 7;    //帮帮星

	//currencyTyp到priceInfo的映射
    const PRICEINFO_MAP = [
        self::CURRENCY_TYPE_RMB    => 'rmbPriceInfo',
        self::CURRENCY_TYPE_SCORE  => 'scorePriceInfo',
        self::CURRENCY_TYPE_BBB    => 'bbPriceInfo',
        self::CURRENCY_TYPE_YYXZ   => 'yyxzPriceInfo',
        self::CURRENCY_TYPE_YYYW   => 'yyywPriceInfo',
        self::CURRENCY_TYPE_YYYY   => 'stonePriceInfo',
        self::CURRENCY_TYPE_BBSTAR => 'bbStarPriceInfo',
    ];

    //订单关单OPM stage
    const OPM_STAGE_PAYCLOSE = 'waitCommitOrClose';  //支付关单阶段
    const OPM_STAGE_REVIEW   = 'waitTradeReview';  //审核阶段


    //订单快照OSS事件类型
    const OSS_EVENT_TYPE_PAYED = 2;  //支付
    const OSS_EVENT_TYPE_CLOSE = 3;   //关单

    //订单关单OPM事件类型
    const OPM_MACHINE_TYPE_ORDER            = 1;    //下单,支付，关单
    const OPM_MACHINE_TYPE_ORDER_UPGRADE    = 2;    //商城下单,支付，关单；后续通用化
    const OPM_MACHINE_TYPE_UNION            = 3;    //支付后拆单

    //OMS的saleChannel
    const OMS_SALE_CHANNLE_ID_LIST = [2, 20, 36, 40, 43, 44];

    const ORDER_CONFIRM_FROM_USER = 1;
    const ORDER_CONFIRM_FROM_KF   = 2;
    const ORDER_CONFIRM_FROM_SYS  = 3;

    public static $orderConfirmFromMap = [
        self::ORDER_CONFIRM_FROM_USER => '用户',
        self::ORDER_CONFIRM_FROM_KF   => '客服',
        self::ORDER_CONFIRM_FROM_SYS  => '系统',
    ];
}
