<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Command.php
 * <AUTHOR>
 * @date 2017/9/11 20:26:46
 * @brief 直播课nmq命令
 *
 **/

class Zb_Const_Command {
    ////核心系统命令100001-199999

    //通用类
    const COMMAND_CORE_100001 = 100001; //延迟清理缓存
    const COMMAND_CORE_100011 = 100011; //延迟清理缓存 交易3.0新增
    //教学相关
    const COMMAND_CORE_110001 = 110001; //课程上线
    const COMMAND_CORE_110002 = 110002; //课程内容更新
    const COMMAND_CORE_110003 = 110003; //课程结束（正常结束）
    const COMMAND_CORE_110004 = 110004; //课程关闭（异常关闭）
    const COMMAND_CORE_110005 = 110005; //章节结束（正常结束）
    const COMMAND_CORE_110006 = 110006; //章节重开
    const COMMAND_CORE_110007 = 110007; //章节时间调整
    const COMMAND_CORE_110008 = 110008; //章节代课
    const COMMAND_CORE_110009 = 110009; //章节关闭（异常结束）
    const COMMAND_CORE_110010 = 110010; //章节编辑
    const COMMAND_CORE_110011 = 110011; //加课
    const COMMAND_CORE_110112 = 110112; //按天分级章节履约，支持批量赠送（2020加油站计划）
    const COMMAND_CORE_110012 = 110012; //减课
    const COMMAND_CORE_110013 = 110013; //章节新增
    const COMMAND_CORE_110014 = 110014; //章节删除
    const COMMAND_CORE_110015 = 110015; //章节更换主讲
    const COMMAND_CORE_110016 = 110016; //课节数据发生变更
    const COMMAND_CORE_110017 = 110017; //老系统的加课命令
    const COMMAND_CORE_110018 = 110018; //课堂笔记
    const COMMAND_CORE_110019 = 110019; //学生资料
    const COMMAND_CORE_110020 = 110020; //章节维度数据汇聚上报
    const COMMAND_CORE_110021 = 110021; //学生课程维度数据汇聚上报
    const COMMAND_CORE_110022 = 110022; //学生章节维度数据汇聚上报
    const COMMAND_CORE_110023 = 110023; //课程产品发布
    const COMMAND_CORE_110024 = 110024; //更新课程产品
    const COMMAND_CORE_110025 = 110025; //更新大纲
    const COMMAND_CORE_110026 = 110026; //删除大纲
    const COMMAND_CORE_110028 = 110028; //删除课程产品
    const COMMAND_CORE_110029 = 110029; //变更容器服务信息
    const COMMAND_CORE_110030 = 110030; //变更容器服务信息 单个更新
    const COMMAND_CORE_110031 = 110031; //生成宠物雪球
    const COMMAND_CORE_110032 = 110032; //新课堂笔记（含班主任）
    const COMMAND_CORE_110033 = 110033; //准备上课&准备下课（2020公益课）
    const COMMAND_CORE_110034 = 110034; //浣熊新绑定练习
    const COMMAND_CORE_110035 = 110035; // 新增大纲异动
    const COMMAND_CORE_110036 = 110036; //misCourse异步
    const COMMAND_CORE_110037 = 110037; //das对外通知uid_lesson变更
    const COMMAND_CORE_110038 = 110038; //伴学信息更新
    const COMMAND_CORE_110039 = 110039; //das对外通知uid_lesson删除
    const COMMAND_CORE_800016 = 800016; //课程方批量创建信息
    const COMMAND_CORE_800017 = 800017; //共享章节关系变动
    const COMMAND_CORE_800027 = 800027; //共享章节关系变动, 内部命令, 仅dal消费
    const COMMAND_CORE_800028 = 800028; //章节共享关系延时diff报警, dal自产自销
    const COMMAND_CORE_800018 = 800018; //课程异动异步处理
    const COMMAND_CORE_800022 = 800022; //导入任务异步处理
    const COMMAND_CORE_800088 = 800088; //纳米项目, 请求roomservice失败重试

    //course
    const CMD_FUDAO_ADD_TASK_800012 = 800012; //回放生成提醒--zhangjian02

    //销售相关
    const COMMAND_CORE_120001 = 120001; //单品上架
    const COMMAND_CORE_120002 = 120002; //单品下架
    const COMMAND_CORE_120003 = 120003; //单品隐藏
    const COMMAND_CORE_120004 = 120004; //单品展现
    const COMMAND_CORE_120005 = 120005; //新商品上架
    const COMMAND_CORE_120006 = 120006; //新商品下架

    //赠品相关
    const COMMAND_CORE_121001 = 121001; //赠品发布
    const COMMAND_CORE_121002 = 121002; //赠品单品加库存

    //单品&拼团策略
    const COMMAND_CORE_121003 = 121003; //单品&拼团策略绑定

    //成品同步物流
    const COMMAND_CORE_121004 = 121004; //成品同步物流<nmq>
    const COMMAND_CORE_121005 = 121005; //成品同步物流<rmq>
    const COMMAND_CORE_121006 = 121006; //发布商品到店铺<rmq>

    //用户相关
    const COMMAND_CORE_130001 = 130001; //学生信息修改
    const COMMAND_CORE_130002 = 130002; //主讲信息修改
    const COMMAND_CORE_130003 = 130003; //用户展示真实名称


    //课中相关
    const COMMAND_CORE_140001 = 140001; //学生上线
    const COMMAND_CORE_140002 = 140002; //学生下线
    const COMMAND_CORE_140003 = 140003; //到课数据计算完毕
    const COMMAND_CORE_140004 = 140004; //学生课中互动题提交异步信令
    const COMMAND_CORE_140005 = 140005; //异步端外摸底测提交
    const COMMAND_CORE_140006 = 140006; //异步提交语音互动
    const COMMAND_CORE_140007 = 140007; //加积分(能量)
    const COMMAND_CORE_140008 = 140008; //教师端记录组表扬
    const COMMAND_CORE_140009 = 140009; //阶段测升班课程推荐

    //后台 商品相关（组合商品后台）
    const COMMAND_CORE_150001 = 150001; //添加/编辑 组合商品
    const COMMAND_CORE_150002 = 150002; //上架组合商品
    const COMMAND_CORE_150003 = 150003; //下架组合商品
    const COMMAND_CORE_150004 = 150004; //组合商品隐藏
    const COMMAND_CORE_150005 = 150005; //组合商品显示
    const COMMAND_CORE_150006 = 150006; //单独修改skulist中的grade
    const COMMAND_CORE_150007 = 150007; //后台发生调课请求


    //拉新
    const COMMAND_CORE_160001 = 160001;//助力卡使用

    //订单相关
    const COMMAND_TRADE_170001 = 170001;//旧订单同步Trade订单系统
    const COMMAND_TRADE_170002 = 170002;//旧订单退款同步Trade订单系统
    const COMMAND_TRADE_170003 = 170003;//旧支付订单写入
    const COMMAND_TRADE_170004 = 170004;//Pay支付回调Trade
    const COMMAND_TRADE_170005 = 170005;//pay支付回调course
    const COMMAND_TRADE_170006 = 170006;//pay退款回调
    const COMMAND_TRADE_170007 = 170007;//发起退款冻结fudao course订单
    const COMMAND_TRADE_170008 = 170008;//发起物流下发通知（已下线）
    const COMMAND_TRADE_170009 = 170009; // pay支付回调trade（重构版）
    const COMMAND_TRADE_170010 = 170010; // 订单支付提交完成通知
    const COMMAND_TRADE_170012 = 170012;//新系统向course系统发送初始化订单nmq
    const COMMAND_TRADE_170013 = 170013;//course接收新系统发送的nmq后发送异步订单落地信息
    const COMMAND_TRADE_170014 = 170014;//新系统回调course进行订单支付状态确认
    const COMMAND_TRADE_170015 = 170015;//订单物流地址修改成功通知
    const COMMAND_CORE_170020  = 170020;//创建订单存储
    const COMMAND_CORE_170021  = 170021;//支付成功的数据存储
    const COMMAND_CORE_170022  = 170022;//发起调课的数据存储
    const COMMAND_CORE_170023  = 170023;//发起退款的数据存储
    const COMMAND_CORE_170024  = 170024;//完成退款的数据存储
    //const COMMAND_CORE_170025  = 170025;//订单寄送地址与course、物流系统同步
    const COMMAND_CORE_170026  = 170026;//订单寄送地址数据存储
    const COMMAND_CORE_170027 = 170027;//订单通知物流派件
    const COMMAND_CORE_170028 = 170028;//订单通知物流拦截
    const COMMAND_CORE_170029 = 170029;//复制新增子订单数据写入
    const COMMAND_CORE_170030 = 170030;//订单更新优惠券信息
    const COMMAND_CORE_170031 = 170031;//更新子订单物流批次号
    const COMMAND_CORE_170032 = 170032;//退团事件通知
    const COMMAND_CORE_170033 = 170033;//成团之后，触发尾款
    const COMMAND_CORE_170034 = 170034;//更新子订单物流催单时间
    const COMMAND_CORE_170035 = 170035;//0元支付 tuan模块特殊处理
    const COMMAND_CORE_170036 = 170036;//订单关闭通知
    const COMMAND_CORE_170037 = 170037;// 退款回调
    const COMMAND_CORE_170038 = 170038;// 退款回调V2,trade通知zbAdapter
    const COMMAND_CORE_170039 = 170039;//浣熊订单同步
    const COMMAND_CORE_170040 = 170040;//浣熊支付回调
    const COMMAND_CORE_170041 = 170041;// 浣熊订单退款
    const COMMAND_CORE_170042 = 170042;// 浣熊转班同步
    const COMMAND_CORE_170043 = 170043;// 代扣下单成功
    const COMMAND_CORE_170044 = 170044;// 代扣订单取消
    const COMMAND_CORE_170045 = 170045;// 批量退款回调
    const COMMAND_CORE_170051 = 170051;//转约接口
    const COMMAND_CORE_170052 = 170052;//退款接口
    const COMMAND_CORE_170053 = 170053;//取消接口

    const COMMAND_TRADE_171001 = 171001;  //交易支付完成后异步状态机处理

    //交易3.0
    const COMMAND_TRADE_173009 = 173009; //支付成功回调
    //新交易3.0(订单3.0)
    const COMMAND_TRADE_173109 = 173109; //支付成功回调

    //交易3.0订单变更通知
    const COMMAND_ORDER_174001  = 174001;//下单
    const COMMAND_ORDER_174002  = 174002;//关单
    const COMMAND_ORDER_174003  = 174003;//支付成功回调
    const COMMAND_ORDER_174004  = 174004;//退款申请
    const COMMAND_ORDER_174005  = 174005;//退款成功

    const COMMAND_ORDER_174006  = 174006;//拆单命令
    const COMMAND_ORDER_174007  = 174007;//订单取消
    const COMMAND_ORDER_174008  = 174008;//转班命令
    const COMMAND_ORDER_174009  = 174009;//售后服务单完成 对外通知
    const COMMAND_ORDER_174109  = 174109;//售后服务单完成 对外通知
    const COMMAND_ORDER_174110  = 174110;//订单状态变更 对外通知

    // 订单同步相关命令1742xx
    const COMMAND_ASCORDER_174201       = 174201;//asc 下单
    const COMMAND_TRANSORDER_174202     = 174202;//转班 下单
    const COMMAND_EDITADDRESS_174203    = 174203;//odc 地址修改
    const COMMAND_INTERCEPT_174204      = 174204;//dar 商品行拦截

    //履约相关
    const COMMAND_OFC_176001 = 176001;//交易完成
    const COMMAND_OFC_176002 = 176002;//转班申请
    const COMMAND_OFC_176003 = 176003;//转班成功
    const COMMAND_OFC_176004 = 176004;//退款申请
    const COMMAND_OFC_176005 = 176005;//退款成功
    const COMMAND_OFC_176006 = 176006;//补寄履约

    //新履约
    const COMMAND_OFC_176011 = 176011;//交易完成
    const COMMAND_OFC_176012 = 176012;//退款申请
    const COMMAND_OFC_176013 = 176013;//退款完成
    const COMMAND_OFC_176014 = 176014;//售后补寄履约
    const COMMAND_OFC_176015 = 176015;//转班履约
    const COMMAND_OFC_176016 = 176016;//延迟履约

    // OPM相关
    const COMMAND_OPM_177001 = 177001;//OPM 管道接收消息  下单
    const COMMAND_OPM_177002 = 177002;//OPM 管道接收消息  支付成功
    const COMMAND_OPM_177003 = 177003;//OPM 管道接收消息  关单
    const COMMAND_OPM_177004 = 177004;//OPM 管道接收消息  审核
    const COMMAND_OPM_177005 = 177005;//OPM 管道接收消息  OAS审核
    const COMMAND_OPM_177006 = 177006;//OPM 管道接收消息  履约成功
    const COMMAND_OPM_177007 = 177007;//OPM 管道接收消息  拆单后子单写数据
    const COMMAND_OPM_177008 = 177008;//OPM 管道接收消息  原单拆单后继续运行命令
    const COMMAND_OPM_177009 = 177009;//OPM 管道接收消息  子单暂停后继续运行命令

    const COMMAND_OPM_177111 = 177111;//OPM 管道接收消息  售后回库
    const COMMAND_OPM_177112 = 177112;
    const COMMAND_OPM_177121 = 177121;//OPM 管道接收消息  售后退款
    const COMMAND_OPM_177122 = 177122;
    const COMMAND_OPM_177131 = 177131;//OPM 管道接收消息  售后订单
    const COMMAND_OPM_177132 = 177132;
    const COMMAND_OPM_177141 = 177141;//OPM 管道接收消息  售后赔偿
    const COMMAND_OPM_177142 = 177142;



    // 数据字典
    const COMMAND_DDS_178001 = 178001;// 文件生成

    // 数据字典通知业务方
    const COMMAND_DDS_178101 = 178101;//通知支付 - 数据字典审核失败

    // 售后
    const COMMAND_ASC_179001 = 179001;//售后进度信息更新
    const COMMAND_ASC_179002 = 179002;//售后子系统触发的自动任务

    const COMMAND_ASSC_179301 = 179301;

    const COMMAND_AFS_179501 = 179501; //通知添加到等待确收任务列表
    const COMMAND_AFS_179502 = 179502; //虚拟物品履约后通知确收

    const COMMAND_TSS_179601 = 179601; //订单改通知tss同步es
    const COMMAND_TSS_179602 = 179602; //售后服务单改通知tss同步es

    const COMMAND_TSS_179606 = 179606; //3.0售后服务单修改通知tss

    const COMMAND_TSS_179611 = 179611;//zoms订单表变更通知tss同步es

    const COMMAND_ZHILE_179901 = 179901; //批量后台处理ors
    const COMMAND_ZHILE_179902 = 179902; //批量后台处理afterplat
    const COMMAND_ZHILE_179903 = 179903; //批量后台处理查售后金额

    //微信
    const COMMAND_WEIXIN_180001 = 180001; //自动通过好友
    const COMMAND_WEIXIN_180002 = 180002; //自动通过好友
    const COMMAND_WEIXIN_180003 = 180003; //自动通过好友
    const COMMAND_WEIXIN_180004 = 180004; //自动通过好友
    const COMMAND_WEIXIN_180005 = 180005; //自动通过好友
    const COMMAND_WEIXIN_180006 = 180006; //自动话术1
    const COMMAND_WEIXIN_180007 = 180007; //自动话术1
    const COMMAND_WEIXIN_180008 = 180008; //自动话术1
    const COMMAND_WEIXIN_180009 = 180009; //自动话术1
    const COMMAND_WEIXIN_180010 = 180010; //自动话术1
    const COMMAND_WEIXIN_180011 = 180011; //自动关联后修改备注
    const COMMAND_WEIXIN_180012 = 180012; //自动关联后修改备注
    const COMMAND_WEIXIN_180013 = 180013; //自动关联后修改备注
    const COMMAND_WEIXIN_180014 = 180014; //自动关联后修改备注
    const COMMAND_WEIXIN_180015 = 180015; //自动关联后修改备注
    const COMMAND_WEIXIN_180016 = 180016; //延迟接收消息
    const COMMAND_WEIXIN_180017 = 180017; //群发消息
    const COMMAND_WEIXIN_180018 = 180018; //群发消息
    const COMMAND_WEIXIN_180019 = 180019; //群发消息
    const COMMAND_WEIXIN_180020 = 180020; //消息撤回
    const COMMAND_WEIXIN_180021 = 180021; //消息打点统计
    const COMMAND_WEIXIN_180022 = 180022; //微信生态-批量模板消息
    const COMMAND_WEIXIN_180023 = 180023; //微信生态-单个模板消息
    const COMMAND_WEIXIN_180024 = 180024; //微信生态-微信消息回调
    const COMMAND_WEIXIN_180025 = 180025; //邮件报警
    const COMMAND_WEIXIN_180027 = 180027; //异步写消息-减轻主库DB压力
    const COMMAND_WEIXIN_180028 = 180028; //督学换绑数据
    const COMMAND_WEIXIN_180029 = 180029; //班主任换绑数据
    const COMMAND_WEIXIN_180044 = 180044; //督学绑定数据
    const COMMAND_WEIXIN_180046 = 180046; //班主任绑定数据
    const COMMAND_WEIXIN_180047 = 180047; //批量绑定异步处理
    const COMMAND_WEIXIN_180030 = 180030; //事件订阅
    const COMMAND_WEIXIN_180031 = 180031; //事件订阅推送督学
    const COMMAND_WEIXIN_180032 = 180032; //自动备注[短备注]
    const COMMAND_WEIXIN_180033 = 180033; //自动备注[短备注]
    const COMMAND_WEIXIN_180034 = 180034; //自动备注[短备注]
    const COMMAND_WEIXIN_180035 = 180035; //自动备注[短备注]
    const COMMAND_WEIXIN_180036 = 180036; //自动备注[短备注]
    const COMMAND_WEIXIN_180037 = 180037; //消息通知
    const COMMAND_WEIXIN_180038 = 180038; //群聊邀请
    const COMMAND_WEIXIN_180039 = 180039; //自动回复(机器人)
    const COMMAND_WEIXIN_180040 = 180040; //删除好友（延时操作)
    const COMMAND_WEIXIN_180041 = 180041; //删除群成员（延时操作)
    const COMMAND_WEIXIN_180042 = 180042; //添加群成员（延时操作)
    const COMMAND_WEIXIN_180043 = 180043; //单个群同步（延时操作)
    const COMMAND_WEIXIN_180045 = 180045; //群操作延迟（删人/加人）

    const COMMAND_WEIXIN_180048 = 180048; //群欢迎语
    const COMMAND_WEIXIN_180049 = 180049; //群欢迎语
    const COMMAND_WEIXIN_180050 = 180050; //群欢迎语
    const COMMAND_WEIXIN_180051 = 180051; //群欢迎语
    const COMMAND_WEIXIN_180052 = 180052; //群欢迎语

    const COMMAND_WEIXIN_180053 = 180053; //督学换业务线
    const COMMAND_WEIXIN_180054 = 180054; //班主任换业务线

    const COMMAND_WEIXIN_181028 = 181028; //督学0元课换绑
    const COMMAND_WEIXIN_181044 = 181044; //督学0元课绑定
    const COMMAND_WEIXIN_181053 = 181053; //督学0元课换业务线

    const COMMAND_WEIXIN_180098 = 180098; //长连接上线
    const COMMAND_WEIXIN_180099 = 180099; //长连接下线

	const COMMAND_WEIXIN_180100 = 180100; //通用延时消息(即时)
	const COMMAND_WEIXIN_180101 = 180101; //通用延时消息1s
	const COMMAND_WEIXIN_180102 = 180102; //通用延时消息2s
	const COMMAND_WEIXIN_180103 = 180103; //通用延时消息3s
	const COMMAND_WEIXIN_180104 = 180104; //通用延时消息(s)
	const COMMAND_WEIXIN_180105 = 180105; //通用延时消息(s)
	const COMMAND_WEIXIN_180106 = 180106; //通用延时消息(s)
	const COMMAND_WEIXIN_180107 = 180107; //通用延时消息(s)
	const COMMAND_WEIXIN_180108 = 180108; //通用延时消息(s)
	const COMMAND_WEIXIN_180109 = 180109; //通用延时消息(s)
	const COMMAND_WEIXIN_180110 = 180110; //通用延时消息(s)
	const COMMAND_WEIXIN_180111 = 180111; //通用延时消息(s)
	const COMMAND_WEIXIN_180112 = 180112; //通用延时消息(s)
	const COMMAND_WEIXIN_180113 = 180113; //通用延时消息(s)
	const COMMAND_WEIXIN_180114 = 180114; //通用延时消息(s)
	const COMMAND_WEIXIN_180115 = 180115; //通用延时消息(s)
	const COMMAND_WEIXIN_180116 = 180116; //通用延时消息(s)
	const COMMAND_WEIXIN_180117 = 180117; //通用延时消息(s)
	const COMMAND_WEIXIN_180118 = 180118; //通用延时消息(s)
	const COMMAND_WEIXIN_180119 = 180119; //通用延时消息(s)
	const COMMAND_WEIXIN_180120 = 180120; //通用延时消息(s)
	const COMMAND_WEIXIN_180121 = 180121; //通用延时消息(s)
	const COMMAND_WEIXIN_180122 = 180122; //通用延时消息(s)
	const COMMAND_WEIXIN_180123 = 180123; //通用延时消息(s)
	const COMMAND_WEIXIN_180124 = 180124; //通用延时消息(s)
	const COMMAND_WEIXIN_180125 = 180125; //通用延时消息(s)
	const COMMAND_WEIXIN_180126 = 180126; //通用延时消息(s)
	const COMMAND_WEIXIN_180127 = 180127; //通用延时消息(s)
	const COMMAND_WEIXIN_180128 = 180128; //通用延时消息(s)
	const COMMAND_WEIXIN_180129 = 180129; //通用延时消息(s)
	const COMMAND_WEIXIN_180130 = 180130; //通用延时消息(s)
	const COMMAND_WEIXIN_180131 = 180131; //通用延时消息(s)
	const COMMAND_WEIXIN_180132 = 180132; //通用延时消息(s)
	const COMMAND_WEIXIN_180133 = 180133; //通用延时消息(s)
	const COMMAND_WEIXIN_180134 = 180134; //通用延时消息(s)
	const COMMAND_WEIXIN_180135 = 180135; //通用延时消息(s)
	const COMMAND_WEIXIN_180136 = 180136; //通用延时消息(s)
	const COMMAND_WEIXIN_180137 = 180137; //通用延时消息(s)
	const COMMAND_WEIXIN_180138 = 180138; //通用延时消息(s)
	const COMMAND_WEIXIN_180139 = 180139; //通用延时消息(s)
	const COMMAND_WEIXIN_180140 = 180140; //通用延时消息(s)
	const COMMAND_WEIXIN_180141 = 180141; //通用延时消息(s)
	const COMMAND_WEIXIN_180142 = 180142; //通用延时消息(s)
	const COMMAND_WEIXIN_180143 = 180143; //通用延时消息(s)
	const COMMAND_WEIXIN_180144 = 180144; //通用延时消息(s)
	const COMMAND_WEIXIN_180145 = 180145; //通用延时消息(s)
	const COMMAND_WEIXIN_180146 = 180146; //通用延时消息(s)
	const COMMAND_WEIXIN_180147 = 180147; //通用延时消息(s)
	const COMMAND_WEIXIN_180148 = 180148; //通用延时消息(s)
	const COMMAND_WEIXIN_180149 = 180149; //通用延时消息(s)
	const COMMAND_WEIXIN_180150 = 180150; //通用延时消息(s)
	const COMMAND_WEIXIN_180151 = 180151; //通用延时消息(s)
	const COMMAND_WEIXIN_180152 = 180152; //通用延时消息(s)
	const COMMAND_WEIXIN_180153 = 180153; //通用延时消息(s)
	const COMMAND_WEIXIN_180154 = 180154; //通用延时消息(s)
	const COMMAND_WEIXIN_180155 = 180155; //通用延时消息(s)
	const COMMAND_WEIXIN_180156 = 180156; //通用延时消息(s)
	const COMMAND_WEIXIN_180157 = 180157; //通用延时消息(s)
	const COMMAND_WEIXIN_180158 = 180158; //通用延时消息(s)
	const COMMAND_WEIXIN_180159 = 180159; //通用延时消息(s)
	const COMMAND_WEIXIN_180160 = 180160; //通用延时消息(s)
	const COMMAND_WEIXIN_180161 = 180161; //通用延时消息(s)
	const COMMAND_WEIXIN_180162 = 180162; //通用延时消息(s)
	const COMMAND_WEIXIN_180163 = 180163; //通用延时消息(s)
	const COMMAND_WEIXIN_180164 = 180164; //通用延时消息(s)
	const COMMAND_WEIXIN_180165 = 180165; //通用延时消息(s)
	const COMMAND_WEIXIN_180166 = 180166; //通用延时消息(s)
	const COMMAND_WEIXIN_180167 = 180167; //通用延时消息(s)
	const COMMAND_WEIXIN_180168 = 180168; //通用延时消息(s)
	const COMMAND_WEIXIN_180169 = 180169; //通用延时消息(s)
	const COMMAND_WEIXIN_180170 = 180170; //通用延时消息(s)
	const COMMAND_WEIXIN_180171 = 180171; //通用延时消息(s)
	const COMMAND_WEIXIN_180172 = 180172; //通用延时消息(s)
	const COMMAND_WEIXIN_180173 = 180173; //通用延时消息(s)
	const COMMAND_WEIXIN_180174 = 180174; //通用延时消息(s)
	const COMMAND_WEIXIN_180175 = 180175; //通用延时消息(s)
	const COMMAND_WEIXIN_180176 = 180176; //通用延时消息(s)
	const COMMAND_WEIXIN_180177 = 180177; //通用延时消息(s)
	const COMMAND_WEIXIN_180178 = 180178; //通用延时消息(s)
	const COMMAND_WEIXIN_180179 = 180179; //通用延时消息(s)
	const COMMAND_WEIXIN_180180 = 180180; //通用延时消息(s)
	const COMMAND_WEIXIN_180181 = 180181; //通用延时消息(s)
	const COMMAND_WEIXIN_180182 = 180182; //通用延时消息(s)
	const COMMAND_WEIXIN_180183 = 180183; //通用延时消息(s)
	const COMMAND_WEIXIN_180184 = 180184; //通用延时消息(s)
	const COMMAND_WEIXIN_180185 = 180185; //通用延时消息(s)
	const COMMAND_WEIXIN_180186 = 180186; //通用延时消息(s)
	const COMMAND_WEIXIN_180187 = 180187; //通用延时消息(s)
	const COMMAND_WEIXIN_180188 = 180188; //通用延时消息(s)
	const COMMAND_WEIXIN_180189 = 180189; //通用延时消息(s)
    const COMMAND_WEIXIN_180190 = 180190; //通用延时消息(s)
	const COMMAND_WEIXIN_180200 = 180200; //通用延时消息(s)
	const COMMAND_WEIXIN_180210 = 180210; //通用延时消息(s)
	const COMMAND_WEIXIN_180220 = 180220; //通用延时消息(s)
	const COMMAND_WEIXIN_180230 = 180230; //通用延时消息(s)
	const COMMAND_WEIXIN_180240 = 180240; //通用延时消息(s)
	const COMMAND_WEIXIN_180250 = 180250; //通用延时消息(s)
	const COMMAND_WEIXIN_180260 = 180260; //通用延时消息(s)
	const COMMAND_WEIXIN_180270 = 180270; //通用延时消息(s)
	const COMMAND_WEIXIN_180280 = 180280; //通用延时消息(s)
	const COMMAND_WEIXIN_180290 = 180290; //通用延时消息(s)
	const COMMAND_WEIXIN_180300 = 180300; //通用延时消息(s)
	const COMMAND_WEIXIN_180310 = 180310; //通用延时消息(s)
	const COMMAND_WEIXIN_180320 = 180320; //通用延时消息(s)
	const COMMAND_WEIXIN_180330 = 180330; //通用延时消息(s)
	const COMMAND_WEIXIN_180340 = 180340; //通用延时消息(s)
	const COMMAND_WEIXIN_180350 = 180350; //通用延时消息(s)
	const COMMAND_WEIXIN_180360 = 180360; //通用延时消息(s)
	const COMMAND_WEIXIN_180370 = 180370; //通用延时消息(s)
	const COMMAND_WEIXIN_180380 = 180380; //通用延时消息(s)
	const COMMAND_WEIXIN_180390 = 180390; //通用延时消息(s)
	const COMMAND_WEIXIN_180400 = 180400; //通用延时消息(s)
	const COMMAND_WEIXIN_180430 = 180430; //通用延时消息(s)
	const COMMAND_WEIXIN_180460 = 180460; //通用延时消息(s)

    const COMMAND_WEIXIN_180461 = 180461; //发版

	const COMMAND_WEIXIN_181100 = 181100; //通用延时消息(即时)(后端任务可能存在耗时操作)

    const COMMAND_WEIXIN_181150 = 181150; //记录退群流水信息

    //kpticker
    const COMMAND_WEIXIN_181200 = 181200; //ticker发送任务到NMQ
//    const COMMAND_WEIXIN_181201 = 181201; //ticker发送任务到NMQ：kpstaff消费
    const COMMAND_WEIXIN_181202 = 181202; //ticker发送任务到NMQ:kpactor的消息
    const COMMAND_WEIXIN_181203 = 181203; //ticker发送任务到NMQ:kpactor的消息
//    const COMMAND_WEIXIN_181204 = 181204; //ticker发送任务到NMQ:kpapi消费


    const COMMAND_WEIXIN_182100 = 182100; //微信群信息变更 用于维护Es数据
    const COMMAND_WEIXIN_182101 = 182101; //活动信息变更 暂时活动名称、isShow状态

    const COMMAND_WEIXIN_182201 = 182201; //机器人信息变更 同步es

    //企微
    const COMMAND_QYWX_182000   = 182000; //企微个人信息相关
    const COMMAND_QYWX_182001   = 182001; //企微消息相关
    const COMMAND_QYWX_182002   = 182002; //企微群相关
    const COMMAND_QYWX_182003   = 182003; //企微通讯录相关
    const COMMAND_QYWX_182004   = 182004; //企微发送群公告（检测重复群公告）
    const COMMAND_QYWX_182005   = 182005; //企微标签相关
    const COMMAND_QYWX_182006   = 182006; //企微朋友圈相关


    const COMMAND_QYWX_182099   = 182099; //企微官方事件透传

    //gateway  182300 - 182999相关 start
    const COMMAND_QYWX_182300   = 182300; //gateway 企微好友扩容
    const COMMAND_QYWX_182301   = 182301; //gateway 企微好友欢迎语
    const COMMAND_QYWX_182302   = 182302; //gateway 企微自动通过
    const COMMAND_QYWX_182303   = 182303; //gateway 企微群欢迎语
    const COMMAND_QYWX_182304   = 182304; //gateway 企微建群拉人
    const COMMAND_QYWX_182305   = 182305; //gateway 企微群违规
    const COMMAND_QYWX_182306   = 182306; //gateway 企微关键词自动回复
    const COMMAND_QYWX_182307   = 182307; //gateway 企微群发
    const COMMAND_QYWX_182308   = 182308; //gateway 企微自动反确认
    const COMMAND_QYWX_182309   = 182309; //gateway 企微群剧本
    const COMMAND_QYWX_182310   = 182310; //gateway 企微群发的批量群公告
    const COMMAND_QYWX_182311   = 182311; //gateway 企微备注
    const COMMAND_QYWX_182312   = 182312; //gateway 企微批量加好友
    const COMMAND_QYWX_182313   = 182313; //gateway 企微批量任务

    //gateway  182300 - 182999相关 end


    const COMMAND_WEIXIN_182600 = 182600; //个微批量任务

    const COMMAND_WEIXIN_182700 = 182700; //养号系统专用
    const COMMAND_WEIXIN_182701 = 182701; //养号系统专用


    const COMMAND_ENGINE_182800 = 182800; //引擎状态变更[上/下线]

	//QQ
    const COMMAND_KPQQ_183000   = 183000; //QQ个人相关
    const COMMAND_KPQQ_183001   = 183001; //QQ消息相关
    const COMMAND_KPQQ_183002   = 183002; //QQ群组相关

    //接码
    const COMMAND_KPSMS_184000   = 184000; //鲲鹏接码服务
    const COMMAND_KPSMS_184001   = 184001; //鲲鹏接码服务换绑手机号
    //鲲鹏机房运维185000 ~ 185999
    const COMMAND_WORK_ORDER_185001   = 185001; //鲲鹏接码服务换绑手机号

    //鲲鹏账号相关
    const COMMAND_KP_ACCOUNT_186001   = 186001; //账号新增
    const COMMAND_KP_ACCOUNT_186002   = 186002; //账号变更

    //设备
    const COMMAND_KPDEVICE_187000   = 187000; //设备基础
    const COMMAND_KPDEVICE_187001   = 187001; //设备屏幕
    const COMMAND_KPDEVICE_187002   = 187002; //设备上层信息变动(例如账号、机房货架号等，主要用于通知发版系统重新计算应装信息)

    //运营
    const COMMAND_YUNYING_190001 = 190001;//学生买课通知
    const COMMAND_YUNYING_190002 = 190002;//学生提交问卷通知活动

    //拉新
    const COMMAND_LAXIN_200000 = 200000; //创建线索
    const COMMAND_LAXIN_200001 = 200001; //新增例子
    const COMMAND_LAXIN_200002 = 200002; //更新例子
    const COMMAND_LAXIN_200003 = 200003; //分配tmk例子
    const COMMAND_LAXIN_200004 = 200004; //调整tmk例子
    const COMMAND_LAXIN_200005 = 200005; //分配sc例子
    const COMMAND_LAXIN_200006 = 200006; //调整sc例子
    const COMMAND_LAXIN_200007 = 200007; //例子失效
    const COMMAND_LAXIN_200008 = 200008; //例子转化
    const COMMAND_LAXIN_200009 = 200009; //SC例子转化
    const COMMAND_LAXIN_200010 = 200010; //SC&Tmk例子失效
    const COMMAND_LAXIN_200011 = 200011; //Tmk例子到课转化
    const COMMAND_LAXIN_200012 = 200012; //绩效用户维度目标pv通知
    const COMMAND_LAXIN_200013 = 200013; //绩效SC维度通知
    const COMMAND_LAXIN_200014 = 200014; //客户转化通知
    const COMMAND_LAXIN_200015 = 200015; //同步LEC云学堂组织关系
    const COMMAND_LAXIN_200016 = 200016; //LEC绩效通知
    const COMMAND_LAXIN_200020 = 200020; //例子转化-发送到绩效模块
    const COMMAND_LAXIN_200022 = 200022; //用户完成体验
    const COMMAND_LAXIN_200023 = 200023; //用户进入教室
    const COMMAND_LAXIN_200024 = 200024; //用户离开教室
    const COMMAND_LAXIN_200025 = 200025; //LEC状态变更
    const COMMAND_LAXIN_200026 = 200026; //LEC线索成单

    const COMMAND_LAXIN_200030 = 200030; // 例子渠道线索
    const COMMAND_LAXIN_200031 = 200031; // 用户0元购课

    const COMMAND_LAXIN_200032 = 200032; // LEC备注手机号

    const COMMAND_LAXIN_200040 = 200040; // 学习报告推送
    const COMMAND_LAXIN_200050 = 200050; //新增通话数据
    const COMMAND_LAXIN_200051 = 200051; //回传通话数据
    const COMMAND_LAXIN_200052 = 200052; //自动外呼通话完成
    const COMMAND_LAXIN_200053 = 200053; //通话录音文件同步
    const COMMAND_LAXIN_200054 = 200054; //NLP录音文字分析完成

    const COMMAND_LAXIN_200060 = 200060; //tmk给例子约课
    const COMMAND_LAXIN_200061 = 200061; //用户愿加微信
    const COMMAND_LAXIN_200062 = 200062; //用户已加微信
    const COMMAND_LAXIN_200063 = 200063; //人员组织架构调整

    const COMMAND_LAXIN_200066 = 200066; //督学更换资产使用人
    const COMMAND_LAXIN_200064 = 200064; //人员微信资产变更
    const COMMAND_LAXIN_200065 = 200065; //LPC负责的课程转移
    const COMMAND_LAXIN_200070 = 200070; //需要智能催到课的用户
    const COMMAND_LAXIN_200071 = 200071; //智能催到课用户课程信息
    const COMMAND_LAXIN_200072 = 200072; //laxincore收敛新建章节命令 下发给子模块
    const COMMAND_LAXIN_200073 = 200073; //laxincore收敛修改章节命令 下发给子模块
    const COMMAND_LAXIN_200074 = 200074; //督学lpc微信信息修改
    const COMMAND_LAXIN_200075 = 200075; //用户课程信息发生变更通知阿卡流斯
    const COMMAND_LAXIN_200076 = 200076;//发送email运力调
    const COMMAND_LAXIN_200077 = 200077;//发送短信运力调度
    const COMMAND_LAXIN_200078 = 200078;//智能外呼调度任务
    const COMMAND_LAXIN_200079 = 200079;//智能外呼发送短信

    const COMMAND_LAXIN_200080 = 200080;//算法工程微信消息回调
    const COMMAND_LAXIN_200081 = 200081;//同步云学堂组织关系
    const COMMAND_LAXIN_200082 = 200082;//智能催到骚扰意图识别
    const COMMAND_LAXIN_200083 = 200083;//暴力催加微
    const COMMAND_LAXIN_200084 = 200084;//例子调配通知批改系统
    const COMMAND_LAXIN_200085 = 200085;//LPC的不同微信间例子调配
    const COMMAND_LAXIN_200086 = 200086;//智能外呼用户事件通知
    const COMMAND_LAXIN_200087 = 200087;//智能外呼用户事件通知

    const COMMAND_LAXIN_200090 = 200090; //用户添加LPC微信

    //浣熊到课相关
    const COMMAND_CORE_200101 = 200101; //浣熊学生上线
    const COMMAND_CORE_200102 = 200102; //浣熊学生下线
    const COMMAND_CORE_200122 = 200122; //浣熊学生到课命令

    //拉新算法工程atom项目占位 200201-200210
    const COMMAND_ATOM_200201 = 200201;
    const COMMAND_ATOM_200202 = 200202;

    // 班主任
    const COMMAND_ASSISTANT_210001 = 210001; // 班主任更换业务账号
    const COMMAND_ASSISTANT_210002 = 210002; // 班主任新增业务账号
    const COMMAND_ASSISTANT_210003 = 210003; // 班主任封号、解封账号
    const COMMAND_ASSISTANT_210004 = 210004; // 业务账号更换使用者
    const COMMAND_ASSISTANT_210005 = 210005; // 人员管理班主任账号封禁、解封
    const COMMAND_ASSISTANT_210006 = 210006; // 炫星对接消息通知
    const COMMAND_ASSISTANT_210007 = 210007; // 账号维度新增、编辑
    const COMMAND_ASSISTANT_211000 = 211000; // 更换班主任
    const COMMAND_ASSISTANT_211001 = 211001; // 没有学生类型
    const COMMAND_ASSISTANT_211002 = 211002; // 没有学生类型,通知大数据组
    const COMMAND_ASSISTANT_211003 = 211003; // 维护老生原班关系
    const COMMAND_ASSISTANT_211004 = 211004; // 原班续报关系通知billing
    const COMMAND_ASSISTANT_211005 = 211005; // 课程绑定解绑辅导老师
    const COMMAND_ASSISTANT_211006 = 211006; // 分小班
    const COMMAND_ASSISTANT_212000 = 212000; // 排灌班邮件
    const COMMAND_ASSISTANT_213000 = 213000; // 213段班主任批改系统使用，213000 测试系统callback
    const COMMAND_ASSISTANT_213001 = 213001; // 213段班主任批改系统使用，213001 兼职接收批改数据命令
    const COMMAND_ASSISTANT_213002 = 213002; // 213段班主任批改系统使用，213002 下发物料更新信令，acls接收
    const COMMAND_ASSISTANT_213101 = 213101; // 213段班主任批改系统使用，213101 兼职作业批改中改为待批改
    const COMMAND_ASSISTANT_214000 = 214000; //拆分微信生态1881007命令，直播公众号粉丝取关关注
    const COMMAND_ASSISTANT_214001 = 214001; //挽单发送短息消息
    const COMMAND_ASSISTANT_214002 = 214002; //挽单发送站内通知消息
    const COMMAND_ASSISTANT_214003 = 214003; //挽单发送钉钉消息
    const COMMAND_ASSISTANT_214004 = 214004; //挽单分配辅导老师任务

    // 215000 - 215999 是跟课服务使用
    const COMMAND_ASSISTANT_215000 = 215000; // 跟课学生列表统计业务访问次数

    const COMMAND_ASSISTANT_216002 = 216002;//资产账号基本信息编辑
    const COMMAND_ASSISTANT_216003 = 216003;//资产账号换绑/解绑时发送
    const COMMAND_ASSISTANT_216100 = 216100;//增加真人帐号

    // 217000 - 217999 触达服务使用
    const COMMAND_TOUCHMIS_217001 = 217001;// lpc工作台灌班后催加微
    const COMMAND_TOUCHMIS_217002 = 217002;//lpc和辅导实时外呼接
    const COMMAND_TOUCHMIS_217003 = 217003;//实时外呼回调状态数据使用
    const COMMAND_TOUCHMIS_217004 = 217004;//实时外呼回调结果数据使用
    const COMMAND_TOUCHMIS_217005 = 217005;//daa服务接收排班灌班消息
    const COMMAND_TOUCHMIS_217006 = 217006;//lpcmsg-ivr外呼记录回调
    const COMMAND_TOUCHMIS_217100 = 217100;//触达服务批量短信
    const COMMAND_TOUCHMIS_217200 = 217200;//通知触达服务自动触达

    // 列表相关
    const COMMAND_LISTING_230001 = 230001; // 列表系统接收列表更新数据
    const COMMAND_LISTING_230002 = 230002; // dar接收状态更新信息然后发送列表更新数据
    const COMMAND_LISTING_230003 = 230003; // 列表系统接收列表更新数据新方式
    const COMMAND_LISTING_230004 = 230004; // 列表系统接收列表更新发票状态
    // 订单通知和锁单的MQ
    const COMMAND_ORDER_230005 = 230005; // 订单暂停状态释放到退款 通知订单变动
    const COMMAND_LOPS_230006 = 230006; // 锁单状态变更通知
    const COMMAND_LOPS_230007 = 230007; // 锁单可锁单通知

    // 商品策略 240000 ~ 240099
    const COMMAND_PRODUCT_240001 = 240001; // 更新优惠券库存
    //物流系统 300001 ~ 399999
    // 发网
    const COMMAND_WMS_300001  = 300001;   // 同步商品信息到發網
    const COMMAND_WMS_300002  = 300002;   // 同步退貨信息到發網
    const COMMAND_WMS_300003  = 300003;   // 同步包裹信息到仓库(京东)
    const COMMAND_WMS_300014  = 300014;   // 同步包裹信息到仓库(其他)
    const COMMAND_WMS_300015  = 300015;   // 菜鸟订阅包裹状态
    const COMMAND_WMS_300004  = 300004;   // 同步调拨单据到发网
    const COMMAND_WMS_300005  = 300005;   // 本地仓库处理包裹到第三方物流公司
    const COMMAND_WMS_300006  = 300006;   // 同步报损出库到发网
    const COMMAND_WMS_300007  = 300007;   // 同步手动出库到发网
    const COMMAND_WMS_300008  = 300008;   // 同步单据取消到发网
    const COMMAND_WMS_300009  = 300009;   // 物流状态回调
    const COMMAND_WMS_300010  = 300010;   // 库存信息回调
    const COMMAND_WMS_300011  = 300011;   // 物资修改回调
    const COMMAND_WMS_300012  = 300012;   // 包裹状态更新
    const COMMAND_WMS_300013  = 300013;   // 取消包裹的退货单
    const COMMAND_WMS_300016  = 300016;   // 订单状态更新
    const COMMAND_WMS_300017  = 300017;   // 库存操作complete
    const COMMAND_WMS_300018  = 300018;   // 库存操作canceled
    const COMMAND_WMS_300020  = 300020;   // 库存变更通知回调（销售层使用）
    const COMMAND_WMS_300021  = 300021;   // 同步退货入库单到易库
    const COMMAND_WMS_300022  = 300022;   // 第三方库内状态回调通知
    const COMMAND_WMS_300023  = 300023;   // 第三方入库单详情回调通知
    const COMMAND_WMS_300024  = 300024;   // 库存变更通知回调（销售层使用）
    const COMMAND_WMS_300025  = 300025;   // 异步发货出库库存操作
    const COMMAND_WMS_300026  = 300026;   // 异步落单
    const COMMAND_WMS_300027  = 300027;   // 领用申请单合单
    const COMMAND_WMS_300028  = 300028;   // 异步批量落单
    const COMMAND_WMS_300029  = 300029;   // 备用锁定库存
    const COMMAND_WMS_300043  = 300043;   // 生产履约单更新
    const COMMAND_WMS_300044  = 300044;   // 异步发送nmq通知物流履约接单
    const COMMAND_WMS_300045  = 300045;   // 异步接收订单状态变更
    const COMMAND_WMS_300047  = 300047;   // 异步处理采购入库单
    const COMMAND_WMS_300048  = 300048;   // 异步中央库存操作撤销
    const COMMAND_WMS_300049  = 300049;   // wms异步记录业务日志
    const COMMAND_WMS_300050  = 300050;   // wms异步打包处理
    const COMMAND_WMS_300033  = 300033;   // wms异步同步nova订单

    const COMMAND_CHANNEL_500001 = 500001; // 渠道对接命令号
    const COMMAND_CHANNEL_500002 = 500002; // 广告投放命令号
    const COMMAND_CHANNEL_500003 = 500003; // 渠道登录线索命令号(由平台发送，Hk_Const_Command::CMD_LAXIN，仅占位)
    const COMMAND_CHANNEL_500004 = 500004; // 交易完成通知投放命令(发送到nmq-zhibo集群，拉新投放服务)
    const COMMAND_CHANNEL_500005 = 500005; // 渠道发券异步通知命令
    const COMMAND_CHANNEL_500006 = 500006; // 渠道下单命令
    const COMMAND_CHANNEL_500007 = 500007; // 渠道小程序二维码
    const COMMAND_CHANNEL_500018 = 500018; // 渠道微信code命令号

    //阿拉丁相关
    const COMMAND_XENG_600001 = 600001; // 阿拉丁语音合成
    const COMMAND_XENG_600002 = 600002; // 试卷提交
    const COMMAND_XENG_600004 = 600004; //提交练一练后推题
    const COMMAND_XENG_600006 = 600006;

    const COMMAND_XENG_600005 = 600005; // 更新未完成环节

    //鸭鸭英语占位   610001-619999
    const COMMAND_DIYOU_610007 = 610007; // 用户注销
    const COMMAND_DIYOU_610008 = 610008; // 微信服务消息
    const COMMAND_DIYOU_610009 = 610009; // 十分钟分钟延迟任务

    //business 商业活动占位 620001-629999
    const COMMAND_BUSINESS_620001 = 620001; // 商业活动游戏pk答题延时1s
    const COMMAND_BUSINESS_620002 = 620002; // 商业活动游戏pk答题延时2s
    const COMMAND_BUSINESS_620003 = 620003; // 商业活动游戏pk答题延时3s
    const COMMAND_BUSINESS_620004 = 620004; // 商业活动游戏pk答题延时4s
    const COMMAND_BUSINESS_620005 = 620005; // 商业活动游戏pk答题延时5s
    const COMMAND_BUSINESS_620006 = 620006; // 商业活动游戏pk答题延时6s
    const COMMAND_BUSINESS_620007 = 620007; // 商业活动游戏pk答题延时7s
    const COMMAND_BUSINESS_620008 = 620008; // 商业活动游戏pk答题延时8s
    const COMMAND_BUSINESS_620009 = 620009; // 商业活动游戏pk答题延时9s
    const COMMAND_BUSINESS_620010 = 620010; // 商业活动游戏pk答题延时10s
    const COMMAND_BUSINESS_620011 = 620011; // 商业活动游戏pk答题延时11s
    const COMMAND_BUSINESS_620012 = 620012; // 商业活动游戏pk答题延时12s
    const COMMAND_BUSINESS_620013 = 620013; // 商业活动游戏pk答题延时13s
    const COMMAND_BUSINESS_620014 = 620014; // 商业活动游戏pk答题延时14s
    const COMMAND_BUSINESS_620101 = 620101; // 商业活动游戏pk 下一轮控制 第一轮 16秒
    const COMMAND_BUSINESS_620102 = 620102; // 商业活动游戏pk 下一轮控制 第二轮 14秒
    const COMMAND_BUSINESS_620103 = 620103; // 商业活动游戏pk 下一轮控制 第三轮 14秒
    const COMMAND_BUSINESS_620104 = 620104; // 商业活动游戏pk 下一轮控制 第四轮 14秒
    const COMMAND_BUSINESS_620105 = 620105; // 商业活动游戏pk 下一轮控制 第五轮 14秒
    const COMMAND_BUSINESS_621001 = 621001; // 商业活动游戏pk作答异步入库
    const COMMAND_BUSINESS_621002 = 621002; // 商业活动游戏pk结算异步入库
    const COMMAND_BUSINESS_621003 = 621003; // 商业活动游戏pk更新排行榜
    const COMMAND_BUSINESS_621004 = 621004; // 商业活动游戏金币LOG变化异步入库
    const COMMAND_BUSINESS_621005 = 621005; // 商业活动游戏用户信息变化入库

    const COMMAND_TASK_633619     = 633619;//小鹿完成巩固练习


    // 支付
    const COMMAND_PAY_250001 = 250001; // 学币，一网通支付回调
    const COMMAND_PAY_250002 = 250002; // 微信代扣, 签约|解约
    const COMMAND_PAY_250003 = 250003; // 学币模拟第三方发起支付通知
    const COMMAND_PAY_250004 = 250004; // 支付通知业务方
    const COMMAND_PAY_250005 = 250005;//支付系统业务流水
    const COMMAND_PAY_250006 = 250006;//支付回调一课业务线
    const COMMAND_PAY_250007 = 250007;//支付回调平台业务线
    const COMMAND_PAY_250008 = 250008;//支付回调它业务线
    const COMMAND_PAY_250009 = 250009;// 退款回调业务线
    const COMMAND_PAY_250010 = 250010;// 批量退款回调业务线
    const COMMAND_PAY_250011 = 250011;//苹果凭证上报重试
    const COMMAND_PAY_250012 = 250012;//微信支付客诉推送
    const COMMAND_PAY_250013 = 250013;//支付资金相关事件
    const COMMAND_PAY_250014 = 250014;//积分发放
    const COMMAND_PAY_250015 = 250015;//积分通用
    const COMMAND_PAY_250016 = 250016;//招行间连异步查询
    const COMMAND_PAY_STB_250025 = 250025;//台账对账
    const COMMAND_PAY_250030 = 250030;// 退款系统单笔回调业务线
    const COMMAND_PAY_250031 = 250031;// 退款系统批量回调业务线
    const COMMAND_PAY_250032 = 250032;// 渠道退款

    //消息中心
    const COMMAND_NOTIFIER_260001 = 260001;//消息异步处理

    //zoms
    const COMMAND_ZOMS_261001 = 261001;//下单结果通知

    //订单聚合系统
    const COMMAND_ONE_270001 = 270001; // 订单系统 和 售后系统 向 聚合系统传递信息使用

    //lpcleads服务
    const COMMAND_LPC_280001 = 280001;
    const COMMAND_LPC_280002 = 280002;
    const COMMAND_LPC_280005 = 280005;
    const COMMAND_LPC_280006 = 280006;
    const COMMAND_LPC_280009 = 280009;
    const COMMAND_LPC_280010 = 280010;
    const COMMAND_LPC_280011 = 280011;
    const COMMAND_LPC_280012 = 280012;
    const COMMAND_LPC_280013 = 280013;
    const COMMAND_LPC_280014 = 280014;
    const COMMAND_LPC_280015 = 280015;
    const COMMAND_LPC_280016 = 280016;
    const COMMAND_LPC_280017 = 280017;
    const COMMAND_LPC_280018 = 280018;//课程失效命令
	const COMMAND_LPC_280019 = 280019;
	const COMMAND_LPC_280020 = 280020;
    const COMMAND_LPC_280021 = 280021;
    const COMMAND_LPC_280022 = 280022;
    const COMMAND_LPC_280023 = 280023;
    const COMMAND_LPC_280030 = 280030;
    const COMMAND_LPC_280031 = 280031;
    const COMMAND_LPC_280032 = 280032;

    //LPC - 工作台
    const COMMAND_LPC_280100 = 280100;
    const COMMAND_LPC_280101 = 280101;
    const COMMAND_LPC_280102 = 280102;
    const COMMAND_LPC_280103 = 280103;
    const COMMAND_LPC_280104 = 280104;//微信生态小程序消息推送LPC
    const COMMAND_LPC_280105 = 280105;//LPC赠课通知
    const COMMAND_LPC_280106 = 280106;//反确认绑定解绑通知
    const COMMAND_LPC_280107 = 280107;//在职微信转移功能，发送例子的消息

    const COMMAND_LPC_280201 = 280201;// 投放页标签通知

    const COMMAND_LPC_280301 = 280301;// 退费挽单工单入库通知

    //防刷算法-分层排灌班
    const COMMAND_FS_SF_311001 = 311001;

    //积分
    const COMMAND_SCORE_800003    = 800003;//教学积分变更
    const COMMAND_SCORE_800004    = 800004;//教学积分变更，新学分服务使用
    const COMMAND_SCORE_800009    = 800009;//助教批改完成后，回调测试系统，测试系统发加学分命令，课中接收加学分
    //重要日志打点
    const COMMAND_ADD_NOTICE_801652 = 801652;//重要日志nmq
    //回放
    const COMMAND_PLAYBACK_810001 = 810001;//章节回放表双写
    const COMMAND_PLAYBACK_810002 = 810002;//学生章节回放表双写
    const COMMAND_PLAYBACK_810003 = 810003;//回放观看时间段命令
    const COMMAND_PLAYBACK_810004 = 810004;//回放续期
    const COMMAND_PLAYBACK_810005 = 810005;//章节回放数据更新
    const COMMAND_PLAYBACK_810006 = 810006;//章节回放-共享直播videomap更新
    // 回放生产NMQ
    const COMMAND_PLAYBACK_810501 = 810501;//回放备份信令写入（来自端）
    const COMMAND_PLAYBACK_810502 = 810502;//回放signalV2加工
    const COMMAND_PLAYBACK_810503 = 810503;//回放流媒体返回数据
    const COMMAND_PLAYBACK_810601 = 810601;//回放生产任务状态变更
    const COMMAND_PLAYBACK_810602 = 810602;//回放生产任务状态变更完成
    const COMMAND_PLAYBACK_810120 = 810120;//录播资源acls更新
    const COMMAND_PLAYBACK_810121 = 810121;//录播资源之关联课件预热（acls)




    const COMMAND_PLAYBACK_810110 = 810110;//录制课视频格式转换
    //试听课到课相关数据
    const COMMAND_ZHIBO_810010 = 810010;//试听课到课相关数据

    //任务系统
    const COMMAND_TASK_820001 = 820001;//签到完成
    const COMMAND_TASK_820002 = 820002;//完成测试
    const COMMAND_TASK_820003 = 820003;//生成课堂报告
    const COMMAND_TASK_820004 = 820004;//绑定解绑测试
    const COMMAND_TASK_820005 = 820005;//新增学生绑定(820002后续逻辑之一)
    const COMMAND_TASK_824001 = 824001;//缓存清理
    const COMMAND_TASK_824002 = 824002;//任务完成、测试提交勋章统计
    const COMMAND_TASK_824003 = 824003;//小学测试提交勋章统计
    const COMMAND_TASK_824004 = 824004;//任务奖励发放
    const COMMAND_TASK_824005 = 824005;//直播回放时长统计--achilles调用
    const COMMAND_TASK_824006 = 824006;//勋章观察者通知
    const COMMAND_TASK_824007 = 824007;//数据统计
    const COMMAND_TASK_824008 = 824008;//新增学生任务(824002后续逻辑)
    const COMMAND_TASK_824009 = 824009;//更新小语日积月累任务
    const COMMAND_TASK_824010 = 824010;//完成小语日积月累任务

    //红包
    const COMMAND_RED_823333 = 823333;

    //错题卡异步入库
    const COMMAND_QE_823334 = 823334;

    //学生进入教室-获取配置
    const COMMAND_ENTER_CONTENT_823335 = 823335;    //进入教室的状态异步写入

    //是否卡
    const COMMAND_YESNOCARD_START_823001 = 823001; //是否卡-开始-记录日志
    const COMMAND_YESNOCARD_START_823002 = 823002; //是否卡-结束-记录日志

    //连麦
    const COMMAND_MIC_ON_823003   = 823003; //开始连麦功能
    const COMMAND_MIC_OFF_823004  = 823004; //结束连麦功能

    //表扬
    const COMMAND_PRAISE_823005 = 823005;//表扬学生
    const COMMAND_PRAISE_823007 = 823007;//表扬数据异步落库

    //签到
    const COMMAND_COMMITSIGNIN_823006   = 823006; //签到

    //学分礼包发放
    const COMMAND_SEND_SCORE_823008 = 823008;



    //库存 TCC
    const COMMAND_STOCK_700001   = 700001; //tcc - try
    const COMMAND_STOCK_700002   = 700002; //tcc - confirm
    const COMMAND_STOCK_700003   = 700003; //tcc - cancel
    const COMMAND_STOCK_700004   = 700004; //缓存异常 回滚数据落库处理
    const COMMAND_STOCK_700005   = 700005; //直接核销实物库存[非TCC模式，注意：售卖量做减法调用]

    const COMMAND_STOCK_7000021  = 7000021; //zbstock 接收新库存服务同步 TCC-try
    const COMMAND_STOCK_7000022  = 7000022; //zbstock 接收新库存服务同步 TCC-cancel
    const COMMAND_STOCK_7000023  = 7000023; //zbstock 接收新库存服务同步 TCC-confirm

	const COMMAND_STOCK_7000011  = 7000011; //新库存服务 下发同步新库存服务 TCC-try
	const COMMAND_STOCK_7000012  = 7000012; //新库存服务 下发同步新库存服务 TCC-cancel
	const COMMAND_STOCK_7000013  = 7000013; //新库存服务 下发同步新库存服务 TCC-confirm
	const COMMAND_STOCK_7000015  = 7000015; //新库存服务库存退还命令
    const COMMAND_STOCK_7000016  = 7000016; // 新库存解封其他sku库存
    const COMMAND_STOCK_7000017  = 7000017; // 新库存兑换码作废、失效, 退款
    const COMMAND_STOCK_7000064  = 7000064; // 通知ares sku 库存发生变化

    //商品平台化 数据同步ES
    const COMMAND_STOCK_700010   = 700010;
    const COMMAND_STOCK_700011   = 700011;
    const COMMAND_STOCK_700012   = 700012;
    const COMMAND_STOCK_700013   = 700013;

    //商品平台化 商品平台提交层
    const COMMAND_STOCK_700015   = 700015;

	const COMMAND_CLEAN_700021   = 700021; //商品缓存清理
	const COMMAND_EVENT_700022   = 700022; //商品一致性事件mq

	const COMMAND_SALEBIZ_700023   = 700023; //售卖策略变更rmq同步

    const COMMAND_GOODS_OPERATION_LOG_700030 = 700030;

    const COMMAND_PLATO_LIMIT_BUY_710000 = 710000; // P维度限购落库命令

    //试卷系统
    const COMMAND_TEST_830001 = 830001;//课下跟读上传录音到bos
    const COMMAND_TEST_830002 = 830002;//学生提交测试
    const COMMAND_TEST_830003 = 830003;//测试系统异步提交
    const COMMAND_TEST_830004 = 830004;//提交结束
    const COMMAND_TEST_830005 = 830005;//延迟删除缓存
    const COMMAND_TEST_830006 = 830006;//异步个性化推题
    const COMMAND_TEST_830007 = 830007;//个性化推题结束
    const COMMAND_TEST_830008 = 830008;//自由作答提交
    const COMMAND_TEST_830009 = 830009;//提交信令
    const COMMAND_TEST_830010 = 830010;//提交信令

    // 试卷中台
    const COMMAND_EXAM_CORE_840001 = 840001; //
    const COMMAND_EXAM_CORE_840002 = 840002; //
    const COMMAND_EXAM_CORE_840003 = 840003; //

    //889号段为Achilles系统专用
    const COMMAND_ACHILLES_889001 = 889001;
    const COMMAND_ACHILLES_889002 = 889002;
    const COMMAND_ACHILLES_889003 = 889003;
    const COMMAND_ACHILLES_889004 = 889004;
    const COMMAND_ACHILLES_889005 = 889005;
    const COMMAND_ACHILLES_889006 = 889006;
    const COMMAND_ACHILLES_889007 = 889007; //端上打点，Achilles计算到课时长
    const COMMAND_ACHILLES_889008 = 889008;
    const COMMAND_ACHILLES_889009 = 889009;
    const COMMAND_ACHILLES_889010 = 889010;
    const COMMAND_ACHILLES_889011 = 889011;
    const COMMAND_ACHILLES_889012 = 889012;
    const COMMAND_ACHILLES_889013 = 889013;
    const COMMAND_ACHILLES_889014 = 889014;
    const COMMAND_ACHILLES_889015 = 889015;
    const COMMAND_ACHILLES_889016 = 889016;
    const COMMAND_ACHILLES_889017 = 889017;
    const COMMAND_ACHILLES_889018 = 889018; //端上打点按学生&章节拆分后发送到计算引擎
    const COMMAND_ACHILLES_889020 = 889020; // 课程体系信息修改
    const COMMAND_ACHILLES_889021 = 889021; // 课程目标修改
    const COMMAND_ACHILLES_889022 = 889022; // teacherconfig更新
    const COMMAND_ACHILLES_889023 = 889023; //计算引擎发送明细
    const COMMAND_ACHILLES_889024 = 889024; //sparta清楚acls缓存
    const COMMAND_ACHILLES_889025 = 889025; //inclass 通知achilles更新三峡html数据

    // 891号段教学前台使用
    const COMMAND_JXFRONT_891001 = 891001; // 接口重构过程中异步diff
    const COMMAND_JXFRONT_891002 = 891002; // 弱缓存-预热学生
    const COMMAND_JXFRONT_891003 = 891003; // 弱缓存-业务方推送数据
    const COMMAND_JXFRONT_891004 = 891004; // 弱缓存-拉取业务方数据
    const COMMAND_JXFRONT_891005 = 891005; // 教学前台相关公众号关注取关(垂直拆分1881007)
    const COMMAND_JXFRONT_891006 = 891006; // 小鹿写字相关公众号关注取关(垂直拆分1881007)
    const COMMAND_JXFRONT_891007 = 891007; // 小鹿写字相关小程序消息相关(垂直拆分1881008)
    const COMMAND_JXFRONT_891008 = 891008; // 旁听课-预热旁听课数据
    const COMMAND_JXFRONT_891009 = 891009; // 旁听课-预热每个年级的旁听列表
    const COMMAND_JXFRONT_891010 = 891010; // 客户端巩固练习提交节点-巩固练习课下排行榜

    // 892号段ridge模块使用(业务前台)
    const COMMAND_RIDGE_892001 = 892001; // 课中营销-章节营销信息变动
    const COMMAND_RIDGE_892002 = 892002; // 素养课，课程章节回放都成功，回调售卖可以上架
    const COMMAND_RIDGE_892003 = 892003; // 计数器持久化
    const COMMAND_RIDGE_892101 = 892101; // 课中营销-增
    const COMMAND_RIDGE_892102 = 892102; // 课中营销-删
    const COMMAND_RIDGE_892103 = 892103; // 课中营销-改
    // 893号段oppos模块使用(业务前台)
    const COMMAND_OPPOS_893101 = 893101; // app配置-配置保存
    const COMMAND_OPPOS_893102 = 893102; // 合规配置更新-整体
    const COMMAND_OPPOS_893103 = 893103; // 合规配置更新-上课页app
    const COMMAND_OPPOS_893104 = 893104; // 履约配置-上课页
    const COMMAND_OPPOS_893105 = 893105; // 履约配置-课程主页-白名单配置
    const COMMAND_OPPOS_893106 = 893106; // 履约配置-课程主页-策略配置
    const COMMAND_OPPOS_893107 = 893107; // 履约配置-课程主页-模板
    //8932号段studyda模块使用（业务前台）
    const COMMAND_OPPOS_893201 = 893201; // 小鹿素养-banner

    //客服系统
    const COMMAND_MISSERVICE_910001 = 910001; // 客服通用
    const COMMAND_MISSERVICE_910002 = 910002; // 客服通用
    const COMMAND_MISSERVICE_910003 = 910003; // 客服通用
    const COMMAND_MISSERVICE_910004 = 910004; // 客服通用
    const COMMAND_MISSERVICE_910005 = 910005; // 七鱼通话记录拉取
    const COMMAND_MISSERVICE_910006 = 910006; // 用户投诉黑名单

    //自研工单
    const COMMAND_SWAN_911001 = 911001; //自研工单自动任务
    const COMMAND_SWAN_911002 = 911002; //自研工单自动派单

    // 质检中台, 920号段, 920001 ~ 920999
    const COMMAND_QUALITYCHECK_920001 = 920001; // 质检系统自跳命令
    const COMMAND_QUALITYCHECK_920002 = 920002; // 质检系统自跳命令
    const COMMAND_QUALITYCHECK_920003 = 920003; // 质检系统自跳命令
    const COMMAND_QUALITYCHECK_920004 = 920004; // 质检系统自跳命令
    const COMMAND_QUALITYCHECK_920005 = 920005; // 质检系统自跳命令

    //zbtiku
    const COMMAND_ZBTIKU_960001 = 960001;//zbtiku前置打标签异步投产
    const COMMAND_ZBTIKU_960002 = 960002;//zbtiku异步打点
    const COMMAND_ZBTIKU_960003 = 960003;//zbtiku生产配置化trace
    const COMMAND_ZBTIKU_960004 = 960004;//zbtiku异步同步生产队列优先级
    const COMMAND_ZBTIKU_960005 = 960005;//zbtiku异步同步打点到业务统计
    const COMMAND_ZBTIKU_960006 = 960006;//zbtiku题源侧项目看板统计
    const COMMAND_ZBTIKU_960007 = 960007;//zbtiku订单异步拆题
    const COMMAND_ZBTIKU_960008 = 960008;//zbtiku扩展题源质检数据看板
    const COMMAND_ZBTIKU_960009 = 960009;//zbtiku纠错修改发送给课中

    //ares
    const COMMAND_ARES_1000001 = 1000001;//ares 任务发送消息通知
    const COMMAND_ARES_1000002 = 1000002;//ares 任务回执消息通知
    const COMMAND_ARES_1000003 = 1000003;//ares 异步 批量增加服务项
    const COMMAND_ARES_1000004 = 1000004;//ares 异步修改服务
    const COMMAND_ARES_1000005 = 1000005;//ares 异步创建任务实例

    const COMMAND_COURSESEARCH_2000001 = 2000001; // coursesearch 课程检索通知消息
    const COMMAND_COURSESEARCH_2000002 = 2000002; // coursesearch 课程检索通知消息

    const COMMAND_CHAT_800001 = 800001; //课中聊天

    //互动中台
    const COMMAND_INTERACT_831001 = 831001;
    const COMMAND_INTERACT_831002 = 831002;
    const COMMAND_INTERACT_831003 = 831003;
    const COMMAND_INTERACT_831004 = 831004; //丢弃，无人接受，压测等场景下防止nmq堵塞使用
    const COMMAND_INTERACT_831006 = 831006; //小班互动表扬落库
    const COMMAND_INTERACT_831007 = 831007; //小班维度PK结果落库
    const COMMAND_INTERACT_831008 = 831008; //老师行为落库
    const COMMAND_INTERACT_831009 = 831009; //聊天内容落库
    const COMMAND_INTERACT_831010 = 831010; //更新学生作答内容相关
    const COMMAND_INTERACT_831011 = 831011; //更新聊天反作弊结果
    const COMMAND_INTERACT_831012 = 831012; //榜单发送信息
    const COMMAND_INTERACT_831013 = 831013; //记录榜单学生信息
    const COMMAND_INTERACT_831014 = 831014; //在线自习室提问信息
    const COMMAND_INTERACT_831015 = 831015; //触达次数
    const COMMAND_INTERACT_831016 = 831016;
    const COMMAND_INTERACT_831017 = 831017;
    const COMMAND_INTERACT_831018 = 831018;
    const COMMAND_INTERACT_831019 = 831019;
    const COMMAND_INTERACT_831020 = 831020;
    const COMMAND_INTERACT_831024 = 831024;
    const COMMAND_INTERACT_831025 = 831025;
    const COMMAND_INTERACT_831026 = 831026;


    //直播中台
    const COMMAND_ROOMSTATUS_870001 = 870001; //jxda room

    const COMMAND_LIVESTATION_870002 = 870002; //信令收集
    const COMMAND_LIVESTATION_870003 = 870003; //小数mvp回放数据生成
    const COMMAND_ROOMSTATUS_870004 = 870004; //jxda room
    const COMMAND_ROOMSTATUS_870005 = 870005; //jxda room
    const COMMAND_ROOMSTATUS_870006 = 870006; //进入房间

    const COMMAND_LIVESTATION_870007 = 870007; //更新组织节点(livestation->jxdalivestation)
    const COMMAND_LIVESTATION_870008 = 870008; //更新直播用户信息(livestation->jxdalivestation)
    const COMMAND_LIVESTATION_870009 = 870009; //更新直播间信息(livestation->jxdalivestation)
    const COMMAND_LIVESTATION_870019 = 870019; //灰度白名单课程章节预热(->jxdalivestation)
    const COMMAND_LIVESTATION_870015 = 870015; //预热完成(livestation->)
    const COMMAND_LIVESTATION_870016 = 870016; //教师直播操作记录(liveui->teacherlive)
    const COMMAND_LIVEUI_870018 = 870018;      //直播间下课(liveui->liveui)
    const COMMAND_LIVEUI_870023 = 870023;      //直播间下课(liveui->)

    // 回放服务
    const COMMAND_PLAYBACK_810111 = 810111; //录制课信令转换


    const COMMAND_JXDA_LIVESTATION_870010 = 870010; //更新achiles中的课程章节绑定组织策略缓存(jxdalivestation->achilles)
    const COMMAND_JXDA_LIVESTATION_870011 = 870011; //更新achilles中的组织策略缓存(jxdalivestation->achilles)
    const COMMAND_JXDA_LIVESTATION_870012 = 870012; //更新achilles中的元数据缓存(jxdalivestation->achilles)
    const COMMAND_JXDA_LIVESTATION_870013 = 870013; //预热数据pre_live(jxdalivestation->livestation)
    const COMMAND_JXDA_LIVESTATION_870014 = 870014; //更新挂载节点(jxdalivestation->livestation)
    const COMMAND_JXDA_LIVESTATION_870017 = 870017; //更新achilles中的直播间缓存(jxdalivestation->achilles)
    const COMMAND_JXDA_LIVESTATION_870020 = 870020; //更新achilles中的直播间用户信息数据缓存
    const COMMAND_JXDA_LIVESTATION_870021 = 870021; //更新achilles中的直播间节点信息数据缓存
    const COMMAND_JXDA_LIVESTATION_870022 = 870022; //更新achilles中的直播间用户所在节点信息数据缓存
    const COMMAND_JXDA_LIVESTATION_870024 = 870024; //更新achilles中的章节原始id节点信息消息数据缓存
    const COMMAND_JXDA_LIVESTATION_870025 = 870025; //检查预热数据
	const COMMAND_JXDA_LIVESTATION_870026 = 870026; // 学生完课通知
    const COMMAND_JXDA_LIVESTATION_870040 = 870040;//更新浣熊班主任跟课学生报警信息
    const COMMAND_JXDA_LIVESTATION_870041 = 870041;//更新浣熊跟课班主任遮盖记录
    const COMMAND_JXDA_LIVESTATION_870042 = 870042;//更新浣熊跟课班主任对报警的处理结果记录
	const COMMAND_JXDA_LIVESTATION_880006 = 880006;

	const COMMAND_JX_CLASSGRAY_880020 = 880020;// 纳米章节灰度变更通知
    const COMMAND_JX_CLASSGRAY_880021 = 880021;// 纳米转发教务310001命令，增加共享章节分组

    // 872*** 为课堂中台新模型使用号段
    const COMMAND_ROOM_STATION_872001 = 872001; // 直播间下课
    const COMMAND_ROOM_STATION_872002 = 872002; // roomdata通知room中间表更新room数据
    const COMMAND_ROOM_STATION_872003 = 872003; // jxdalivestation通知room中间表更新room数据


    //教学 AB测试系统使用号段860001-862000
    const COMMAND_JXDA_ABGRAY_860001 = 860001;//添加/更新开关
    const COMMAND_JXDA_ABGRAY_860002 = 860002;//删除开关
    const COMMAND_JXDA_ABGRAY_860003 = 860003;//添加/更新强升任务
    const COMMAND_JXDA_ABGRAY_860004 = 860004;//删除强升任务
    const COMMAND_JXDA_ABGRAY_860005 = 860005; //智能选课定时任务

    // 教学 课堂配置
    const COMMAND_TARTARUS_ACLS_850001 = 850001;//更新acls缓存



    //激励中台 (1140001-1149999)
    const COMMAND_SIGNINSUBMIT_1140001 = 1140001;  //jxda signin submitdata
    // const COMMAND_LABEL_1140002 = 1140002;  //jxda 标签 学生当前获得称号
    const COMMAND_ENCOURAGE_FISH_1140003 = 1140003;  //jxda 小数激励加小鱼
    const COMMAND_ENCOURAGE_SCORE_1140004 = 1140004;  //小数激励及时反馈添加学分
    const COMMAND_ENCOURAGE_LABEL_1140005 = 1140005;  //小数激励称号更新
    const COMMAND_ENCOURAGE_REPORT_1140006 = 1140006;  //小数激励战绩
    const COMMAND_PRAISELIST_1140007 = 1140007; //小数排行榜落库
    const COMMAND_PRAISELISTT_1140008 = 1140008; //测试类排行榜数据提交
    const COMMAND_SCORE_ADD_FAIL_1140009 = 1140009; // 加分失败处理
    const COMMAND_SCORE_BUDGET_DISBURSE_1140011 = 1140011; // 单个学生分发学分处理
    // 发放补偿使用
    const COMMAND_SCORE_WRITE_DB_800005 = 800005; // jxda-score消费入库

    // 活动平台 (1110001--1119999)
    const COMMAND_ACTPLATFORM_1110001  = 1110001;//老蜂鸟页面发布buildtool
    const COMMAND_ACTPLATFORM_1110002  = 1110002;//新蜂鸟页面发布fengniao
    const COMMAND_ACTPLATFORM_1110003  = 1110003;//新蜂鸟答题
    const COMMAND_ACTPLATFORM_1110004  = 1110004;//新蜂鸟form第三方数据推送
    const COMMAND_ACTPLATFORM_1110005  = 1110005;//新蜂鸟抽奖，同步发奖品
    const COMMAND_ACTPLATFORM_1110006  = 1110006;//新蜂鸟抽奖，库存同步
    const COMMAND_ACTPLATFORM_1110007  = 1110007;//新蜂鸟活动发布
    const COMMAND_ACTPLATFORM_1110008  = 1110008;//新蜂鸟课中抽奖插座 慢车道
    const COMMAND_ACTPLATFORM_1110009  = 1110009;//新蜂鸟课中抽奖插座 快车道
    const COMMAND_ACTPLATFORM_1110010  = 1110010;//组队通知
    const COMMAND_ACTPLATFORM_1110011  = 1110011;//新蜂鸟异步命令
    const COMMAND_ACTPLATFORM_1110012  = 1110012;//新蜂鸟下单命令
    const COMMAND_ACTPLATFORM_1110015  = 1110015;//蜂鸟空命令，为了解决sell集群偏移量问题，后续待删除
    const COMMAND_ACTPLATFORM_1110016  = 1110016;//蜂鸟接收微信消息推送

    // 活动引擎 (1110101--1110999)
    const COMMAND_ACTENGINE_1110101 = 1110101; // 发放优惠券[活动引擎]
    const COMMAND_ACTENGINE_1110102 = 1110102; // 海报审核结果通知[活动引擎]

    //kejian
    const COMMAND_KEJIAN_980001 = 980001; //课件绑定生成zip包后通知下游变更缓存
    const COMMAND_KEJIAN_980002 = 980002; //课件生成前端程序包后通知下游变更缓存
    const COMMAND_KEJIAN_980003 = 980003; //通知互动生成互动课件
    const COMMAND_KEJIAN_980004 = 980004; //章节/大纲变更通知下游
    const COMMAND_KEJIAN_980005 = 980005; //审核结果通知
    const COMMAND_KEJIAN_980006 = 980006; //课件异步发送邮件
    const COMMAND_KEJIAN_980007 = 980007; //直播间异动课件异步发送的章节信息
    const COMMAND_KEJIAN_980008 = 980008; //课件zip资源生成通知自动化巡检
    const COMMAND_KEJIAN_980009 = 980009; //课件异动解绑报警
    const COMMAND_KEJIAN_980010 = 980010; //课件开始打包
    const COMMAND_KEJIAN_980011 = 980011; //课件相关资源更新通知(小鹿编程，cocos题互动题等)
    const COMMAND_KEJIAN_980012 = 980012; //课件解绑通知下游(小鹿编程，cocos题互动题等)
    const COMMAND_KEJIAN_980013 = 980013; //CDN预热
    const COMMAND_KEJIAN_980014 = 980014; //Kj纬度检查
    const COMMAND_KEJIAN_980015 = 980015; //Lesson纬度检查
    const COMMAND_KEJIAN_980016 = 980016; //课件打包
    const COMMAND_KEJIAN_980020 = 980020; //课件资源更新

    // cocos 981000 - 981999
    const COMMAND_COCOS_981000 = 981000; // cocos 堂堂测程序包变更

    // 讲义982000-982999
    const COMMAND_ZB_LECTURE_NOTES_982000 = 982000; //自动化任务结果通知

    //校验任务生产系统
    const COMMAND_JYCHECK_990001 = 990001; //检测结果通知mq

    // 教研ai课视频生产
    const COMMAND_JYAI_991001 = 991001; //转码视频回调失败重试mq
    const COMMAND_JYAI_991002 = 991002; //音频信息变更通知achilles

    // 教研任务生产系统
    const COMMAND_JYTASK_950001 = 950001;
    const COMMAND_JYTASK_950002 = 950002;

    // reportcore 951000 - 951999
    const COMMAND_REPORTCORE_951000 = 951000;

    // i_Lab
    const COMMAND_ILAB_880001 = 880001; // i_lab 学生端提交 更新目标完成度
    const COMMAND_ILAB_880002 = 880002; // i_lab 学生端提交 更新目标完成度
    const COMMAND_ILAB_880003 = 880003; // i_lab MT系统更改 更新目标信息
	const COMMAND_ILAB_880004 = 880004; // ilab abtest推题

	// 堂堂测
	const COMMAND_INCLAS_EXAM_888001 = 888001; // 堂堂测发送

    //策略
    const COMMAND_VIP_899001 = 899001; //策略赠送or收回vip
    const COMMAND_COUPON_UPDATE_899002 = 899002; //用户优惠券变更命令
    const COMMAND_SKU_GROUP_UPDATE_899003 = 899003; //限购组变更命令
    const COMMAND_COUPON_CODE_UPDATE_899004 = 899004; //兑换码变更命令

    //主讲（970001-97999）
    const COMMAND_TEACHER_970001 = 970001; //通知阿喀琉斯刷新fudao老师信息缓存

    // 拼团 (1121000--1121999)
    const COMMAND_TUAN_1121000 = 1121000; // 拼团时间到期未成团自动退款
    const COMMAND_TUAN_1121001 = 1121001; // 开团参团成功 == 团长是开团，团员是参团
    const COMMAND_TUAN_1121002 = 1121002; // 拼团成功
    const COMMAND_TUAN_1121003 = 1121003; // 团解散
    const COMMAND_TUAN_1121004 = 1121004; // 拼团反作弊
    const COMMAND_TUAN_1121005 = 1121005; // 更新拼团商品的拼团售卖量
    const COMMAND_TUAN_1121006 = 1121006; // 拼团异步更新团成员信息
    const COMMAND_TUAN_1121007 = 1121007; // 拼团写入或删除团临时表数据 (开团写入、成团或解散团删除)

    // 活动-结算(1122000--1122499)
    const COMMAND_ACT_REWARD_1122000 = 1122000; // 结算奖励成功

    // 分销 (1122500--1122799)(先占个位置)
    const COMMAND_FENXIAO_1122500 = 1122500; // 分销系统用户转化成功（写入tblFenXiaoInviteRelation表一条记录）
    const COMMAND_FENXIAO_1122501 = 1122501; // 奖励提醒（触发时机：老用户获得购课奖励时）
    const COMMAND_FENXIAO_1122502 = 1122502; // 分销系统分销员和新用户绑定成功通知（触发时机：分销员和新用户绑定成功时）
    const COMMAND_FENXIAO_1122503 = 1122503; // 分销系统取消转化
    const COMMAND_FENXIAO_1122504 = 1122504; // 分销系统设置转化状态并重算奖励
    const COMMAND_FENXIAO_1122505 = 1122505; // 分销系统重算礼品卡聚合
    const COMMAND_FENXIAO_1122506 = 1122506; // 分销系统计算奖励
    const COMMAND_FENXIAO_1122507 = 1122507; // 分销系统到课完课数更新通知
    const COMMAND_FENXIAO_1122508 = 1122508; // 招募员注册成功通知
    const COMMAND_FENXIAO_1122509 = 1122509; // 分销员注册成功通知
    const COMMAND_FENXIAO_1122510 = 1122510; // 用户符合参与门槛通知
    const COMMAND_FENXIAO_1122511 = 1122511; // 用户访问轨迹通知
    const COMMAND_FENXIAO_1122512 = 1122512; // check分销规则通知

    // 活动平台 - 任务系统 (1122800 ~ 1122999)
    const COMMAND_TASK_1122800 = 1122800; // 履约奖励
    const COMMAND_TASK_1122801 = 1122801; // 触发行为，累计进度
    const COMMAND_TASK_1122805 = 1122805; // 触发行为，蜂鸟任务过滤

    //billing组 ordersearch 1123000 ~1123099
    const COMMAND_ORDERSEARCH_1123000 = 1123000;//[订单ES写入]异步修复数据

    // 活动平台 - 礼品卡系统（1123100 ~ 1123199）
    const COMMAND_GIFTCARD_1123100 = 1123100; // 下发礼品卡
    const COMMAND_GIFTCARD_1123101 = 1123101; // 兑换奖品

    // 趣学习&趣课堂 666000 --- 667000
    const COMMAND_ZBINTERACT_666000 = 666000;
    const COMMAND_ZBINTERACT_666001 = 666001;
    const COMMAND_ZBINTERACT_666002 = 666002;
    const COMMAND_ZBINTERACT_666003 = 666003;
    const COMMAND_ZBINTERACT_666004 = 666004;
    const  COMMAND_ZBINTERACT_666005 = 666005;
    const  COMMAND_ZBINTERACT_666006 = 666006;


    const  COMMAND_JYSERIES_666500 = 666500;

    //群发消息
    const COMMAND_MASSIVE_MESSAGE_1270001 = 1270001; //视频号转发执行状态回调

    //微信 1880001--1889999
    // 微信 eco  1880001 - 1880500
    const COMMAND_WECHAT_ECO_1880001 = 1880001;
    const COMMAND_WECHAT_ECO_1880002 = 1880002;
    const COMMAND_WECHAT_ECO_1880003 = 1880003;
    const COMMAND_WECHAT_ECO_1880004 = 1880004;
    const COMMAND_WECHAT_ECO_1880005 = 1880005; //一元听书

    //微信 wxmarket 1880501 - 1880600
    const COMMAND_WECHAT_WXMARKET_1880501 = 1880501;
    const COMMAND_WECHAT_WXMARKET_1881888 = 1881888;

    //营销活动 1880601 - 1880700
    const COMMAND_WECHAT_YX_1880601 = 1880601; //任务宝-创建&发送海报
    const COMMAND_WECHAT_YX_1880602 = 1880602; //任务宝-微信回调
    const COMMAND_WECHAT_YX_1880603 = 1880603; //任务宝-告警
    const COMMAND_WECHAT_YX_1880621 = 1880621; //任务宝-任务上报
    const COMMAND_WECHAT_YX_1880622 = 1880622; //任务宝-互动链事件上报
    const COMMAND_WECHAT_YX_1880641 = 1880641; //任务宝-库存不足通知
    const COMMAND_WECHAT_YX_1880661 = 1880661; //任务宝-风控信息上报

    //素材库 1880701 - 1880800
    const COMMAND_WECHAT_YXCONTENT_1880701 = 1880701; //素材库-调用信息上报
    const COMMAND_WECHAT_YXCONTENT_1880702 = 1880702; //素材库-调用信息上报-新版
    const COMMAND_WECHAT_YXCONTENT_1880703 = 1880703; //素材库-生成渠道链接上报

    // 微信 wxserver 1881000 - 1881999
    const COMMAND_WECHAT_WXSERVER_1881001 = 1881001; // 绑定用户信息
    const COMMAND_WECHAT_WXSERVER_1881002 = 1881002; // 扫描二维码
    const COMMAND_WECHAT_WXSERVER_1881003 = 1881003; // 关键字回复
    const COMMAND_WECHAT_WXSERVER_1881004 = 1881004; // 作业帮一课加群服务
    const COMMAND_WECHAT_WXSERVER_1881005 = 1881005; // 作业帮账号信息变更，手机号解绑，更换密码
    const COMMAND_WECHAT_WXSERVER_1881006 = 1881006; // 消息推送，用于大规模群发
    const COMMAND_WECHAT_WXSERVER_1881007 = 1881007; // 用户关注与取关事件
    const COMMAND_WECHAT_WXSERVER_1881008 = 1881008; // 小程序客服消息
    const COMMAND_WECHAT_WXSERVER_1881009 = 1881009; // 用户打标签
    const COMMAND_WECHAT_WXSERVER_1881010 = 1881010; // 全量拉粉丝更新用户信息
    const COMMAND_WECHAT_WXSERVER_1881011 = 1881011; // 用户活跃行为
    const COMMAND_WECHAT_WXSERVER_1881012 = 1881012; // 外部打标签任务
    const COMMAND_WECHAT_WXSERVER_1881013 = 1881013; // 消息推送，用于实时业务
    const COMMAND_WECHAT_WXPUSHER_1881101 = 1881101; // 消息系统推送模板消息
    const COMMAND_WECHAT_WXPUSHER_1881102 = 1881102; // 消息系统推送客服消息
    const COMMAND_WECHAT_WXPUSHER_1881103 = 1881103; // 消息系统推送订阅消息
    const COMMAND_WECHAT_WXPUSHER_1881201 = 1881201; // 消息系统推送模板消息日志，已废弃
    const COMMAND_WECHAT_WXPUSHER_1881202 = 1881202; // 消息系统推送客服消息日志，已废弃
    const COMMAND_WECHAT_WXPUSHER_1881203 = 1881203; // 消息系统推送订阅消息日志，已废弃
    const COMMAND_WECHAT_ESMIDDLE_1881204 = 1881204; // 异步写ES
    const COMMAND_WECHAT_ESMIDDLE_1881205 = 1881205; // wxservergo消息分发总线
    
    const COMMAND_WECHAT_WXWEB_1881301    = 1881301; //小程序导流中间页透传参数实时同步，rmq


    //微信群控 1882000 ～ 1882999
    const COMMAND_WXQK_1882000 = 1882000; //个人信息相关
    const COMMAND_WXQK_1882001 = 1882001; //微信好友回调
    const COMMAND_WXQK_1882002 = 1882002; //微信群回调
    const COMMAND_WXQK_1882003 = 1882003; //微信消息回调
    const COMMAND_WXQK_1882004 = 1882004; //微信系统回调
    const COMMAND_WXQK_1882005 = 1882005; //建群通知
    const COMMAND_WXQK_1882006 = 1882006; //标签相关
    const COMMAND_WXQK_1882007 = 1882007; //朋友圈相关
    const COMMAND_WXQK_1882008 = 1882008; //微信其他
    const COMMAND_WXQK_1882009 = 1882009; //视频号
    const COMMAND_WXQK_1882042 = 1882042; //微信通用工具

    const COMMAND_WXQK_1882010 = 1882010; //自动建群延迟
    const COMMAND_WXQK_1882011 = 1882011; //入群系统消息处理延迟
    const COMMAND_WXQK_1882012 = 1882012; //群信息变动处理延迟
    const COMMAND_WXQK_1882013 = 1882013; //机器人下架通知

    const COMMAND_WXQK_1882014 = 1882014; //个微 - 机器人账号同步
    const COMMAND_WXQK_1882015 = 1882015; //个微 - 机器人账号状态同步
    const COMMAND_WXQK_1882016 = 1882016; //qq - 机器人账号状态同步
    const COMMAND_WXQK_1882017 = 1882017; //qq - 机器人账号状态同步
    const COMMAND_WXQK_1882018 = 1882018; //企微 - 机器人账号状态同步
    const COMMAND_WXQK_1882019 = 1882019; //企微 - 机器人账号状态同步
    const COMMAND_WXQK_1882034 = 1882034; //废弃群通知
    const COMMAND_WXQK_1882035 = 1882035; //回收群 活动维度

    const COMMAND_WXSK_1882020 = 1882020; //通过好友申请处理延迟
    const COMMAND_WXQK_1882021 = 1882021; //通过好友申请处理延迟 群控业务

    const COMMAND_WXTB_1882022 = 1882022;//wxtb建群成功回调
    const COMMAND_WXQK_1882023 = 1882023;//建群流程完成通知

    const COMMAND_WXTB_1882040 = 1882040;   // 好友反确认


    const COMMAND_WXTA_1882025 = 1882025; //wxteamalloc (wxta) 增加lpc异动绑定战队成功回调

    const COMMAND_WXTB_1882026 = 1882026;//战队异动通知
    const COMMAND_WXTA_1882027 = 1882027; //wxteamalloc (wxta) 绑定战队成功回调
    const COMMAND_WXTB_1882028 = 1882028;//wxtb流程处理完成回调
    //

    const COMMAND_WXQK_1882024 = 1882024;//获取群二维码的信息通知（策略使用）
    const COMMAND_WXQK_1882060 = 1882060;//进量通知 （策略自动切期次使用）

    const COMMAND_WXQK_1882029 = 1882029; //群控业务通用延时处理


    const COMMAND_WXQK_1882033 = 1882033; //群控业务通用延时处理
    const COMMAND_WXQK_1882036 = 1882036; //群控侧线索更新sql数据通知
    const COMMAND_WXQK_1882037 = 1882037; // 视频号私信

    const COMMAND_WXOT_1882032 = 1882032; //wxohters接收公告号发消息回调命令号
    const COMMAND_WXQK_1882038 = 1882038; //群控接收到机器人之后通知其他业务方新增机器人
    const COMMAND_WXQK_1882039 = 1882039; //群控同步机器人状态之后通知其他业务方
    const COMMAND_QLROBOT_1882041 = 1882041; //企微运营者离职
    const COMMAND_QLROBOT_1882052 = 1882052; //企微运营者离职

    const COMMAND_MASS_1882800 = 1882800; // 群发消息事件维度回调
    const COMMAND_MASS_1882801 = 1882801; // 群发消息群维度事件回调
    const COMMAND_MASS_1882802 = 1882802; // 群发消息群成员维度事件回调
    const COMMAND_MASS_1882804 = 1882804; // 群发消息回调

    const COMMAND_WXQK_1882998 = 1882998; // 用户进群，群控发送mq



    //消息系统 1883000 ～ 1883999
    const COMMAND_WXMSG_1883001 = 1883001; //消息接收NMQ
    const COMMAND_WXMSG_1883002 = 1883002; //任务状态通知NMQ
    const COMMAND_WXMSG_1883003 = 1883003; //群信息变更通知NMQ

    const COMMAND_WXQK_1882030 = 1882030; //变更群维护者处理延迟
    const COMMAND_WXQK_1882031 = 1882031; //异步发送群消息



    const COMMAND_WXEXECUTOR_1887001 = 1887001; //微信执行系统消息，主动消息回调
    const COMMAND_WXEXECUTOR_1887002 = 1887002; //微信执行系统消息，被动消息
    const COMMAND_WXGC_1887003 = 1887003; //重试回调
    const COMMAND_WXGC_1887004 = 1887004; // WXGC 模仿wxexecute回调自己

    const COMMAND_WXGC_1887100  = 1887100; // 群能力向外广播
    const COMMAND_WXGC_1887101  = 1887101; // 系统消息进群延迟处理

    const COMMAND_WXGC_1887200  = 1887200; // 进群向督学发nmq
    const COMMAND_WXGC_1887201  = 1887201; // 退群向督学发nmq

    const COMMAND_WXQK_1882999 = 1882999; //群控方向日志收集

    const COMMAND_WXQK_1883031 = 1883031;//添加微信ID黑白名单（策略使用）
    const COMMAND_WXQK_1883032 = 1883032;//添加微信unionid黑白名单（策略使用）
    const COMMAND_WXQK_1883033 = 1883033;//取消微信ID黑白名单（策略使用）
    const COMMAND_WXQK_1883034 = 1883034;//取消微信unionid黑白名单（策略使用）
    const COMMAND_WXQK_1883035 = 1883035; //通知系统消息（策略使用）
    const COMMAND_WXQK_1883036 = 1883036; //码生成回调

    //企业微信1884000 ~ 1884999
    const COMMAND_WXWORK_1884001 = 1884001; //建群延迟消息
    const COMMAND_WXWORK_1884201 = 1884201; //添加企业微信外部联系人通知

    const COMMAND_WXWORK_1884301 = 1884301; //企微进群退群通知
    const COMMAND_WXWORK_1884302 = 1884302; //企微更新通知
    const COMMAND_WXWORK_1884303 = 1884303; //个人号好友通知
    const COMMAND_WXWORK_1884304 = 1884304; //WxWorkUser好友通知
    const COMMAND_WXWORK_1884501 = 1884501; //对话剧本首轮消息发送结果通知

    //wx客服系统1884401 ~ 1884499
    const COMMAND_WXCS_1884401 = 1884401; //wx客服系统消息
    const COMMAND_WXCS_1884402 = 1884402; //csmsg通过个人号业务方发送个微&企微消息结果回调
    const COMMAND_WXCS_1884403 = 1884403; //客服工作台通过csmsg发送个微&企微消息结果回调
    const COMMAND_WXCS_1884404 = 1884404; //策略strategy通过csmsg发送微&企微消息结果回调
    //=====企业微信1884000 ~ 1884999 结束 ====


    //=====微信 1880001--1889999=结束======

    // 低幼业务线NMQ号码，以2打头的7位号码，
    // 格式：业务线（2）+ 具体业务（两位数，如班主任是00）+ 具体项目（两位数，kidtutor暂定为00）+ 具体接口号（两位数rd定义）
    // 业务：班主任方向为00，鸭鸭写字01，鸭鸭英语02，鸭鸭语文03
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9000000 = 9000000; // 低幼练习作答完毕提交
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9000001 = 9000001; // 低幼鸭鸭新语音完毕提交

    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9000010 = 9000010; // 低幼班主任排灌班消息
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9000011 = 9000011; // 低幼班主任新增班主任 - 排灌班异动NMQ（直播用）
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100000 = 9100000; // 低幼班主任消息任务
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100001 = 9100001; // 低幼班主任排灌班削峰队列
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100002 = 9100002; // 低幼灌班完成通知
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100003 = 9100003; // 低幼点评任务
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100004 = 9100004; // 天鹰语音回调
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100005 = 9100005; // 发送短信
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100006 = 9100006;  // 低幼取消灌班完成通知
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100007 = 9100007; // 通话记录手动标注
    const COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100011 = 9100011; // 低幼课程直播章节重开

    //低幼语文
    const COMMAND_DIYOU_KID_CHINESE_9030101        = 9030101; // 鸭鸭语文 接收客服会话里用户发送的微信消息

    //=====低幼 9000000--9999999=结束======

    //=====购物车 1890000~1890500======
    const COMMAND_CART_1890000  = 1890000;//添加/删除购物车
    //=====购物车 1890000~1890500======

    // 基础架构专用 999000 --- 999999
    const COMMAND_INFRA_999000 = 999000;    //Sparta 打包完成
    const COMMAND_INFRA_999001 = 999001;    //OCS 集群策略变更

    // 0转正相关 1260000 ~ 1269999
    const COMMAND_ZERO_1260001 = 1260001; //0转正链接转化
    const COMMAND_ZERO_1261000 = 1261000; //获得微信ID mapping信息
    const COMMAND_ZERO_1261001 = 1261001; //unionId-uid更新

    const COMMAND_ZERO_1262001 = 1262001; //活动备群通知
    const COMMAND_ZERO_1262002 = 1262002; //绑定战队
    const COMMAND_ZERO_1262003 = 1262003; //删除LPC

    const COMMAND_ZERO_1262004 = 1262004; //先加微，后进群，加好友成功事件
    const COMMAND_ZERO_1262005 = 1262005; //先加微，后进群，备码相关事件
    const COMMAND_ZERO_1262006 = 1262006; //灌班异步任务事件
    const COMMAND_ZERO_1262007 = 1262007; //异步备群
    const COMMAND_ZERO_1262008 = 1262008; //先加微，后进群，获取二维码成功事件
    const COMMAND_ZERO_1262009 = 1262009; //userCourse用户与lpc关系调整
    const COMMAND_ZERO_1262010 = 1262010; //小程序直播间监控数据

    //SCP(供应链生产)相关
    const SCP_SYNC_WMS_STOCK   = 616001; //同步wms 仓库库存变更
    const WMS_SYNC_SCP_STOCK   = 616002; //三方入库回调wms库存变动下发同步scp系统

    //问答系统相关 1280001 ~ 1289999
    const COMMAND_WENDA_1280001 = 1280001; //问答系统通用信令号
    const COMMAND_WENDA_1280002 = 1280002; //问答系统通用信令号，生产中判重发出的命令号，只有命令号与1280001不同


    //作答审核模块
    const COMMAND_WNEDA_1280101 = 1280101; //作答超时信令
    const COMMAND_WNEDA_1280102 = 1280102; //作答提交将checklistuuid发给派单
    const COMMAND_WENDA_1280103 = 1280103; //作答发送对外通知（非流程类通知）
    const COMMAND_WENDA_1280104 = 1280104; //问答系统向申诉系统进行事件通知
    const COMMAND_WENDA_1280106 = 1280106; //申诉结果通知
    const COMMAND_WENDA_1280107 = 1280107; //薪资事件通知

    //问答激励系统start
    const COMMAND_WENDA_1280201 = 1280201; //通知规则更新
    const COMMAND_WENDA_1280401 = 1280401; // 问答葫芦娃消息转发
    const COMMAND_WENDA_1280402 = 1280402; // 问答微信模版消息MQ使用
    const COMMAND_WENDA_1280403 = 1280403; // 收发场景消息的MQ
    //问答激励系统end

    //问答工单相关
    const COMMAND_WENDA_1280301 = 1280301; //问答工单机审
    //问答测试题
    const COMMAND_WENDA_1280302 = 1280302; //测试题事件触发
    const COMMAND_WENDA_1280303 = 1280303; //派单结果通知
    const COMMAND_WENDA_1280501 = 1280501;  //问答自动解答
    const COMMAND_WENDA_1280508 = 1280508;  //更新staff用户删除状态
    const COMMAND_WENDA_1280505 = 1280505;  //注销用户薪资冻结
    const COMMAND_WENDA_1280601 = 1280601;  //负反馈投产结果通知
    const COMMAND_WENDA_1280602 = 1280602;  //同步消息至海外

    //问答系统相关 1280000 ~ 1289999

    //  ol
    const COMMAND_WENDA_340012 = 340012; //老师号事件回调

    //-----小鹿相关（125号段） 1250000 ~ 1259999 start--------
    const COMMAND_XIAOLU_1250001 = 1250001; //小鹿数据变更通知
    const COMMAND_XIAOLU_1251001 = 1251001; //小鹿内部教学相关消息通知
    const COMMAND_XIAOLU_1251002 = 1251002;//发送ares任务回执消息
    const COMMAND_XIAOLU_1252001 = 1252001; //小鹿写字作业状态c端同步到b端
    const COMMAND_XIAOLU_1252002 = 1252002; //小鹿写字作业状态b端同步到c端
    const COMMAND_XIAOLU_1252003 = 1252003; //小鹿写字异步生成报告消息
    const COMMAND_XIAOLU_1252004 = 1252004; //小鹿写字任务消息同步
    const COMMAND_XIAOLU_1253001 = 1253001; //小鹿写字deer-mis自回调服务
    const COMMAND_XIAOLU_1253002 = 1253002; //小鹿写字deer-mis向handwrite-correct发送通知
    const COMMAND_XIAOLU_1253003 = 1253003; //小鹿写字handwrite-correct向deer-mis发送通知
    const COMMAND_XIAOLU_1253004 = 1253004; //小鹿写字deer-mis向handwrite发送通知
    const COMMAND_XIAOLU_1253005 = 1253005; //小鹿写字handwrite向deer-mis发送通知
    const COMMAND_XIAOLU_1254001 = 1254001; //小鹿写字服务号关注情况的消息
    //-----小鹿相关（125号段） 1250000 ~ 1259999 end--------

    //-----IMC相关（1024000 ~ 1024999） start--------
    const COMMAND_IMC_1024000 = 1024000; //拆分微信生态1881007命令, 接受作业帮直播课微信小程序事件消息
    //-----IMC相关（1024000 ~ 1024999） end--------

    //-----Commodity 大促 start
    const COMMAND_PROMOTION_1290000 = 1290000;
    //-----Commodity 大促 end

    //-----学习管家相关 (1300000-1300999) start-----
    const COMMAND_GUANJIA_1300000 = 1300000;
    const COMMAND_GUANJIA_1300001 = 1300001;
    const COMMAND_GUANJIA_1300002 = 1300002;
    const COMMAND_GUANJIA_1300003 = 1300003;
    const COMMAND_GUANJIA_1300004 = 1300004;

    //-----学习管家相关 (1300000-1301000) end-----

    // das 1400000 - 1400999
    const COMMAND_DAS_1400001 = 1400001;  // 实时加课
    const COMMAND_DAS_1400002 = 1400002;  // 实时减课
    const COMMAND_DAS_1400003 = 1400003;  // 延迟加课
    const COMMAND_DAS_1400004 = 1400004;  // 延迟减课
    const COMMAND_DAS_1400005 = 1400005;  // 加课完成
    const COMMAND_DAS_1400006 = 1400006;  // 减课完成
    const COMMAND_DAS_1400007 = 1400007;  // 最终一致性校验
    const COMMAND_DAS_1400008 = 1400008;  // 第二阶段加课-高优

    static $arrCommandMap = array(
        //ares
        self::COMMAND_ARES_1000001 => 'ares 任务发送消息通知',
        self::COMMAND_ARES_1000002 => 'ares 任务回执消息通知',
        self::COMMAND_ARES_1000003 => 'ares 异步增加服务项',
        self::COMMAND_ARES_1000004 => 'ares 异步修改服务',
        self::COMMAND_ARES_1000005 => 'ares 异步创建任务实例',
        //通用类命令
        self::COMMAND_CORE_100001 => '延迟清理缓存',
        self::COMMAND_CORE_100011 => '延迟清理缓存 交易3.0新增',
        //课节相关
        self::COMMAND_CORE_110001 => '课程上线',
        self::COMMAND_CORE_110002 => '课程编辑',
        self::COMMAND_CORE_110003 => '课程结束（正常结束）',
        self::COMMAND_CORE_110004 => '课程关闭（异常关闭）',
        self::COMMAND_CORE_110005 => '章节结束（正常结束）',
        self::COMMAND_CORE_110006 => '章节重开',
        self::COMMAND_CORE_110007 => '章节时间调整',
        self::COMMAND_CORE_110008 => '章节代课',
        self::COMMAND_CORE_110009 => '章节关闭（异常结束）',
        self::COMMAND_CORE_110010 => '章节编辑',
        self::COMMAND_CORE_110011 => '加课',
        self::COMMAND_CORE_110112 => '按天分级章节履约，支持批量赠送（2020加油站计划）',
        self::COMMAND_CORE_110012 => '减课',
        self::COMMAND_CORE_110013 => '章节新增',
        self::COMMAND_CORE_110014 => '章节删除',
        self::COMMAND_CORE_110015 => '章节更换主讲',
        self::COMMAND_CORE_110016 => '课节数据发生变更',
        self::COMMAND_CORE_110017 => '老系统的加课命令',
        self::COMMAND_CORE_110018 => '课堂笔记',
        self::COMMAND_CORE_110019 => '学生资料',
        self::COMMAND_CORE_110020 => '章节维度数据汇聚上报',
        self::COMMAND_CORE_110021 => '学生课程维度数据汇聚上报',
        self::COMMAND_CORE_110022 => '学生章节维度数据汇聚上报',
        self::COMMAND_CORE_110023 => '课程产品发布',
        self::COMMAND_CORE_110024 => '更新课程产品',
        self::COMMAND_CORE_110025 => '更新大纲',
        self::COMMAND_CORE_110026 => '删除大纲',
        self::COMMAND_CORE_110028 => '删除课程产品',
        self::COMMAND_CORE_110029 => '变更容器服务信息',
        self::COMMAND_CORE_110030 => '更新容器服务信息',
        self::COMMAND_CORE_110031 => '生成宠物雪球',
        self::COMMAND_CORE_110032 => '新课堂笔记（含班主任）',
        self::COMMAND_CORE_110033 => '准备上课&准备下课（2020公益课）',
        self::COMMAND_CORE_110035 => '新增大纲异动',
        self::COMMAND_CORE_110036 => '课程异步处理',
        self::COMMAND_CORE_110037 => 'das对外通知uid_lesson变更',
        self::COMMAND_CORE_110038 => '伴学信息更新',
        self::COMMAND_CORE_110039 => 'das对外通知uid_lesson删除',
        self::COMMAND_CORE_800016 => '课程方批量创建信息',
        self::COMMAND_CORE_800017 => '共享章节关系变动',
        self::COMMAND_CORE_800027 => '共享章节关系变动',
        self::COMMAND_CORE_800028 => '共享章节关系延时diff报警',
        self::COMMAND_CORE_800018 => '课程异动异步处理',
        self::COMMAND_CORE_800022 => '导入任务异步处理',
        self::COMMAND_CORE_800088 => '请求roomservice失败重试',

        //course相关
        self::CMD_FUDAO_ADD_TASK_800012=>'回放生成提醒任务',


        //单品相关
        self::COMMAND_CORE_120001 => '单品上架',
        self::COMMAND_CORE_120002 => '单品下架',
        self::COMMAND_CORE_120003 => '单品隐藏',
        self::COMMAND_CORE_120004 => '单品展现',
        self::COMMAND_CORE_120005 => '新商品上架',
        self::COMMAND_CORE_120006 => '新商品下架',

        //赠品相关
        self::COMMAND_CORE_121001 => '赠品发布',
        self::COMMAND_CORE_121002 => '赠品单品加库存',

        //单品&拼团策略
        self::COMMAND_CORE_121003 => '单品绑定拼团策略',
        self::COMMAND_CORE_121004 => '成品同步物流',
        self::COMMAND_CORE_121005 => '实物同步物流',
        self::COMMAND_CORE_121006 => '发布商品到店铺',

        //用户相关
        self::COMMAND_CORE_130001 => '学生信息修改',
        self::COMMAND_CORE_130002 => '主讲信息修改',
        self::COMMAND_CORE_130003 => '用户展示真实名称',

        //课中相关
        self::COMMAND_CORE_140001 => '学生上线',
        self::COMMAND_CORE_140002 => '学生下线',
        self::COMMAND_CORE_140003 => '到课数据计算完毕',
        self::COMMAND_CORE_140004 => '学生课中互动题提交异步信令',
        self::COMMAND_CORE_140005 => '异步端外摸底测提交',
        self::COMMAND_CORE_140006 => '异步提交语音互动',
        self::COMMAND_CORE_140007 => '加积分(能量)',
        self::COMMAND_CORE_140008 => '教师端记录组表扬',
        self::COMMAND_CORE_140009 => '阶段测升班课程推荐',

        //后台 商品相关（组合商品后台）
        self::COMMAND_CORE_150001 => '添加/编辑组合商品',
        self::COMMAND_CORE_150002 => '上架组合商品',
        self::COMMAND_CORE_150003 => '下架组合商品',
        self::COMMAND_CORE_150004 => '组合商品隐藏', //
        self::COMMAND_CORE_150005 => '组合商品取消隐藏',//
        self::COMMAND_CORE_150006 => '单独修改skulist中的grade',//
        self::COMMAND_CORE_150007 => '通知course发生调课',

        //拉新
        self::COMMAND_CORE_160001 => '助力卡',

	    //订单相关
        self::COMMAND_TRADE_170001 => '旧订单同步新订单',
        self::COMMAND_TRADE_170002 => '旧订单退款同步新订单',
        self::COMMAND_TRADE_170003 => '旧系统下单命令',
        self::COMMAND_TRADE_170004 => 'Pay支付回调Trade',
        self::COMMAND_TRADE_170005 => 'pay支付回调course',
        self::COMMAND_TRADE_170006 => 'pay退款回调',
        self::COMMAND_TRADE_170007 => 'trade退款通知course',
        self::COMMAND_TRADE_170008 => '发起物流下发通知(异步)',
        self::COMMAND_TRADE_170009 => 'pay支付回调trade(重构版)',
        self::COMMAND_TRADE_170010 => '订单支付提交完成通知',
        self::COMMAND_TRADE_170012 => '旧系统同步下单命令',
        self::COMMAND_TRADE_170013 => '旧系统接收同步订单信息后发送异步落单信息',
        self::COMMAND_TRADE_170014 => '旧系统接收pay支付回调',
        self::COMMAND_TRADE_170015 => '订单物流地址修改成功通知',
        //self::COMMAND_CORE_170025  => '通知course、物流系统订单寄送地址更新',
        self::COMMAND_CORE_170027  => '通知物流系统派件',
        self::COMMAND_CORE_170028  => '通知物流系统拦截',
        //self::COMMAND_CORE_170031  => '更新子订单物流批次号',//暂不提供异步命令
        self::COMMAND_CORE_170032  => '退团事件通知',
        self::COMMAND_CORE_170033  => '成团之后，触发尾款',
        //self::COMMAND_CORE_170034  => '更新子订单物流催单时间',//暂不提供异步命令
        self::COMMAND_CORE_170035  => '0元支付 tuan模块特殊处理',
        self::COMMAND_CORE_170036 => '订单关闭通知',
        self::COMMAND_CORE_170037 => '退款回调',
        self::COMMAND_CORE_170038 => '新退款回调',
        self::COMMAND_CORE_170045 => '批量退款',
        self::COMMAND_CORE_170039 => '浣熊订单同步一期下单',
        self::COMMAND_CORE_170040 => '浣熊订单同步一期退款',
        self::COMMAND_CORE_170041 => '浣熊订单同步一期转班',
        self::COMMAND_CORE_170042 => '浣熊订单同步一期调班',

        //2019-11-04添加
        self::COMMAND_TRADE_171001 => 'trade支付成功后异步处理消息',

        self::COMMAND_TRADE_173009 => '交易3.0支付成功回调',

        self::COMMAND_TRADE_173109 => '新交易3.0支付成功回调',

        //交易3.0订单事件
        self::COMMAND_ORDER_174001 => '下单',
        self::COMMAND_ORDER_174002 => '关单',
        self::COMMAND_ORDER_174003 => '支付成功',
        self::COMMAND_ORDER_174004 => '退款申请',
        self::COMMAND_ORDER_174005 => '退款成功',

        self::COMMAND_ORDER_174006 => '拆单',
        self::COMMAND_ORDER_174007 => '取消',
        self::COMMAND_ORDER_174008 => '转班',
        self::COMMAND_ORDER_174009 => '售后服务单完成',
        self::COMMAND_ORDER_174110 => '订单变更通知',
        self::COMMAND_ORDER_174109 => '3.0售后服务单完成',

        // 订单同步相关命令1742xx
        self::COMMAND_ASCORDER_174201       => 'asc 下单',
        self::COMMAND_TRANSORDER_174202     => '转班事件通知',
        self::COMMAND_EDITADDRESS_174203    => 'dc 地址修改',
        self::COMMAND_INTERCEPT_174204      => 'dar 商品行拦截',

        //履约
        self::COMMAND_OFC_176001 => '交易完成',
        self::COMMAND_OFC_176002 => '转班申请',
        self::COMMAND_OFC_176003 => '转班成功',
        self::COMMAND_OFC_176004 => '退款申请',
        self::COMMAND_OFC_176005 => '退款成功',
        self::COMMAND_OFC_176006 => '补寄履约',
        //履约3.0
        self::COMMAND_OFC_176011 => '正向下单履约',
        self::COMMAND_OFC_176012 => '退款申请履约',
        self::COMMAND_OFC_176013 => '退款完成成功',
        self::COMMAND_OFC_176014 => '售后退款履约',
        self::COMMAND_OFC_176015 => '转班履约',
        self::COMMAND_OFC_176016 => '延迟履约',

        //数据字典
        self::COMMAND_DDS_178001 => '文件生成',
        self::COMMAND_DDS_178101 => '支付相关数据字典 - 审核失败',

        self::COMMAND_OPM_177001 => '交易3.0订单下单通知管道',
        self::COMMAND_OPM_177002 => '交易3.0订单支付通知管道',
        self::COMMAND_OPM_177003 => '交易3.0订单关单通知管道',
        self::COMMAND_OPM_177004 => '交易3.0订单审核通知管道',
        self::COMMAND_OPM_177005 => '交易3.0订单OAs通知管道',
        self::COMMAND_OPM_177006 => '交易3.0订单ARK通知管道',
        self::COMMAND_OPM_177007 => '交易3.0原单拆单后写子单数据',
        self::COMMAND_OPM_177008 => '交易3.0原单拆单后继续运行命令',
        self::COMMAND_OPM_177009 => '交易3.0子单暂停后继续运行命令',
        self::COMMAND_OPM_177111 => '售后3.0售后回库Register',
        self::COMMAND_OPM_177112 => '售后3.0售后回库Resume',
        self::COMMAND_OPM_177121 => '售后3.0售后退款Register',
        self::COMMAND_OPM_177122 => '售后3.0售后退款Resume',
        self::COMMAND_OPM_177131 => '售后3.0售后订单Register',
        self::COMMAND_OPM_177132 => '售后3.0售后订单Resume',
        self::COMMAND_OPM_177141 => '售后3.0售后赔偿Register',
        self::COMMAND_OPM_177142 => '售后3.0售后赔偿Resume',

        self::COMMAND_ASC_179001 => '售后3.0售后进度更新',
        self::COMMAND_ASC_179002 => '售后子系统自动任务',

        self::COMMAND_ASSC_179301 => '3.0售后自动处理通知',

        self::COMMAND_AFS_179501 => '通知加入订单等待确收任务队列',
        self::COMMAND_AFS_179502 => '虚拟物品履约后订单确收通知',

        self::COMMAND_TSS_179601 => '定单修改通知tss同步es',
        self::COMMAND_TSS_179602 => '售后服务单修改通知tss同步es',

        self::COMMAND_TSS_179606 => '3.0售后服务单修改通知tss',

        self::COMMAND_TSS_179611 => 'zoms订单变更通知tss同步es',

        self::COMMAND_ZHILE_179901 => '批量后台处理ors',
        self::COMMAND_ZHILE_179902 => '批量后台处理afterplat',
        self::COMMAND_ZHILE_179903 => '批量后台处理查售后金额',

        //微信
        self::COMMAND_WEIXIN_180001 => '自动通过好友',
        self::COMMAND_WEIXIN_180002 => '自动通过好友',
        self::COMMAND_WEIXIN_180003 => '自动通过好友',
        self::COMMAND_WEIXIN_180004 => '自动通过好友',
        self::COMMAND_WEIXIN_180005 => '自动通过好友',
        self::COMMAND_WEIXIN_180006 => '自动话术1',
        self::COMMAND_WEIXIN_180007 => '自动话术1',
        self::COMMAND_WEIXIN_180008 => '自动话术1',
        self::COMMAND_WEIXIN_180009 => '自动话术1',
        self::COMMAND_WEIXIN_180010 => '自动话术1',
        self::COMMAND_WEIXIN_180011 => '自动修改备注',
        self::COMMAND_WEIXIN_180012 => '自动修改备注',
        self::COMMAND_WEIXIN_180013 => '自动修改备注',
        self::COMMAND_WEIXIN_180014 => '自动修改备注',
        self::COMMAND_WEIXIN_180015 => '自动修改备注',
        self::COMMAND_WEIXIN_180016 => '延迟接收消息',

        self::COMMAND_WEIXIN_180017 => '群发消息',
        self::COMMAND_WEIXIN_180018 => '群发消息',
        self::COMMAND_WEIXIN_180019 => '群发消息',
        self::COMMAND_WEIXIN_180020 => '消息撤回',
        self::COMMAND_WEIXIN_180021 => '消息打点',
        self::COMMAND_WEIXIN_180022 => '批量模板消息',
        self::COMMAND_WEIXIN_180023 => '单个模板消息',
        self::COMMAND_WEIXIN_180024 => '回复消息',
        self::COMMAND_WEIXIN_180025 => '邮件报警',
        self::COMMAND_WEIXIN_180027 => '异步写消息',
        self::COMMAND_WEIXIN_180028 => '督学换绑数据',
        self::COMMAND_WEIXIN_181028 => '督学0元课换绑数据',
        self::COMMAND_WEIXIN_180029 => '班主任换绑数据',
        self::COMMAND_WEIXIN_180044 => '督学换绑回调',
        self::COMMAND_WEIXIN_181044 => '督学0元课换绑回调',
        self::COMMAND_WEIXIN_180046 => '班主任换绑回调',
        self::COMMAND_WEIXIN_180047 => '设备账号绑定',
        self::COMMAND_WEIXIN_180030 => '事件订阅',
        self::COMMAND_WEIXIN_180031 => '事件订阅推送督学',
        self::COMMAND_WEIXIN_180032 => '自动备注[短备注]',
        self::COMMAND_WEIXIN_180033 => '自动备注[短备注]',
        self::COMMAND_WEIXIN_180034 => '自动备注[短备注]',
        self::COMMAND_WEIXIN_180035 => '自动备注[短备注]',
        self::COMMAND_WEIXIN_180036 => '自动备注[短备注]',
        self::COMMAND_WEIXIN_180037 => '消息通知',
        self::COMMAND_WEIXIN_180038 => '群聊邀请',
    	self::COMMAND_WEIXIN_180039 => '自动回复(机器人)',
    	self::COMMAND_WEIXIN_180040 => '删除好友（延时操作)',
    	self::COMMAND_WEIXIN_180041 => '删除群成员（延时操作)',
    	self::COMMAND_WEIXIN_180042 => '添加群成员（延时操作)',
    	self::COMMAND_WEIXIN_180043 => '单个群同步（延时操作)',
        self::COMMAND_WEIXIN_180045 => '群操作延迟（删人/加人）',
        self::COMMAND_WEIXIN_180048 => "群欢迎语",
        self::COMMAND_WEIXIN_180049 => "群欢迎语",
        self::COMMAND_WEIXIN_180050 => "群欢迎语",
        self::COMMAND_WEIXIN_180051 => "群欢迎语",
        self::COMMAND_WEIXIN_180052 => "群欢迎语",
        self::COMMAND_WEIXIN_180053 => "督学换业务线",
        self::COMMAND_WEIXIN_181053 => "督学0元课换业务线",
        self::COMMAND_WEIXIN_180054 => "班主任换业务线",

        self::COMMAND_WEIXIN_180098 => '长连接上线',
        self::COMMAND_WEIXIN_180099 => '长连接下线',

		self::COMMAND_WEIXIN_180100 => '通用延时消息',
		self::COMMAND_WEIXIN_180101 => '通用延时消息',
		self::COMMAND_WEIXIN_180102 => '通用延时消息',
		self::COMMAND_WEIXIN_180103 => '通用延时消息',
		self::COMMAND_WEIXIN_180104 => '通用延时消息',
		self::COMMAND_WEIXIN_180105 => '通用延时消息',
		self::COMMAND_WEIXIN_180106 => '通用延时消息',
		self::COMMAND_WEIXIN_180107 => '通用延时消息',
		self::COMMAND_WEIXIN_180108 => '通用延时消息',
		self::COMMAND_WEIXIN_180109 => '通用延时消息',
		self::COMMAND_WEIXIN_180110 => '通用延时消息',
		self::COMMAND_WEIXIN_180111 => '通用延时消息',
		self::COMMAND_WEIXIN_180112 => '通用延时消息',
		self::COMMAND_WEIXIN_180113 => '通用延时消息',
		self::COMMAND_WEIXIN_180114 => '通用延时消息',
		self::COMMAND_WEIXIN_180115 => '通用延时消息',
		self::COMMAND_WEIXIN_180116 => '通用延时消息',
		self::COMMAND_WEIXIN_180117 => '通用延时消息',
		self::COMMAND_WEIXIN_180118 => '通用延时消息',
		self::COMMAND_WEIXIN_180119 => '通用延时消息',
		self::COMMAND_WEIXIN_180120 => '通用延时消息',
		self::COMMAND_WEIXIN_180121 => '通用延时消息',
		self::COMMAND_WEIXIN_180122 => '通用延时消息',
		self::COMMAND_WEIXIN_180123 => '通用延时消息',
		self::COMMAND_WEIXIN_180124 => '通用延时消息',
		self::COMMAND_WEIXIN_180125 => '通用延时消息',
		self::COMMAND_WEIXIN_180126 => '通用延时消息',
		self::COMMAND_WEIXIN_180127 => '通用延时消息',
		self::COMMAND_WEIXIN_180128 => '通用延时消息',
		self::COMMAND_WEIXIN_180129 => '通用延时消息',
		self::COMMAND_WEIXIN_180130 => '通用延时消息',
		self::COMMAND_WEIXIN_180131 => '通用延时消息',
		self::COMMAND_WEIXIN_180132 => '通用延时消息',
		self::COMMAND_WEIXIN_180133 => '通用延时消息',
		self::COMMAND_WEIXIN_180134 => '通用延时消息',
		self::COMMAND_WEIXIN_180135 => '通用延时消息',
		self::COMMAND_WEIXIN_180136 => '通用延时消息',
		self::COMMAND_WEIXIN_180137 => '通用延时消息',
		self::COMMAND_WEIXIN_180138 => '通用延时消息',
		self::COMMAND_WEIXIN_180139 => '通用延时消息',
		self::COMMAND_WEIXIN_180140 => '通用延时消息',
		self::COMMAND_WEIXIN_180141 => '通用延时消息',
		self::COMMAND_WEIXIN_180142 => '通用延时消息',
		self::COMMAND_WEIXIN_180143 => '通用延时消息',
		self::COMMAND_WEIXIN_180144 => '通用延时消息',
		self::COMMAND_WEIXIN_180145 => '通用延时消息',
		self::COMMAND_WEIXIN_180146 => '通用延时消息',
		self::COMMAND_WEIXIN_180147 => '通用延时消息',
		self::COMMAND_WEIXIN_180148 => '通用延时消息',
		self::COMMAND_WEIXIN_180149 => '通用延时消息',
		self::COMMAND_WEIXIN_180150 => '通用延时消息',
		self::COMMAND_WEIXIN_180151 => '通用延时消息',
		self::COMMAND_WEIXIN_180152 => '通用延时消息',
		self::COMMAND_WEIXIN_180153 => '通用延时消息',
		self::COMMAND_WEIXIN_180154 => '通用延时消息',
		self::COMMAND_WEIXIN_180155 => '通用延时消息',
		self::COMMAND_WEIXIN_180156 => '通用延时消息',
		self::COMMAND_WEIXIN_180157 => '通用延时消息',
		self::COMMAND_WEIXIN_180158 => '通用延时消息',
		self::COMMAND_WEIXIN_180159 => '通用延时消息',
		self::COMMAND_WEIXIN_180160 => '通用延时消息',
		self::COMMAND_WEIXIN_180161 => '通用延时消息',
		self::COMMAND_WEIXIN_180162 => '通用延时消息',
		self::COMMAND_WEIXIN_180163 => '通用延时消息',
		self::COMMAND_WEIXIN_180164 => '通用延时消息',
		self::COMMAND_WEIXIN_180165 => '通用延时消息',
		self::COMMAND_WEIXIN_180166 => '通用延时消息',
		self::COMMAND_WEIXIN_180167 => '通用延时消息',
		self::COMMAND_WEIXIN_180168 => '通用延时消息',
		self::COMMAND_WEIXIN_180169 => '通用延时消息',
		self::COMMAND_WEIXIN_180170 => '通用延时消息',
		self::COMMAND_WEIXIN_180171 => '通用延时消息',
		self::COMMAND_WEIXIN_180172 => '通用延时消息',
		self::COMMAND_WEIXIN_180173 => '通用延时消息',
		self::COMMAND_WEIXIN_180174 => '通用延时消息',
		self::COMMAND_WEIXIN_180175 => '通用延时消息',
		self::COMMAND_WEIXIN_180176 => '通用延时消息',
		self::COMMAND_WEIXIN_180177 => '通用延时消息',
		self::COMMAND_WEIXIN_180178 => '通用延时消息',
		self::COMMAND_WEIXIN_180179 => '通用延时消息',
		self::COMMAND_WEIXIN_180180 => '通用延时消息',
		self::COMMAND_WEIXIN_180181 => '通用延时消息',
		self::COMMAND_WEIXIN_180182 => '通用延时消息',
		self::COMMAND_WEIXIN_180183 => '通用延时消息',
		self::COMMAND_WEIXIN_180184 => '通用延时消息',
		self::COMMAND_WEIXIN_180185 => '通用延时消息',
		self::COMMAND_WEIXIN_180186 => '通用延时消息',
		self::COMMAND_WEIXIN_180187 => '通用延时消息',
		self::COMMAND_WEIXIN_180188 => '通用延时消息',
		self::COMMAND_WEIXIN_180189 => '通用延时消息',
		self::COMMAND_WEIXIN_180190 => '通用延时消息',
		self::COMMAND_WEIXIN_180200 => '通用延时消息',
		self::COMMAND_WEIXIN_180210 => '通用延时消息',
		self::COMMAND_WEIXIN_180220 => '通用延时消息',
		self::COMMAND_WEIXIN_180230 => '通用延时消息',
		self::COMMAND_WEIXIN_180240 => '通用延时消息',
		self::COMMAND_WEIXIN_180250 => '通用延时消息',
		self::COMMAND_WEIXIN_180260 => '通用延时消息',
		self::COMMAND_WEIXIN_180270 => '通用延时消息',
		self::COMMAND_WEIXIN_180280 => '通用延时消息',
		self::COMMAND_WEIXIN_180290 => '通用延时消息',
		self::COMMAND_WEIXIN_180300 => '通用延时消息',
		self::COMMAND_WEIXIN_180310 => '通用延时消息',
		self::COMMAND_WEIXIN_180320 => '通用延时消息',
		self::COMMAND_WEIXIN_180330 => '通用延时消息',
		self::COMMAND_WEIXIN_180340 => '通用延时消息',
		self::COMMAND_WEIXIN_180350 => '通用延时消息',
		self::COMMAND_WEIXIN_180360 => '通用延时消息',
		self::COMMAND_WEIXIN_180370 => '通用延时消息',
		self::COMMAND_WEIXIN_180380 => '通用延时消息',
		self::COMMAND_WEIXIN_180390 => '通用延时消息',
		self::COMMAND_WEIXIN_180400 => '通用延时消息',
		self::COMMAND_WEIXIN_180430 => '通用延时消息',
		self::COMMAND_WEIXIN_180460 => '通用延时消息',
        self::COMMAND_WEIXIN_180461 => '发版',

		self::COMMAND_WEIXIN_181100 => '通用延时消息',

        self::COMMAND_WEIXIN_181150 => '记录退群信息',

        self::COMMAND_WEIXIN_181200 => 'ticker发送任务到NMQ',
        self::COMMAND_WEIXIN_181202 => 'ticker_kpactor发送任务到NMQ',
        self::COMMAND_WEIXIN_181203 => 'ticker发送任务到NMQ', //图片上传使用


        self::COMMAND_WEIXIN_182100 => '微信群信息变更',
        self::COMMAND_WEIXIN_182101 => '活动信息变更',
        self::COMMAND_WEIXIN_182201 => '机器人信息变更',


        self::COMMAND_QYWX_182000   => '企微个人回调',
        self::COMMAND_QYWX_182001   => '企微消息',
        self::COMMAND_QYWX_182002   => '企微群相关',
        self::COMMAND_QYWX_182003   => '企微通讯录',
        self::COMMAND_QYWX_182004   => '企微发送群公告',
        self::COMMAND_QYWX_182005   => '企微标签相关',
        self::COMMAND_QYWX_182006   => '企微朋友圈相关',

        self::COMMAND_QYWX_182099   => '企微官方事件回调透传',

        self::COMMAND_QYWX_182300   => '企微好友扩容',
        self::COMMAND_QYWX_182301   => '企微好友欢迎语',
        self::COMMAND_QYWX_182302   => '企微自动通过',
        self::COMMAND_QYWX_182303   => '企微群欢迎语',
        self::COMMAND_QYWX_182304   => '企微建群拉人',
        self::COMMAND_QYWX_182305   => '企微群违规',
        self::COMMAND_QYWX_182306   => '企微关键词自动回复',
        self::COMMAND_QYWX_182307   => '企微群发',
        self::COMMAND_QYWX_182308   => '企微自动反确认',
        self::COMMAND_QYWX_182309   => '企微群剧本',
        self::COMMAND_QYWX_182310   => '企微群发的批量群公告',
        self::COMMAND_QYWX_182311   => '企微备注',
        self::COMMAND_QYWX_182312   => '企微批量加好友',
        self::COMMAND_QYWX_182313   => '企微批量任务',

        self::COMMAND_WEIXIN_182600 => '微信批量任务',

        self::COMMAND_WEIXIN_182700 => '养号系统专用',
        self::COMMAND_WEIXIN_182701 => '养号系统专用',

        self::COMMAND_ENGINE_182800 => '引擎状态回调',

        self::COMMAND_KPQQ_183000 => 'QQ个人回调',
        self::COMMAND_KPQQ_183001 => 'QQ消息回调',
        self::COMMAND_KPQQ_183002 => 'QQ群组回调',

        self::COMMAND_KPSMS_184000 => '接码服务',
        self::COMMAND_KPSMS_184001 => '接码服务换绑手机号',

        self::COMMAND_WORK_ORDER_185001 => '机房工单拉新回调',
        self::COMMAND_KP_ACCOUNT_186001 => '账号新增',
        self::COMMAND_KP_ACCOUNT_186002 => '账号更新',
        //设备
        self::COMMAND_KPDEVICE_187000 => '设备基础回调',
        self::COMMAND_KPDEVICE_187001 => '设备屏幕回调',
        self::COMMAND_KPDEVICE_187002 => '设备信息变动',

        self::COMMAND_WXQK_1882000 => '个人信息相关',
        self::COMMAND_WXQK_1882001 => '微信好友回调',
        self::COMMAND_WXQK_1882002 => '微信群回调',
        self::COMMAND_WXQK_1882003 => '微信消息回调',
        self::COMMAND_WXQK_1882004 => '微信系统回调',
        self::COMMAND_WXQK_1882005 => '建群通知',
        self::COMMAND_WXQK_1882006 => '标签回调',
        self::COMMAND_WXQK_1882007 => '朋友圈回调',
        self::COMMAND_WXQK_1882008 => '微信其他行为回调',
        self::COMMAND_WXQK_1882009 => '视频号回调',
        self::COMMAND_WXQK_1882010 => '自动建群处理延迟',
        self::COMMAND_WXQK_1882011 => '入群系统消息处理延迟',
        self::COMMAND_WXQK_1882012 => '群信息变动处理延迟',
        self::COMMAND_WXQK_1882013 => '收到机器人下架通知',
        self::COMMAND_WXQK_1882014 => '个微机器人账号同步',
        self::COMMAND_WXQK_1882015 => '个微机器人账号状态同步',
        self::COMMAND_WXQK_1882016 => 'qq机器人账号状态同步',
        self::COMMAND_WXQK_1882017 => 'qq机器人账号状态同步',
        self::COMMAND_WXQK_1882018 => '企微机器人账号状态同步',
        self::COMMAND_WXQK_1882019 => '企微机器人账号状态同步',
        self::COMMAND_WXSK_1882020 => '个人号方向通过好友申请处理延迟',
        self::COMMAND_WXQK_1882021 => '群控方向通过好友申请处理延迟',
        self::COMMAND_WXQK_1882030 => '群控方向更换群维护者处理延迟',
        self::COMMAND_WXQK_1882031 => '异步发送群消息',
        self::COMMAND_WXTB_1882022 => 'wxtb建群成功通知',
        self::COMMAND_WXTB_1882040 => '好友反确认',
        self::COMMAND_WXQK_1882042 => '微信通用工具',

        self::COMMAND_WXQK_1882034 => '群废弃通知',
        self::COMMAND_WXQK_1882035 => '回收群(活动维度)',
        self::COMMAND_WXTB_1882026 => '战队异动通知',
        self::COMMAND_WXTA_1882027 => '绑定战队成功通知',
        self::COMMAND_WXTA_1882025 => '增加lpc异动绑定战队成功通知',

        self::COMMAND_WXQK_1882029 => '群控业务通用延时处理',
        self::COMMAND_WXQK_1882033 => '群控通用异步',
        self::COMMAND_WXQK_1882036 => '群控侧更新线索通知',
        self::COMMAND_WXQK_1882037 => '视频号私信',
        self::COMMAND_WXOT_1882032 => '群控wxother接收公众号消息',

        self::COMMAND_WXQK_1882038 => "群控接收机器人之后通知其他业务方新增机器人",
        self::COMMAND_WXQK_1882039 => "群控同步机器人状态之后通知其他业务方",
        self::COMMAND_QLROBOT_1882041 => "企微运营者离职",
        self::COMMAND_QLROBOT_1882052 => "企微机器人下架",
        self::COMMAND_MASS_1882800 => '群发消息事件维度事件回调',
        self::COMMAND_MASS_1882801 => '群发消息群维度事件回调',
        self::COMMAND_MASS_1882802 => '群发消息成员维度事件回调',
        self::COMMAND_MASS_1882804 => '群发消息回调',

        self::COMMAND_WXQK_1882023 => '建群流程完成通知',

        self::COMMAND_WXQK_1882024 => '获取群二维码信息通知',
        self::COMMAND_WXTB_1882028 => 'wxtb流程处理完成通知',
        self::COMMAND_WXQK_1882998 => '用户进群通知',
        self::COMMAND_WXQK_1882999 => '群控方向日志收集',
        self::COMMAND_WXQK_1882060 => '进量切期通知',

        self::COMMAND_WXQK_1883031 => '添加微信ID黑白名单（策略使用）',
        self::COMMAND_WXQK_1883032 => '添加微信unionid黑白名单（策略使用）',
        self::COMMAND_WXQK_1883033 => '取消微信ID黑白名单（策略使用）',
        self::COMMAND_WXQK_1883034 => '添加微信unionid黑白名单（策略使用）',
        self::COMMAND_WXQK_1883035 => '系统消息（策略使用）',
        self::COMMAND_WXQK_1883036 => '群码回调（策略使用）',

        self::COMMAND_WXWORK_1884001 => '建群延迟消息',
        self::COMMAND_WXWORK_1884201 => '添加企业微信外部联系人通知',
        self::COMMAND_WXWORK_1884301 => '企微进群退群通知',
        self::COMMAND_WXWORK_1884302 => '企微信息更新',
        self::COMMAND_WXWORK_1884303 => '个人号好友通知',
        self::COMMAND_WXWORK_1884304 => 'WxWorkUser好友通知',
        self::COMMAND_WXWORK_1884501 => '对话剧本首轮消息发送结果通知',

        //csmsg
        self::COMMAND_WXCS_1884401   => 'wx客服系统消息rqm-wxcs',
        self::COMMAND_WXCS_1884402   => 'csmsg通过个人号业务方发送个微&企微消息结果回调',
        self::COMMAND_WXCS_1884403   => '客服工作台通过csmsg发送个微&企微消息结果回调',
        self::COMMAND_WXCS_1884404   => '策略strategy通过csmsg发送微&企微消息结果回调',

        self::COMMAND_WXEXECUTOR_1887001 => 1887001, //微信执行系统消息，主动消息回调
        self::COMMAND_WXEXECUTOR_1887002 => 1887002, //微信执行系统消息，被动消息
        self::COMMAND_WXGC_1887003 => '重试回调', //微信执行系统消息，被动消息
        self::COMMAND_WXGC_1887004 => 'WXGC回调自己',
        self::COMMAND_WXGC_1887100  => '群能力向外广播',
        self::COMMAND_WXGC_1887101  => '系统消息进群延迟处理',
        self::COMMAND_WXGC_1887200  => '进群向督学发nmq',
        self::COMMAND_WXGC_1887201  => '退群向督学发nmq',

        //运营
        self::COMMAND_YUNYING_190001 => '学生买课通知',
        self::COMMAND_YUNYING_190002 => '学生答完问卷通知',

        //拉新
        self::COMMAND_LAXIN_200000  => '创建线索',
        self::COMMAND_LAXIN_200001  => '新增例子',
        self::COMMAND_LAXIN_200002  => '更新例子',
        self::COMMAND_LAXIN_200003  => '分配tmk例子',
        self::COMMAND_LAXIN_200004  => '调整tmk例子',
        self::COMMAND_LAXIN_200005  => '分配sc例子',
        self::COMMAND_LAXIN_200006  => '调整sc例子',
        self::COMMAND_LAXIN_200007  => '例子失效',
        self::COMMAND_LAXIN_200008  => '例子转化',
        self::COMMAND_LAXIN_200009  => 'SC转化',
        self::COMMAND_LAXIN_200010  => 'SCTmk例子失效',
        self::COMMAND_LAXIN_200011  => 'Tmk例子到课转化',
        self::COMMAND_LAXIN_200012  => '绩效用户维度目标pv通知',
        self::COMMAND_LAXIN_200013  => '绩效SC维度通知',
        self::COMMAND_LAXIN_200014  => '客户转化通知',
        self::COMMAND_LAXIN_200020  => '例子转化-发送到绩效模块',
        self::COMMAND_LAXIN_200022  => '用户完成体验',
        self::COMMAND_LAXIN_200023  => '用户进入教室',
        self::COMMAND_LAXIN_200024  => '用户离开教室',
        self::COMMAND_LAXIN_200030  => '例子渠道线索',
        self::COMMAND_LAXIN_200031  => '用户0元购课通知',
        self::COMMAND_LAXIN_200032  => 'LEC备注手机号',
        self::COMMAND_LAXIN_200040  => '学习报告推送',
        self::COMMAND_LAXIN_200050  => '新增通话数据',
        self::COMMAND_LAXIN_200051  => '回传通话数据',
        self::COMMAND_LAXIN_200052  => '自动外呼通话完成',
        self::COMMAND_LAXIN_200053  => '通话录音文件同步',
        self::COMMAND_LAXIN_200054  => 'NLP录音文字分析完成',
        self::COMMAND_LAXIN_200060  => 'tmk给例子约课',
        self::COMMAND_LAXIN_200061  => '用户愿加微信',
        self::COMMAND_LAXIN_200062  => '用户已加微信',
        self::COMMAND_LAXIN_200063  => '人员组织架构调整',
        self::COMMAND_LAXIN_200066  => '督学更换资产使用人',
        self::COMMAND_LAXIN_200064  => '人员微信资产变更',
        self::COMMAND_LAXIN_200065  => 'LPC负责的课程转移',
        self::COMMAND_LAXIN_200070  => '需要智能催到课的用户',
        self::COMMAND_LAXIN_200071  => '智能催到课用户课程信息',
        self::COMMAND_LAXIN_200072  => 'laxincore收敛新建章节命令',
        self::COMMAND_LAXIN_200073  => 'laxincore收敛修改章节命令',
        self::COMMAND_LAXIN_200074  => '督学lpc微信信息修改',
        self::COMMAND_LAXIN_200075  => '用户课程信息发生变更通知阿卡流斯',
        self::COMMAND_LAXIN_200076  => '已批量发邮件',
        self::COMMAND_LAXIN_200077  => '已批量发送短信',
        self::COMMAND_LAXIN_200078  => '智能催到课创建呼叫用户任务',
        self::COMMAND_LAXIN_200079  => '智能催到课触发短信发送逻辑',
        self::COMMAND_LAXIN_200080  => '算法工程微信消息回调',
        self::COMMAND_LAXIN_200081  => '同步云学堂组织关系',
        self::COMMAND_LAXIN_200082  => '智能催到课识别骚扰意图',
        self::COMMAND_LAXIN_200090  => '用户添加LPC微信',
        self::COMMAND_LAXIN_200083  => '暴力催加微',
        self::COMMAND_LAXIN_200084  => '例子调配通知批改系统',
        self::COMMAND_LAXIN_200085  => 'LPC的不同微信间例子调配',
        self::COMMAND_LAXIN_200086  => '智能外呼用户事件通知',
        self::COMMAND_LAXIN_200087  => '智能外呼用户推送',
        self::COMMAND_LAXIN_200015  => '同步LEC云学堂组织关系',
        self::COMMAND_LAXIN_200016  => 'LEC绩效变化通知',
        self::COMMAND_LAXIN_200025  => 'LEC状态变更',
        self::COMMAND_LAXIN_200026  => 'LEC线索成单',

        //浣熊到课相关
        self::COMMAND_CORE_200101  => '浣熊学生上线',
        self::COMMAND_CORE_200102  => '浣熊学生下线',
        self::COMMAND_CORE_200122  => '浣熊学生到课命令',
        //任务系统
        self::COMMAND_TASK_820001   => '签到完成',
        self::COMMAND_TASK_820002   => '完成测试',
        self::COMMAND_TASK_820003   => '生成课堂报告',
        self::COMMAND_TASK_820004   => '绑定解绑测试',
        self::COMMAND_TASK_820005   => '新增学生绑定(820002后续逻辑之一)',
        self::COMMAND_TASK_824001   => '任务缓存清理',
        self::COMMAND_TASK_824002   => '测试提交、任务完成',
        self::COMMAND_TASK_824003   => '小学测试提交勋章统计',
        self::COMMAND_TASK_824004   => '任务奖励发放',
        self::COMMAND_TASK_824005   => '直播回放时长统计',
        self::COMMAND_TASK_824006   => '勋章观察者通知',
        self::COMMAND_TASK_824007   => '数据统计',
        self::COMMAND_TASK_824008   => '新增学生任务(824002后续逻辑)',
        self::COMMAND_TASK_824009   => '更新小语日积月累任务',
        self::COMMAND_TASK_824010   => '完成小语日积月累任务',

        // 阿童木
        self::COMMAND_ATOM_200201 => '命中备用池转移账号到可用池',
        self::COMMAND_ATOM_200202 => '账号使用过后下一次重新使用',

        // 班主任
        self::COMMAND_ASSISTANT_210001 => '班主任更换业务账号',
        self::COMMAND_ASSISTANT_210002 => '班主任添加业务账号',
        self::COMMAND_ASSISTANT_210003 => '班主任手机号解封、封禁',
        self::COMMAND_ASSISTANT_210004 => '业务账号更换使用者',
        self::COMMAND_ASSISTANT_210005 => '人员管理班主任账号封禁、解封',
        self::COMMAND_ASSISTANT_210006 => '炫星对接消息通知',
        self::COMMAND_ASSISTANT_210007 => '账号维度新增、编辑',

        self::COMMAND_ASSISTANT_211000 => '更换班主任',
        self::COMMAND_ASSISTANT_211001 => '没有找到学生类型',
        self::COMMAND_ASSISTANT_211002 => '没有学生类型,通知大数据组',
        self::COMMAND_ASSISTANT_211003 => '维护老生原班关系',
        self::COMMAND_ASSISTANT_211004 => '原班续报关系通知billing',
        self::COMMAND_ASSISTANT_211005 => '课程绑定解绑辅导老师',
        self::COMMAND_ASSISTANT_211006 => '分小班',
        self::COMMAND_ASSISTANT_212000 => '排灌班邮件',
        self::COMMAND_ASSISTANT_213000 => '订正系统回调测试系统订正结果',
        self::COMMAND_ASSISTANT_213001 => '订正系统兼职接收批改数据',
        self::COMMAND_ASSISTANT_213002 => '订正系统发送物料更新指令-acls接收',
        self::COMMAND_ASSISTANT_213101 => '订正系统兼职作业批改中改为待批改',
        self::COMMAND_ASSISTANT_214000 => '拆分微信生态1881007命令，直播公众号粉丝取关关注',
        self::COMMAND_ASSISTANT_214001 => '发送挽单短息消息',
        self::COMMAND_ASSISTANT_214002 => '发送挽单站内通知消息',
        self::COMMAND_ASSISTANT_214003 => '发送挽单钉钉消息',
        self::COMMAND_ASSISTANT_214004 => '分配辅导老师',

        self::COMMAND_ASSISTANT_215000 => '跟课学生列表统计业务访问次数',

        self::COMMAND_ASSISTANT_216002 => '资产账号基本信息编辑',
        self::COMMAND_ASSISTANT_216003 => '资产账号换绑/解绑时发送',
        self::COMMAND_ASSISTANT_216100 => '增加真人帐号',

        self::COMMAND_TOUCHMIS_217001 => 'LPC灌班后发送催加微',
        self::COMMAND_TOUCHMIS_217002 => '触达接收实时电话外呼',
        self::COMMAND_TOUCHMIS_217003 => '触达实时电话状态回调',
        self::COMMAND_TOUCHMIS_217004 => '触达实时电话结果回调',
        self::COMMAND_TOUCHMIS_217005 => 'daa服务接收排班灌班数据',
        self::COMMAND_TOUCHMIS_217006 => '触达ivr外呼记录回调',
        self::COMMAND_TOUCHMIS_217100 => '触达批量短信',
        self::COMMAND_TOUCHMIS_217200 => '通知触达服务自动触达',

        //客服系统
        self::COMMAND_MISSERVICE_910001 => '客服通用',
        self::COMMAND_MISSERVICE_910002 => '客服通用',
        self::COMMAND_MISSERVICE_910003 => '客服通用',
        self::COMMAND_MISSERVICE_910004 => '客服通用',
        self::COMMAND_MISSERVICE_910005 => '七鱼通话记录拉取',
        self::COMMAND_MISSERVICE_910006 => '用户投诉黑名单',

        //自研工单系统
        self::COMMAND_SWAN_911001 => '自动任务',
        self::COMMAND_SWAN_911002 => '自动派单',

        // 质检中台, 920号段, 920001 ~ 920999
        self::COMMAND_QUALITYCHECK_920001 => '质检系统自跳命令',
        self::COMMAND_QUALITYCHECK_920002 => '质检系统自跳命令',
        self::COMMAND_QUALITYCHECK_920003 => '质检系统自跳命令',
        self::COMMAND_QUALITYCHECK_920004 => '质检系统自跳命令',
        self::COMMAND_QUALITYCHECK_920005 => '质检系统自跳命令',

        // 列表
        self::COMMAND_LISTING_230001 => '列表系统接收更新数据',
        self::COMMAND_LISTING_230002 => 'dar接收状态更新信息然后发送列表更新数据',
        self::COMMAND_LISTING_230003 => '列表系统接收列表更新数据新方式',
        self::COMMAND_LISTING_230004 => '列表系统接收列表更新发票状态',

        // 订单
        self::COMMAND_ORDER_230005 => '订单暂停转退款通知',
        self::COMMAND_LOPS_230006 => '锁单状态变更通知',
        self::COMMAND_LOPS_230007 => '锁单可锁单通知',


        //商品策略
        self::COMMAND_PRODUCT_240001 => '更新优惠券库存',

        // 支付相关
        self::COMMAND_PAY_250001 => '学币，一网通支付',
        self::COMMAND_PAY_250002 => '微信代扣, 签约|解约',
        self::COMMAND_PAY_250003 => '学币模拟第三方发起支付通知',
        self::COMMAND_PAY_250004 => '支付通知业务方',
        self::COMMAND_PAY_250005 => '支付系统业务流水',
        self::COMMAND_PAY_250006 => '支付回调一课业务线',
        self::COMMAND_PAY_250007 => '支付回调平台业务线',
        self::COMMAND_PAY_250008 => '支付回调它业务线',
        self::COMMAND_PAY_250009 => '单笔退款回调业务线',
        self::COMMAND_PAY_250010 => '批量退款回调业务线',
        self::COMMAND_PAY_250011 => '苹果凭证重试上报',
        self::COMMAND_PAY_250012 => '微信投诉推送',
        self::COMMAND_PAY_250013 => '资金相关的变动事件',
        self::COMMAND_PAY_250014 => '积分发放',
        self::COMMAND_PAY_250015 => '积分明细拆分处理',
        self::COMMAND_PAY_250016 => '招行间连异步查询',
        self::COMMAND_PAY_STB_250025 => '台账系统对账',
        self::COMMAND_PAY_250030 => '退款系统单笔回调业务线',
        self::COMMAND_PAY_250031 => '退款系统批量回调业务线',
        self::COMMAND_PAY_250032 => '退款系统请求渠道退款',

        //交易通知中心
        self::COMMAND_NOTIFIER_260001 => '执行通知推送',

        //zoms
        self::COMMAND_ZOMS_261001 => '下单结果通知',

        //订单聚合
        self::COMMAND_ONE_270001 => "订单中心或者售后中心向聚合系统推送信息",

        //学分
        self::COMMAND_SCORE_800003    => '教学积分变更',
        //学分
        self::COMMAND_SCORE_800004    => '教学积分变更，新学分服务使用',
        self::COMMAND_SCORE_800009    => '教学积分变更，试卷批改结束',
        self::COMMAND_ADD_NOTICE_801652 => '重要日志打点',
        //回放
        self::COMMAND_PLAYBACK_810001 => '章节回放表双写',
        self::COMMAND_PLAYBACK_810002 => '学生章节回放表双写',
        self::COMMAND_PLAYBACK_810110 => '写录制课回放日志',
        self::COMMAND_PLAYBACK_810111 => '录制课信令转换',

        self::COMMAND_PLAYBACK_810003 => '学生观看回放时间段',
        self::COMMAND_PLAYBACK_810004 => '回放续期',
        self::COMMAND_PLAYBACK_810005 => '章节维度回放数据更新for acls',
        self::COMMAND_PLAYBACK_810006 => '章节回放-共享直播videomap更新',
        self::COMMAND_PLAYBACK_810501 => '回放备份信令写入（来自端）',
        self::COMMAND_PLAYBACK_810502 => '回放signalV2加工',
        self::COMMAND_PLAYBACK_810503 => '回放流媒体返回数据',
        self::COMMAND_PLAYBACK_810601 => '回放生产任务状态变更',
        self::COMMAND_PLAYBACK_810602 => '回放生产任务状态变更完成',

        // acls预热相关
        self::COMMAND_PLAYBACK_810120 => 'acls录播资源预热',
        self::COMMAND_PLAYBACK_810121 => 'acls录播资源课件预热',

        //试听课到课相关数据
        self::COMMAND_ZHIBO_810010 => '试听课到课相关数据',


        //红包
        self::COMMAND_RED_823333       => '红包异步入库',

        //答题卡异步入库
        self::COMMAND_QE_823334       => '答题卡异步入库',

        //学生进入教室
        self::COMMAND_ENTER_CONTENT_823335=>'进入教室配置异步写入',
        //是否卡
        self::COMMAND_YESNOCARD_START_823001 => '是否卡开始异步记日志',
        self::COMMAND_YESNOCARD_START_823002 => '是否卡结束异步记日志',

        //连麦
        self::COMMAND_MIC_ON_823003     => '开始连麦功能异步记日志',
        self::COMMAND_MIC_OFF_823004    => '结束连麦功能异步记日志',

        //表扬
        self::COMMAND_PRAISE_823005          => '表扬学生异步记日志',
        self::COMMAND_PRAISE_823007             =>'表扬榜异步写数据',

        //2020暑秋发布会随即学分礼包
        self::COMMAND_SEND_SCORE_823008           => '学分礼包发放',

        //签到
        self::COMMAND_COMMITSIGNIN_823006     => '签到异步处理',

        //试卷系统
        self::COMMAND_TEST_830001 => '课下跟读上传录音到bos',
        self::COMMAND_TEST_830002 => '学生提交测试',
        self::COMMAND_TEST_830003 => '测试系统异步提交',
        self::COMMAND_TEST_830004 => '提交结束',
        self::COMMAND_TEST_830005 => '延迟删除缓存',
        self::COMMAND_TEST_830006 => '异步个性化推题',
        self::COMMAND_TEST_830007 => '个性化推题结束',
        self::COMMAND_TEST_830008 => '自由作答提交',
        self::COMMAND_TEST_830009 => '提交信令',
        self::COMMAND_TEST_830010 => '试卷或模版变更同步信令（临时）',

        //试卷中台
        self::COMMAND_EXAM_CORE_840001 => '试卷绑定关系变化通知',
        self::COMMAND_EXAM_CORE_840002 => '试卷信息变化通知',
        self::COMMAND_EXAM_CORE_840003 => '作答信息变化通知',

        // 课堂配置
        self::COMMAND_TARTARUS_ACLS_850001 => '课堂配置acls缓存更新',


        //物流相关
        self::COMMAND_WMS_300001 => "往发网同步商品",
        self::COMMAND_WMS_300002 => "同步退貨信息到发网",
        self::COMMAND_WMS_300003 => "同步包裹信息到仓库(京东)",
        self::COMMAND_WMS_300014 => "同步包裹信息到仓库(其他)",
        self::COMMAND_WMS_300015 => "菜鸟订阅包裹状态",
        self::COMMAND_WMS_300004 => "同步调拨单据到发网",
        self::COMMAND_WMS_300005 => "处理包裹到第三方物流",
        self::COMMAND_WMS_300006 => "同步报损出库到发网",
        self::COMMAND_WMS_300007 => "同步手动出库到发网",
        self::COMMAND_WMS_300008 => "同步单据取消到发网",
        self::COMMAND_WMS_300009 => "物流状态回调",
        self::COMMAND_WMS_300010 => "库存信息回调",
        self::COMMAND_WMS_300011 => "物资修改回调",
        self::COMMAND_WMS_300012 => "包裹状态更新",
        self::COMMAND_WMS_300013 => "取消包裹对应的退货单",
	    self::COMMAND_WMS_300016 => "订单状态更新",
	    self::COMMAND_WMS_300017 => "库存操作complete",
	    self::COMMAND_WMS_300018 => "库存操作cancel",
        self::COMMAND_WMS_300020 => "库存信息通知回调",
        self::COMMAND_WMS_300021 => "同步退货入库单到易库",
        self::COMMAND_WMS_300022 => "第三方库内状态回调通知",
        self::COMMAND_WMS_300023 => "第三方入库单详情回调通知",
        self::COMMAND_WMS_300024 => "库存信息通知回调",
	    self::COMMAND_WMS_300025 => "异步发货出库库存处理",
	    self::COMMAND_WMS_300026 => "物流异步落单",
	    self::COMMAND_WMS_300027 => "领用申请单合单",
        self::COMMAND_WMS_300028 => "异步批量落单",
        self::COMMAND_WMS_300029 => "备用锁定库存",
        self::COMMAND_WMS_300043 => "生产履约单状态更新",
        self::COMMAND_WMS_300044 => "异步发送nmq通知物流履约接单",
        self::COMMAND_WMS_300045 => "异步接收订单状态变更",
        self::COMMAND_WMS_300047 => "异步处理采购入库单",
        self::COMMAND_WMS_300048 => "回滚中央库存",
        self::COMMAND_WMS_300049 => "wms异步记录业务日志",
        self::COMMAND_WMS_300050 => "wms异步打包处理",
        self::COMMAND_WMS_300033 => "wms异步同步nova订单",

        //库存
        self::COMMAND_STOCK_700001 => "库存预锁定[tcc-try]",
        self::COMMAND_STOCK_700002 => "库存核销[tcc-confirm]",
        self::COMMAND_STOCK_700003 => "业务线取消库存回滚[tcc-cancel]",
        self::COMMAND_STOCK_700004 => "缓存异常回滚数据落库",
        self::COMMAND_STOCK_700005 => "直接核销实物库存",

	    self::COMMAND_STOCK_7000021  => "zbstock 接收新库存服务同步 TCC-try",
		self::COMMAND_STOCK_7000022  => "zbstock 接收新库存服务同步 TCC-cancel",
		self::COMMAND_STOCK_7000023  => "zbstock 接收新库存服务同步 TCC-confirm",

		self::COMMAND_STOCK_7000011  => "zbstock 接收新库存服务同步 TCC-try",
		self::COMMAND_STOCK_7000012  => "zbstock 接收新库存服务同步 TCC-cancel",
		self::COMMAND_STOCK_7000013  => "zbstock 接收新库存服务同步 TCC-confirm",
	    self::COMMAND_STOCK_7000015  => "新库存服务库存退还命令",
        self::COMMAND_STOCK_7000016  => "新库存兑换码系统解封其他sku库存",
        self::COMMAND_STOCK_7000017  => "新库存兑换码作废、失效, 退款",
        self::COMMAND_STOCK_7000064  => "通知ares库存发生变化",

        //商品化数据 同步ES
        self::COMMAND_STOCK_700010 => '数据同步新增 spu Es',
        self::COMMAND_STOCK_700011 => '数据同步编辑 spu Es',
        self::COMMAND_STOCK_700012 => '数据同步新增 sku Es',
        self::COMMAND_STOCK_700013 => '数据同步编辑 sku Es',

        //商品平台化 商品平台提交层
        self::COMMAND_STOCK_700015 => '商品平台提交层',
        self::COMMAND_CLEAN_700021 => '清理平台商品缓存',
		self::COMMAND_EVENT_700022 => '商品强一致mq',
		self::COMMAND_SALEBIZ_700023 => '售卖策略变更同步 bizbuilder ES',

        self::COMMAND_GOODS_OPERATION_LOG_700030 => '商品中台操作日志',

		//限购
		self::COMMAND_PLATO_LIMIT_BUY_710000 => 'P维度限购落库命令',

        //渠道相关
        self::COMMAND_CHANNEL_500001 => "渠道对接命令号",
        self::COMMAND_CHANNEL_500002 => "广告对应",
        self::COMMAND_CHANNEL_500003 => "渠道登录线索命令号",
        self::COMMAND_CHANNEL_500004 => "交易完成通知投放",
        self::COMMAND_CHANNEL_500005 => "渠道发券异步通知命令",
        self::COMMAND_CHANNEL_500006 => "渠道下单命令",
        self::COMMAND_CHANNEL_500007 => "渠道小程序二维码命令",
        self::COMMAND_CHANNEL_500018 => "渠道微信消息接收",

        //直播
        self::COMMAND_LIVESTATION_870002 => '流媒体互动信令处理',
        self::COMMAND_LIVESTATION_870003 => '小数mvp回放数据生成',

        //889号段为Achilles系统专用
        self::COMMAND_ACHILLES_889001 => "用户特权信息",
        self::COMMAND_ACHILLES_889002 => "MVP课程信息",
        self::COMMAND_ACHILLES_889003 => "子订单信息",
        self::COMMAND_ACHILLES_889004 => "学生课程错题本",
        self::COMMAND_ACHILLES_889005 => "课程任务奖励配置",
        self::COMMAND_ACHILLES_889006 => "学生领取奖励情况",
        self::COMMAND_ACHILLES_889007 => "端上打点[Achilles计算到课时长]",
        self::COMMAND_ACHILLES_889008 => "课程的分班列表",
        self::COMMAND_ACHILLES_889009 => "重点关注列表",
        self::COMMAND_ACHILLES_889010 => "课程推荐信息",
        self::COMMAND_ACHILLES_889011 => "章节配置信息",
        self::COMMAND_ACHILLES_889012 => "章节直播状态",
        self::COMMAND_ACHILLES_889013 => "拉流地址变更",
        self::COMMAND_ACHILLES_889014 => "学生组信息变更",
        self::COMMAND_ACHILLES_889015 => "更新课程报告",
        self::COMMAND_ACHILLES_889016 => "组信息变更",
        self::COMMAND_ACHILLES_889017 => "房间组信息变更",
        self::COMMAND_ACHILLES_889018 => "端上打点[Achilles计算引擎接收]",
        self::COMMAND_ACHILLES_889020 => "课程体系信息更新",
        self::COMMAND_ACHILLES_889021 => "课程目标信息更新",
        self::COMMAND_ACHILLES_889022 => "teacherconfig更新", // teacherconfig更新
        self::COMMAND_ACHILLES_889023 => "计算引擎发送明细",
        self::COMMAND_ACHILLES_889024 => "sparta清楚acls缓存",
        self::COMMAND_ACHILLES_889025 => "inclass 通知achilles更新三峡html数据",
        // 891号段教学前台使用
        self::COMMAND_JXFRONT_891001 => "接口重构过程中异步diff",
        self::COMMAND_JXFRONT_891002 => "弱缓存-预热学生",
        self::COMMAND_JXFRONT_891003 => "弱缓存-业务方推送数据",
        self::COMMAND_JXFRONT_891004 => "弱缓存-拉取业务方数据",
        self::COMMAND_JXFRONT_891005 => "教学前台相关公众号关注取关(垂直拆分1881007)",
        self::COMMAND_JXFRONT_891006 => "小鹿写字相关公众号关注取关(垂直拆分1881007)",
        self::COMMAND_JXFRONT_891007 => "小鹿写字相关小程序消息相关(垂直拆分1881008)",
        self::COMMAND_JXFRONT_891008 => "旁听课-预热旁听课数据",
        self::COMMAND_JXFRONT_891009 => "旁听课-预热每个年级的旁听列表",
        self::COMMAND_JXFRONT_891010 => "客户端巩固练习提交节点-巩固练习课下排行榜",

        // 892号段ridge模块使用(业务前台)
        self::COMMAND_RIDGE_892001 => "课中营销-章节营销信息变动",
        self::COMMAND_RIDGE_892002 => "课中营销-素养课可以上架推送",
        self::COMMAND_RIDGE_892003 => "计数器-计数器持久化信令",
        self::COMMAND_RIDGE_892101 => "课中营销-章节营销信息变动-增",
        self::COMMAND_RIDGE_892102 => "课中营销-章节营销信息变动-删",
        self::COMMAND_RIDGE_892103 => "课中营销-章节营销信息变动-改",
        // 893号段oppos模块使用(业务前台)
        self::COMMAND_OPPOS_893101 => "app配置-配置保存",
        self::COMMAND_OPPOS_893102 => "合规配置-屏障修改",
        self::COMMAND_OPPOS_893103 => "合规配置-屏障修改-上课页app",
        self::COMMAND_OPPOS_893104 => "履约配置-上课页",
        self::COMMAND_OPPOS_893105 => "履约配置-课程主页-白名单配置",
        self::COMMAND_OPPOS_893106 => "履约配置-课程主页-策略配置",
        self::COMMAND_OPPOS_893107 => "履约配置-课程主页-模板",

        //zbtiku
        self::COMMAND_ZBTIKU_960001 => "zbtiku前置打标签异步投产",
        self::COMMAND_ZBTIKU_960002 => "zbtiku异步打点",
        self::COMMAND_ZBTIKU_960003 => "zbtiku生产配置化trace",
        self::COMMAND_ZBTIKU_960004 => "zbtiku异步同步生产队列优先级",
        self::COMMAND_ZBTIKU_960005 => "zbtiku异步打点同步到业务统计",
        self::COMMAND_ZBTIKU_960006 => "zbtiku题源侧项目看板统计",
        self::COMMAND_ZBTIKU_960007 => "zbtiku订单异步拆题",
        self::COMMAND_ZBTIKU_960008 => "zbtiku扩展题源质检数据看板",
        self::COMMAND_ZBTIKU_960009 => "zbtiku纠错修改发送给课中",

        // 活动平台 (1110001--1119999)
        self::COMMAND_ACTPLATFORM_1110001 => '同步蜂鸟打点数据到斑马大数据',
        self::COMMAND_ACTPLATFORM_1110002 => '新蜂鸟页面发布',
        self::COMMAND_ACTPLATFORM_1110003 => '答题',
        self::COMMAND_ACTPLATFORM_1110004 => '蜂鸟表单数据同步给第三方',
        self::COMMAND_ACTPLATFORM_1110005 => '蜂鸟同步抽奖核销',
        self::COMMAND_ACTPLATFORM_1110006 => '蜂鸟同步抽奖库存更新',
        self::COMMAND_ACTPLATFORM_1110007 => '新蜂鸟活动发布',
        self::COMMAND_ACTPLATFORM_1110008 => '新蜂鸟课中抽奖适配器',
        self::COMMAND_ACTPLATFORM_1110009 => '新蜂鸟课中抽奖适配器2',
        self::COMMAND_ACTPLATFORM_1110010 => '组队通知',
        self::COMMAND_ACTPLATFORM_1110011 => '新蜂鸟异步命令',
        self::COMMAND_ACTPLATFORM_1110012 => '新蜂鸟下单命令',
        self::COMMAND_ACTPLATFORM_1110015 => '蜂鸟空命令，为了解决sell集群偏移量问题，后续待删除',
        self::COMMAND_ACTPLATFORM_1110016 => '蜂鸟接收微信消息推送',

        // 活动引擎
        self::COMMAND_ACTENGINE_1110101 => '发放优惠券[活动引擎]',
        self::COMMAND_ACTENGINE_1110102 => '海报审核结果通知[活动引擎]',

        //阿拉丁相关
        self::COMMAND_XENG_600001 => "语音合成",
        self::COMMAND_XENG_600002 => "试卷提交",
        self::COMMAND_XENG_600005 => "更新未完成环节",
        self::COMMAND_XENG_600004 => "生成知识点巩固",
        self::COMMAND_XENG_600006 => "异步双写",

        //鸭鸭英语
        self::COMMAND_DIYOU_610007 => "用户注销",
        self::COMMAND_DIYOU_610008 => "微信服务消息",
        self::COMMAND_DIYOU_610009 => "十分钟分钟延迟任务",

        self::COMMAND_BUSINESS_620001 => "商业活动游戏pk延时1s",
        self::COMMAND_BUSINESS_620002 => "商业活动游戏pk延时2s",
        self::COMMAND_BUSINESS_620003 => "商业活动游戏pk延时3s",
        self::COMMAND_BUSINESS_620004 => "商业活动游戏pk延时4s",
        self::COMMAND_BUSINESS_620005 => "商业活动游戏pk延时5s",
        self::COMMAND_BUSINESS_620006 => "商业活动游戏pk延时6s",
        self::COMMAND_BUSINESS_620007 => "商业活动游戏pk延时7s",
        self::COMMAND_BUSINESS_620008 => "商业活动游戏pk延时8s",
        self::COMMAND_BUSINESS_620009 => "商业活动游戏pk延时9s",
        self::COMMAND_BUSINESS_620010 => "商业活动游戏pk延时10s",
        self::COMMAND_BUSINESS_620012 => "商业活动游戏pk延时12s",
        self::COMMAND_BUSINESS_620013 => "商业活动游戏pk延时13s",
        self::COMMAND_BUSINESS_620014 => "商业活动游戏pk延时14s",
        self::COMMAND_BUSINESS_620101 => "商业活动游戏pk开启下一轮 第一轮 16秒",
        self::COMMAND_BUSINESS_620102 => "商业活动游戏pk开启下一轮 第二轮 14秒",
        self::COMMAND_BUSINESS_620103 => "商业活动游戏pk开启下一轮 第三轮 14秒",
        self::COMMAND_BUSINESS_620104 => "商业活动游戏pk开启下一轮 第四轮 14秒",
        self::COMMAND_BUSINESS_620105 => "商业活动游戏pk开启下一轮 第五轮 14秒",
        self::COMMAND_BUSINESS_621001 => "商业活动游戏pk作答异步入库",
        self::COMMAND_BUSINESS_621002 => "商业活动游戏pk结算异步入库",
        self::COMMAND_BUSINESS_621003 => "商业活动游戏pk更新排行榜",
        self::COMMAND_BUSINESS_621004 => "商业活动游戏金币LOG异步入库",
        self::COMMAND_BUSINESS_621005 => "商业活动游戏异步更新用户信息",

        self::COMMAND_CHAT_800001 => '课中聊天',

        self::COMMAND_COURSESEARCH_2000001 =>'课程检索',
        self::COMMAND_COURSESEARCH_2000002 => '查询数据检测',

        //课件相关
        self::COMMAND_KEJIAN_980001 => '课件绑定生成zip包后通知下游变更缓存',
        self::COMMAND_KEJIAN_980002 => '课件生成前端程序包后通知下游变更缓存',
        self::COMMAND_KEJIAN_980003 => '通知互动生成互动课件',
        self::COMMAND_KEJIAN_980004 => '通知下游绑定关系变更',
        self::COMMAND_KEJIAN_980005 => '通知下游审核结果',
        self::COMMAND_KEJIAN_980006 => '课件异步发送邮件',
        self::COMMAND_KEJIAN_980007 => '直播间异动课件异步发送章节信息',
        self::COMMAND_KEJIAN_980008 => '课件zip资源生成后通知自动化巡检',
        self::COMMAND_KEJIAN_980009 => '课件异动解绑报警',
        self::COMMAND_KEJIAN_980010 => '课件生产生命周期mq',
        self::COMMAND_KEJIAN_980011 => '课件相关资源更新通知(小鹿编程，cocos题互动题等)',
        self::COMMAND_KEJIAN_980012 => '课件解绑通知(小鹿编程，cocos题互动题等)',
        self::COMMAND_KEJIAN_980013 => 'CDN预热',
        self::COMMAND_KEJIAN_980014 => 'Kj纬度检查',
        self::COMMAND_KEJIAN_980015 => 'Lesson纬度检查',
        self::COMMAND_KEJIAN_980016 => '课件打包',
        self::COMMAND_KEJIAN_980020 => '课件资源更新',

        self::COMMAND_COCOS_981000 => 'cocos堂堂测包更新',

        self::COMMAND_ZB_LECTURE_NOTES_982000 => '讲义自动化任务结果通知',

        //课件检测平台
        self::COMMAND_JYCHECK_990001 => '校验平台结果通知',

        // 教研AI视频转码回调失败通知
        self::COMMAND_JYAI_991001 => '教研AI视频转码回调失败通知',
        self::COMMAND_JYAI_991002 => '音频信息变更通知achilles',


        // 教研任务生产系统
        self::COMMAND_JYTASK_950001 => "教研任务数据缓存到achilles",
        self::COMMAND_JYTASK_950002 => "教研任务数据更新通知雪球",

        // reportcore
        self::COMMAND_REPORTCORE_951000 => '生成报告',

        //互动中台
         self::COMMAND_INTERACT_831001 => "教师端发送互动",
         self::COMMAND_INTERACT_831002 => "教师端互动状态修改",
         self::COMMAND_INTERACT_831003 => "学生端互动作答",
         self::COMMAND_INTERACT_831004 => "丢弃",
         self::COMMAND_INTERACT_831006 => "小班化单题表扬落库",
         self::COMMAND_INTERACT_831007 => "小班维度PK结果落库",
         self::COMMAND_INTERACT_831008 => "老师行为落库",
         self::COMMAND_INTERACT_831009 => "聊天内容落库",
         self::COMMAND_INTERACT_831010 => "更新学生作答内容相关",
         self::COMMAND_INTERACT_831011 => "更新聊天反作弊结果",
         self::COMMAND_INTERACT_831012 => "榜单发送信息",
         self::COMMAND_INTERACT_831013 => "记录榜单学生信息",
         self::COMMAND_INTERACT_831014 => "在线自习室提问信息",
         self::COMMAND_INTERACT_831015 => "触达次数",
         self::COMMAND_INTERACT_831024 => "称号入库",
         self::COMMAND_INTERACT_831025 => "能量值入库",
         self::COMMAND_INTERACT_831026 => "聊天内容落库(纳米)",

        //激励中台
        self::COMMAND_SIGNINSUBMIT_1140001 => "形象签到",
        // self::COMMAND_LABEL_1140002 => "学生称号",
        self::COMMAND_ENCOURAGE_FISH_1140003 => "小数激励加小鱼",
        self::COMMAND_ENCOURAGE_SCORE_1140004 => "小数激励加学分",
        self::COMMAND_ENCOURAGE_LABEL_1140005 => '小数激励称号更新',
        self::COMMAND_ENCOURAGE_REPORT_1140006 => '小数激励战绩',
        self::COMMAND_PRAISELIST_1140007 => '小数排行榜落库',
        self::COMMAND_PRAISELISTT_1140008 => '小数测试类排行榜数据提交',
        self::COMMAND_SCORE_ADD_FAIL_1140009 => '加分失败处理',
        self::COMMAND_SCORE_BUDGET_DISBURSE_1140011 => '预算学分分发',
        self::COMMAND_SCORE_WRITE_DB_800005 => '激励中台消费入库',

        //直播中台
        self::COMMAND_ROOMSTATUS_870001 => "房间直播状态",
        self::COMMAND_ROOMSTATUS_870004 => "用户班主任信息",
        self::COMMAND_ROOMSTATUS_870005 => "房间下的分组信息",
        self::COMMAND_ROOMSTATUS_870006 => "进入房间",

        self::COMMAND_LIVESTATION_870007 => "更新组织节点",
        self::COMMAND_LIVESTATION_870008 => "更新直播用户信息",
        self::COMMAND_LIVESTATION_870009 => "更新直播间信息",
        self::COMMAND_LIVESTATION_870019 => "灰度白名单课程章节预热",
        self::COMMAND_LIVESTATION_870015 => "直播预热完成数据",
        self::COMMAND_LIVESTATION_870016 => "记录教师操作日志",
        self::COMMAND_LIVEUI_870018      => "直播间下课",
        self::COMMAND_LIVEUI_870023      => "直播间下课记录教师退出时间",

        self::COMMAND_JXDA_LIVESTATION_870010 => "更新achiles中的课程章节绑定组织策略缓存",
        self::COMMAND_JXDA_LIVESTATION_870011 => "更新achilles中的组织策略缓存",
        self::COMMAND_JXDA_LIVESTATION_870012 => "更新achilles中的元数据缓存",
        self::COMMAND_JXDA_LIVESTATION_870017 => "更新achilles中的直播间缓存",
        self::COMMAND_JXDA_LIVESTATION_870020 => "更新achilles中的直播间用户信息数据缓存",
        self::COMMAND_JXDA_LIVESTATION_870021 => "更新achilles中的直播间节点信息数据缓存",
        self::COMMAND_JXDA_LIVESTATION_870022 => "更新achilles中的直播间用户所在节点信息数据缓存",
        self::COMMAND_JXDA_LIVESTATION_870024 => "更新achilles中的章节原始id节点信息消息数据缓存",
        self::COMMAND_JXDA_LIVESTATION_870013 => "预热数据pre_live",
        self::COMMAND_JXDA_LIVESTATION_870014 => "更新挂载节点(比如：分班，灌班，换班)",
        self::COMMAND_JXDA_LIVESTATION_870025 => "检查预热数据消息体",
        self::COMMAND_JXDA_LIVESTATION_880006 =>  "直播中台单章节预热",
        self::COMMAND_JXDA_LIVESTATION_870026 => "学生完课通知acls",

        self::COMMAND_JX_CLASSGRAY_880020 => "纳米章节灰度变更通知",
        self::COMMAND_JX_CLASSGRAY_880021 => "纳米转发教务310001命令，增加共享章节分组",

        self::COMMAND_JXDA_LIVESTATION_870040 => "更新浣熊班主任跟课学生报警信息",
        self::COMMAND_JXDA_LIVESTATION_870041 => "更新浣熊跟课班主任遮盖记录",
        self::COMMAND_JXDA_LIVESTATION_870042 => "更新浣熊跟课班主任对报警的处理结果记录",

        self::COMMAND_ROOM_STATION_872001 =>  "课堂中台直播间下课",
        self::COMMAND_ROOM_STATION_872002 =>  "课堂中台room通知room中间表更新数据",
        self::COMMAND_ROOM_STATION_872003 =>  "课堂中台通知room中间表更新数据",

        // i_Lab
        self::COMMAND_ILAB_880001 => '更新用户目标完成度',
        self::COMMAND_ILAB_880002 => '通知计算引擎计算完成度',
        self::COMMAND_ILAB_880003 => 'MT后台更新目标信息',
        self::COMMAND_ILAB_880004 => 'ilababtest推题',

        // 堂堂测
        self::COMMAND_INCLAS_EXAM_888001 => '堂堂测下课',

        //主讲
        self::COMMAND_TEACHER_970001 => '更新fudao老师信息缓存',

        // 拼团 (1121000--1121999)
        self::COMMAND_TUAN_1121000 => '拼团时间到期未成团自动退款', // 拼团时间到期未成团自动退款
        self::COMMAND_TUAN_1121001 => '开团参团成功', // 团长是开团，团员是参团
        self::COMMAND_TUAN_1121002 => '拼团成功',
        self::COMMAND_TUAN_1121003 => '团解散',
        self::COMMAND_TUAN_1121004 => '拼团反作弊',
        self::COMMAND_TUAN_1121005 => '更新拼团商品的拼团售卖量',
        self::COMMAND_TUAN_1121006 => '拼团异步更新团成员信息',
        self::COMMAND_TUAN_1121007 => '拼团异步写入或删除团临时表数据',

        // 活动-结算(1122000--1122499)
        self::COMMAND_ACT_REWARD_1122000 => '活动系统奖励结算',

        // 分销 (1122500--1122999)
        self::COMMAND_FENXIAO_1122500    => '分销系统用户转化成功',
        self::COMMAND_FENXIAO_1122501    => '分销系统分销员奖励提醒',
        self::COMMAND_FENXIAO_1122502    => '分销系统分销员和新用户绑定成功通知',
        self::COMMAND_FENXIAO_1122503    => '分销系统转化取消',
        self::COMMAND_FENXIAO_1122504    => '分销系统设置转化状态并重算奖励',
        self::COMMAND_FENXIAO_1122505    => '分销系统重算礼品卡聚合',
        self::COMMAND_FENXIAO_1122506    => '分销系统计算奖励',
        self::COMMAND_FENXIAO_1122507    => '分销系统到课完课数更新通知',
        self::COMMAND_FENXIAO_1122508    => '招募员注册成功通知',
        self::COMMAND_FENXIAO_1122509    => '分销员注册成功通知',
        self::COMMAND_FENXIAO_1122510    => '用户符合参与门槛通知',
        self::COMMAND_FENXIAO_1122511    => '用户访问轨迹通知',
        self::COMMAND_FENXIAO_1122512    => 'check分销规则通知',

        // 活动平台 - 任务系统 (1122800 ~ 1122999)
        self::COMMAND_TASK_1122800 => '任务系统[履约奖励]',
        self::COMMAND_TASK_1122801 => '任务系统[触发行为，累计进度]',
        self::COMMAND_TASK_1122805 => '任务系统[蜂鸟行为过滤]',

        //billing组 ordersearch 1123000 ~1123099
        self::COMMAND_ORDERSEARCH_1123000 =>'[订单ES写入]异步修复数据',

        // 活动平台 - 礼品卡系统（1123100 ~ 1123199）
        self::COMMAND_GIFTCARD_1123100 => '礼品卡系统[下发礼品卡]',
        self::COMMAND_GIFTCARD_1123101 => '礼品卡系统[兑换奖品]',

        // 趣学习&趣课堂
        self::COMMAND_ZBINTERACT_666000 => '趣课件生成课件后对外通知',
        self::COMMAND_ZBINTERACT_666001 => '趣课件生成课件压缩包后对外通知',
        self::COMMAND_ZBINTERACT_666002 => '趣学习异步打点通知',
        self::COMMAND_ZBINTERACT_666003 => '趣课件操作记录通知',
        self::COMMAND_ZBINTERACT_666004 => '趣课件预留通知',
        self::COMMAND_ZBINTERACT_666005 => '趣学习异步打包zip',
        self::COMMAND_ZBINTERACT_666006 => '趣学习学生打点记录',

        self::COMMAND_JYSERIES_666500 => '教研中台通知Achillse更新数据',

        //群发消息
        self::COMMAND_MASSIVE_MESSAGE_1270001 => '视频号转发状态回调',

        //lpc例子服务
        self::COMMAND_LPC_280001    => 'lpc例子创建',
        self::COMMAND_LPC_280002    => 'lpc例子更新',
        self::COMMAND_LPC_280005    => 'lpc例子分配',
        self::COMMAND_LPC_280006    => 'lpc例子调配',
        self::COMMAND_LPC_280009    => 'lpc例子转化',
        self::COMMAND_LPC_280010    => 'lpc例子失效',
        self::COMMAND_LPC_280011    => '调配中转通知',
        self::COMMAND_LPC_280012    => '失效中转通知',
        self::COMMAND_LPC_280013    => '灌班中转通知',
        self::COMMAND_LPC_280014    => '更新中转通知',
        self::COMMAND_LPC_280015    => '同一个辅导老师不同业务账号调配中转通知',
        self::COMMAND_LPC_280016    => '课程审批通知',
        self::COMMAND_LPC_280017    => '排班信息变更通知',
        self::COMMAND_LPC_280018    => '课程失效',
		self::COMMAND_LPC_280019    => '课程小班变更通知',
		self::COMMAND_LPC_280020    => '课程老师历史关系通知',
        self::COMMAND_LPC_280021    => '例子创建中转通知',
        self::COMMAND_LPC_280022    => '例子分配统一通知',
        self::COMMAND_LPC_280023    => '例子调配统一通知',
        self::COMMAND_LPC_280030    => '新增排班统一通知',
        self::COMMAND_LPC_280031    => '删除排班统一通知',
        self::COMMAND_LPC_280032    => '新增小班统一通知',

		//LPC - 工作台
        self::COMMAND_LPC_280100    => '学生异动通知',
        self::COMMAND_LPC_280101    => '赠课当天章节变动通知',
        self::COMMAND_LPC_280102    => '绑定与解绑用户',
        self::COMMAND_LPC_280103    => '扫描二维码',
        self::COMMAND_LPC_280104    => '微信生态小程序消息推送LPC',
        self::COMMAND_LPC_280105    => 'LPC赠课通知',
        self::COMMAND_LPC_280106    => '反确认绑定解绑通知',
        self::COMMAND_LPC_280107    => '例子失效后发送加微信息',

        self::COMMAND_LPC_280201    => '投放页标签通知',

        self::COMMAND_LPC_280301    => '退费挽单工单入库通知',

        //微信eco
        self::COMMAND_WECHAT_ECO_1880001 => '记录拉新小程序新用户&任务校验',
        self::COMMAND_WECHAT_ECO_1880002 => '拉新小程序，绘制答题结果页海报',
        self::COMMAND_WECHAT_ECO_1880003 => '微信生态 - 1 元听书解锁',
        self::COMMAND_WECHAT_ECO_1880004 => '微信生态-支付unionid打点',
        self::COMMAND_WECHAT_ECO_1880005 => '微信生态-1元听书',

        //微信wxmarket
        self::COMMAND_WECHAT_WXMARKET_1880501 => '微信生态-微信营销平台-用户活动海报生成',
        self::COMMAND_WECHAT_WXMARKET_1881888 => '微信生态-微信营销平台-公众号消息事件',

        //营销活动
        self::COMMAND_WECHAT_YX_1880601 => '任务宝-创建&发送海报',
        self::COMMAND_WECHAT_YX_1880602 => '任务宝-微信回调',
        self::COMMAND_WECHAT_YX_1880603 => '任务宝-告警',
        self::COMMAND_WECHAT_YX_1880621 => '任务宝-任务上报',
        self::COMMAND_WECHAT_YX_1880622 => '任务宝-互动链事件上报',
        self::COMMAND_WECHAT_YX_1880641 => '任务宝-库存不足通知',
        self::COMMAND_WECHAT_YX_1880661 => '任务宝-风控信息上报',

        //素材库
        self::COMMAND_WECHAT_YXCONTENT_1880701 => '素材库-调用信息上报',
        self::COMMAND_WECHAT_YXCONTENT_1880702 => '素材库-调用信息上报-新版',
        self::COMMAND_WECHAT_YXCONTENT_1880703 => '素材库-生成渠道链接上报',

        // 微信服务
        self::COMMAND_WECHAT_WXSERVER_1881001 => '绑定与解绑用户',
        self::COMMAND_WECHAT_WXSERVER_1881002 => '扫描二维码',
        self::COMMAND_WECHAT_WXSERVER_1881003 => '关键字回复',
        self::COMMAND_WECHAT_WXSERVER_1881004 => '作业帮一课加群服务',
        self::COMMAND_WECHAT_WXSERVER_1881005 => '作业帮账号更换手机号以及更换密码',
        self::COMMAND_WECHAT_WXSERVER_1881006 => '消息推送，用于大规模群发',
        self::COMMAND_WECHAT_WXSERVER_1881007 => '用户关注与取关事件',
        self::COMMAND_WECHAT_WXSERVER_1881008 => '小程序客服消息',
        self::COMMAND_WECHAT_WXSERVER_1881009 => '用户打标签',
        self::COMMAND_WECHAT_WXSERVER_1881010 => '全量拉取粉丝更新用户信息',
        self::COMMAND_WECHAT_WXSERVER_1881011 => '用户活跃行为',
        self::COMMAND_WECHAT_WXSERVER_1881012 => '外部打标签任务',
        self::COMMAND_WECHAT_WXSERVER_1881013 => '消息推送，用于实时业务',

        //微信生态消息推送系统
        self::COMMAND_WECHAT_WXPUSHER_1881101 => '消息系统推送模板消息',
        self::COMMAND_WECHAT_WXPUSHER_1881102 => '消息系统推送客服消息',
        self::COMMAND_WECHAT_WXPUSHER_1881103 => '消息系统推送订阅消息',
        self::COMMAND_WECHAT_WXPUSHER_1881201 => '消息系统推送模板消息日志',
        self::COMMAND_WECHAT_WXPUSHER_1881202 => '消息系统推送客服消息日志',
        self::COMMAND_WECHAT_WXPUSHER_1881203 => '消息系统推送订阅消息日志',

        self::COMMAND_WECHAT_ESMIDDLE_1881204 => 'ESMiddle批量异步写ES',
        self::COMMAND_WECHAT_ESMIDDLE_1881205 => 'wxservergo消息分发总线',
        self::COMMAND_WECHAT_WXWEB_1881301    => '小程序导流中间页透传参数实时同步',

        //微信消息PUSH系统
        self::COMMAND_WXMSG_1883001 => '消息接收nmq',
        self::COMMAND_WXMSG_1883002 => "任务状态通知",
        self::COMMAND_WXMSG_1883003 => "群信息变更通知",

        // 低幼业务线
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9000000 => '低幼练习作答完毕提交',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9000001 => '鸭鸭英语新语音通知',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9000010 => '低幼班主任排灌班消息',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9000011 => '低幼班主任新增班主任-排灌班异动NMQ',
        self::COMMAND_DIYOU_KID_CHINESE_9030101 => '鸭鸭语文 接收客服会话里用户发送的微信消息',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100000 => '低幼班主任消息推送',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100001 => '低幼班主任排灌班削峰队列',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100002 => '低幼灌班完成通知',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100003 => '低幼灌班完成通知',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100004 => '天鹰语音回调',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100005 => '发送短信',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100006 => '低幼取消灌班完成通知',
        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100007 => '通话记录手动标注',

        self::COMMAND_DIYOU_ASSISTANT_KIDTUTOR_9100011 => '低幼课程直播章节重开',

        //策略vip
        self::COMMAND_VIP_899001 => '策略发送vip异步入库',
        self::COMMAND_COUPON_UPDATE_899002 => '用户优惠券变更命令',
        self::COMMAND_SKU_GROUP_UPDATE_899003 => '商品限购组变更命令',
        self::COMMAND_COUPON_CODE_UPDATE_899004 => '兑换码变更命令',

        //购物车
        self::COMMAND_CART_1890000  => '购物车添加/删除完成通知',

        //基础架构
        self::COMMAND_INFRA_999000 => 'Sparta打包完成通知',
        self::COMMAND_INFRA_999001 => 'OCS集群策略变更通知',

        //0转正相关
        self::COMMAND_ZERO_1260001 => '0转正链接转化',
        self::COMMAND_ZERO_1261000 => '获得微信ID mapping信息',
        self::COMMAND_ZERO_1261001 => 'unionId-uid更新',



        //特提斯
        self::COMMAND_JXDA_ABGRAY_860001 => '添加/更新开关',
        self::COMMAND_JXDA_ABGRAY_860002 => '删除开关',
        self::COMMAND_JXDA_ABGRAY_860003 => '添加/更新强升任务',
        self::COMMAND_JXDA_ABGRAY_860004 => '删除强升任务',
        self::COMMAND_JXDA_ABGRAY_860005 => '选课定时任务',



        self::COMMAND_ZERO_1262001  => '活动备群通知',
        self::COMMAND_ZERO_1262002  => '绑定战队',
        self::COMMAND_ZERO_1262003  => '删除LPC',

        self::COMMAND_ZERO_1262004  => '先加微，后进群，加好友成功事件',
        self::COMMAND_ZERO_1262005  => '先加微，后进群，备群相关事件',
        self::COMMAND_ZERO_1262006  => '灌班异步任务事件',
        self::COMMAND_ZERO_1262007  => '异步备群',
        self::COMMAND_ZERO_1262008  => '先加微，后进群，获取二维码成功事件',
        self::COMMAND_ZERO_1262009  => 'lpc与用户的userCourse关系变换事件',

        self::COMMAND_ZERO_1262010  => '小程序直播间监控事件',

        //供应链scp
        self::SCP_SYNC_WMS_STOCK    => '同步wms仓库库存变更',
        self::WMS_SYNC_SCP_STOCK    => 'wms同步同步scp回调入库',

        //问答系统
        self::COMMAND_WENDA_1280001 => '问答系统通用信令号',
        self::COMMAND_WENDA_1280002 => '问答系统通用信令号',
        //问答作答系统
        self::COMMAND_WNEDA_1280101 => '作答超时信令',
        self::COMMAND_WNEDA_1280102 => '作答提交将checklistuuid发给派单',
        self::COMMAND_WENDA_1280103 => '作答对外通知（非流程类通知）',
        self::COMMAND_WENDA_1280104 => '问答系统向申诉系统进行事件通知',
        self::COMMAND_WENDA_1280106 => '申诉结果通知',
        self::COMMAND_WENDA_1280107 => '薪资事件通知',
        //问答激励系统
        self::COMMAND_WENDA_1280201 => '问答激励更新通知',
        self::COMMAND_WENDA_1280401 => '问答葫芦娃消息转发',
        self::COMMAND_WENDA_1280402 => '问答微信模版消息MQ使用',
        self::COMMAND_WENDA_1280403 => '收发场景消息的MQ',
        self::COMMAND_WENDA_1280301 => '问答工单机审',
        self::COMMAND_WENDA_1280302 => '问答测试题事件mq',
        self::COMMAND_WENDA_1280303 => '派单结果通知',
        self::COMMAND_WENDA_1280501 => '问答自动解答',
        self::COMMAND_WENDA_1280508 => '更新staff用户删除状态',
        self::COMMAND_WENDA_1280505 => '注销用户薪资冻结调整',
        self::COMMAND_WENDA_1280601 => '问答负反馈投产结果反馈',
        self::COMMAND_WENDA_1280602 => '同步消息至海外',

        //防刷算法-分层排灌班
        self::COMMAND_FS_SF_311001 => '分层排灌班',

        //ol
        self::COMMAND_WENDA_340012 => '老师号事件回调',

        //小鹿方向：
        self::COMMAND_XIAOLU_1250001 => '小鹿通知业务方数据变更',
        self::COMMAND_XIAOLU_1251001 => '小鹿内部教学相关消息通知',
        self::COMMAND_XIAOLU_1251002 => '发送ares任务回执消息',
        self::COMMAND_XIAOLU_1252001 => '小鹿写字作业状态c端同步到b端',
        self::COMMAND_XIAOLU_1252002 => '小鹿写字作业状态b端同步到c端',
        self::COMMAND_XIAOLU_1252003 => '小鹿写字异步生成报告消息',
        self::COMMAND_XIAOLU_1252004 => '小鹿写字任务消息同步',
        self::COMMAND_XIAOLU_1253001 => '小鹿写字deer-mis自回调服务',
        self::COMMAND_XIAOLU_1253002 => '小鹿写字deer-mis向handwrite-correct发送通知',
        self::COMMAND_XIAOLU_1253003 => '小鹿写字handwrite-correct向deer-mis发送通知',
        self::COMMAND_XIAOLU_1253004 => '小鹿写字deer-mis向handwrite发送通知',
        self::COMMAND_XIAOLU_1253005 => '小鹿写字handwrite向deer-mis发送通知',
        self::COMMAND_XIAOLU_1254001 => '小鹿写字服务号关注取关',

        //IMC
        self::COMMAND_IMC_1024000 => 'IMC接受作业帮直播课微信小程序事件消息',

        self::COMMAND_PROMOTION_1290000 => '大促预约发送优惠券',

        //-----学习管家相关 (1300000-1300999) start-----
        self::COMMAND_GUANJIA_1300000 => '学习管家策略修改通知阿克琉斯更新策略列表接口',
        self::COMMAND_GUANJIA_1300001 => '学习管家策略修改通知阿克琉斯更新策略详情接口',
        self::COMMAND_GUANJIA_1300002 => '学习管家任务修改通知阿克琉斯更新C端任务接口',
        self::COMMAND_GUANJIA_1300003 => '学习管家-学生提交练习任务通知',
        self::COMMAND_GUANJIA_1300004 => '学习管家任务修改通知阿克琉斯更新章节或者课程下学生对应的试卷id接口',

        //-----学习管家相关 (1300000-1301000) end-----

        // das 1400000 - 1400999
        self::COMMAND_DAS_1400001 => '实时加课',
        self::COMMAND_DAS_1400002 => '实时减课',
        self::COMMAND_DAS_1400003 => '延迟加课',
        self::COMMAND_DAS_1400004 => '延迟减课',
        self::COMMAND_DAS_1400005 => '加课完成',
        self::COMMAND_DAS_1400006 => '减课完成',
        self::COMMAND_DAS_1400007 => '最终一致性校验',
        self::COMMAND_DAS_1400008 => '加课-高优',

    );
}
