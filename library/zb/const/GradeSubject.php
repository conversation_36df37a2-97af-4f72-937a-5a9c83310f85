<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   GradeSubject.php
 * <AUTHOR>
 * @date   2018/1/24 11:21
 * @brief  简介
 */
class Zb_Const_GradeSubject
{
    //定义学段信息
    CONST GRADE_STAGE_PRIMARY = 1; //小学
    CONST GRADE_STAGE_JUNIOR = 20; //初中
    CONST GRADE_STAGE_SENIOR = 30; //高中
    CONST GRADE_STAGE_PRESCHOOL = 60; //学前
    CONST GRADE_STAGE_ADULT = 70; //成人
    CONST GRADE_STAGE_DAXUE = 80;//大学
    CONST GRADE_STAGE_DIYOU = 90;//低幼
    CONST GRADE_STAGE_OTHER = 255; //其它

    //学科
    const CHINESE        = 1;   //语文
    const MATHEMATICS    = 2;   //数学
    const ENGLISH        = 3;   //英语
    const PHYSICS        = 4;   //物理
    const CHEMISTRY      = 5;   //化学
    const BIOLOGY        = 6;   //生物
    const POLITICS       = 7;   //政治
    const HISTORY        = 8;   //历史
    const GEOGRAPHY      = 9;   //地理
    const INTEREST       = 10;  //兴趣课 直播课使用
    const MORALEDUCATION = 11;  //思想品德 直播课使用
    const LECTURE        = 12;  //讲座 直播课使用
    const LIZONG         = 13;  //理综 试卷用
    const WENZONG        = 14;  //文综 试卷用
    const OLYMPIAD       = 15;  //奥数
    const SCIENCE        = 16;  //科学
    const KOUYU          = 17; //成人-实用英语-口语
    const XIEZUO         = 18; //成人-实用英语-写作
    const YUEDU          = 19; //成人-实用英语-阅读
    const CIHUI          = 20; //成人-实用英语-词汇
    const YUFA           = 21; //成人-实用英语-语法
    const TINGLI         = 22; //成人-实用英语-听力
    const ZONGHE         = 23; //成人-实用英语-综合
    const CPA            = 24; //成人-财会-中级财会
    const CJCK           = 25; //成人-财会-初级财会
    const BISHI          = 26; //成人-教师资格证-笔试
    const MIANSHI        = 27; //成人-教师资格证-面试
    const SHENGKAO       = 28; //省考
    const GUOKAO         = 29; //国考
    const PUTONGHUA      = 30;//'普通话',
    const ZHAOSHIBILU    = 31;//'招录笔试',
    const CPA_N          = 32;//CPA,
    const SIWEI          = 33;//思维
    const XIEZI          = 34;//写字
    const MEISHU         = 35;//美术
    const SHIYE          = 36;//事业单位考试
    const CMA            = 37;//CMA
    const SWS            = 38;//税务师
    const ZY             = 39;//专业
    const SCRATCH        = 40;//Scratch
    const PYTHON         = 41;//PYTHON
    const JISHU          = 42;//JISHU
    const CPA_R          = 43;//CPA    32 改为MBA
    const FUYE           = 44;//成教-副业
    const XINGQU         = 45;//成教-兴趣
    const ZHICHANG       = 46;//成教-职场
    const KOUCAI         = 47;//口才
    const DAODE          = 48;//道德与法治
    const YINYUE         = 49;//音乐
    const MEIHSHU        = 50;//美术
    const XINXI          = 51;//信息技术
    const SIXIANG          = 52;//思想政治
    const SUYANG           = 53;//素养
    const JIYILI           = 54;//记忆力
    const LUOJI            = 55;//逻辑力
    const ZHUANZHU         = 56;//专注力
    const YUEDULI          = 57;//阅读力
    const WEIQI            = 58;//围棋
    const TNYD = 59;  //体能运动
    const YSSY = 60;  //艺术素养
    const SWLJ = 61;  //思维逻辑
    const KJCX = 62;  //科技创新
    const YYWX = 63;  //语言文学
    const CTWH = 64;  //传统文化
    const SHSJ = 65;  //社会实践
    const RENWEN = 66;//人文
    const YUYAN  = 67;//语言
    const JTJY   = 68;//家庭教育
    const XXLI   = 69;//学习力
    const DKEXUE = 70;//大科学
    const BIAODALI = 71;//表达力
    const TIYUJIANKANG = 72;//体育与健康 - 使用中
    const TONGYONGJISHU = 73;//通用技术 - 使用中
    const SHUFA = 74;//书法 - 使用中
    const LAODONG = 75;//劳动 - 使用中
    const JAPANESE = 76;//日语 - 使用中
    const SPANISH = 77;//西班牙语 - 使用中
    const RWSUYANG = 78;//人文素养 - 使用中
    const YUNDONG = 79;//运动 - 使用中
    const CPLUSPLUS = 80;//C++
    const PYTHONH = 82;//PythonH

    static public $arrGradeStage = array(
        self::GRADE_STAGE_PRIMARY,
        self::GRADE_STAGE_JUNIOR,
        self::GRADE_STAGE_SENIOR,
    );

    /*
     * 年级
     */
    public static $GRADE = array (
        1   => '小学',
        11  => '一年级',
        12  => '二年级',
        13  => '三年级',
        14  => '四年级',
        15  => '五年级',
        16  => '六年级',
        2   => '初一',
        3   => '初二',
        4   => '初三',
        21  => '预初',
        20  => '初中',
        5   => '高一',
        6   => '高二',
        7   => '高三',
        30  => '高中', //答疑，直播
        31 => '职高一',
        32 => '职高二',
        33 => '职高三',
        50  => '高中', //题库
        # 学前学段，60 ~ 69
        60  => '学前',
        61  => '学前班',
        62  => '大班',
        63  => '中班',
        64  => '小班',
        255 => '其他',
        # 成人学段，71 ~ 100
        70  => '成人',//学段
        71  => '成人',//学部下年级
        #大学
        80  => '大学',
        81  => '大一',
        82  => '大二',
        83  => '大三',
        84  => '大四',
        100 => '研究生',
        90  => '低幼',//学段
        91  => '低幼',//学段下年级
    );


    /*
     * 年级关联所有年级
     */
    static public $GRADEMAP = array(
        1   => array(1,11,12,13,14,15,16),
        10  => array(1,10),
        11  => array(1,11),
        12  => array(1,12),
        13  => array(1,13),
        14  => array(1,14),
        15  => array(1,15),
        16  => array(1,16),
        2   => array(20,2),
        3   => array(20,3),
        4   => array(20,4),
        21  => array(20,21),
        20  => array(20,2,3,4,21),
        5   => array(30,5),
        6   => array(30,6),
        7   => array(30,7),
        30  => array(30,5,6,7,31,32,33),
        31  => array(30, 31),
        32  => array(30, 32),
        33  => array(30, 33),
        60  => array(60, 61, 62, 63, 64),
        61  => array(60, 61),
        62  => array(60,62),
        63  => array(60,63),
        64  => array(60,64),
        70  => array(70,71),
        71  => array(70,71),
        80  => array(80,81,82,83,84,100),
        81  => array(80,81),
        82  => array(80,82),
        83  => array(80,83),
        84  => array(80,84),
        100  => array(80,100),
        90 => array(90,91),
        91 => array(90,91),
    );

    /*
     * 年级关联的学部
     */
    static public $GRADEMAPXB = array(
        1   => 1,
        11  => 1,
        12  => 1,
        13  => 1,
        14  => 1,
        15  => 1,
        16  => 1,
        2   => 20,
        3   => 20,
        4   => 20,
        21  => 20,
        20  => 20,
        5   => 30,
        6   => 30,
        7   => 30,
        30  => 30,
        31  => 30,
        32  => 30,
        33  => 30,
        60  => 60,
        61  => 60,
        62  => 60,
        63  => 60,
        64  => 60,
        70  => 70,
        71  => 70,
        81 => 80,
        82 => 80,
        83 => 80,
        84 => 80,
        100 => 80,
        90  => 90,
        91  => 90,
    );

   //科目
    static public $SUBJECT = array (
        1 => '语文',
        2 => '数学',
        3 => '英语',
        4 => '物理',
        5 => '化学',
        6 => '生物',
        7 => '政治',
        8 => '历史',
        9 => '地理',
        10=> '兴趣课',//直播课使用
        11=> '思想品德',//直播课使用
        12=> '讲座',//直播课使用
        13=> '理综',//试卷用
        14=> '文综',//试卷用
        15=> '奥数',
        16=> '科学',
        17=> '口语',//成人-实用英语
        18=> '写作',//成人-实用英语
        19=> '阅读',//成人-实用英语
        20=> '词汇',//成人-实用英语
        21=> '语法',//成人-实用英语
        22=> '听力',//成人-实用英语
        23=> '综合',//成人-实用英语
        24=> '中级财会',//成人-中级财会
        25=> '初级财会',//成人-财会
        26=> '笔试',//成人-教师资格证
        27=> '面试',//成人-教师资格证
        28=> '省考',//成人-公考
        29=> '国考',//成人-公考
        30 => '普通话',
        31 => '招录笔试',
        32 => 'MBA',
        33 => '思维',
        34 => '写字',
        35 => '美术',
        36 => '事业单位联考',
        37 => 'CMA',
        38 => '税务师',
        39 => '专业',//成教-考研
        40 => '图形化',
        41 => 'Python编程',
        42 => '技术',
        43 => 'CPA',
        44 => '副业',
        45 => '兴趣',
        46 => '职场',
        47 => '口才',
        48 => '道德与法治',
        49 => '音乐',
        50 => '美术',
        51 => '信息技术',
        52 => '思想政治',
        53 => '素养',
        54 => '记忆力',
        55 => '逻辑力',
        56 => '专注力',
        57 => '阅读力',
        58 => '围棋',
        59 => '体能运动',
        60 => '艺术素养',
        61 => '思维逻辑',
        62 => '科技创新',
        63 => '语言文学',
        64 => '传统文化',
        65 => '社会实践',
        66 => '人文',
        67 => '语言',
        68 => '家庭教育',
        69 => '学习力',
        70 => '大科学',
        71 => '表达力',
        72=> "体育与健康",
        73 => "通用技术",
        74 => "书法",
        75 => "劳动",
        76 => "日语",
        77 => "西班牙语",
        78 => "人文素养",
        79 => "运动",
        80 => "C++",
        82 => "PythonH",
    );

    //升年级
    static public $GRADEHIGHER = array(
        11  => 12,
        12  => 13,
        13  => 14,
        14  => 15,
        15  => 16,
        16  => 2,
        2   => 3,
        3   => 4,
        4   => 5,
        5   => 6,
        6   => 7,
        7   => null,
    );

    //降年级
    static public $GRADELOWER = array(
        11  => null,
        12  => 11,
        13  => 12,
        14  => 13,
        15  => 14,
        16  => 15,
        2   => 16,
        3   => 2,
        4   => 3,
        5   => 4,
        6   => 5,
        7   => 6,
    );
}
