<?php

class Ext_Ral_Ral
{
    const PROTOCOL_HTTP = 'http';
    const PROTOCOL_HTTPS = 'https';
    const PROTOCOL_NSHEAD = 'nshead';

    private $workerID = 0;

    private static $ralInst = NULL; // 本次请求相关信息

    private $queryString = ""; // 不建议使用
    private $ralReqUri = "";
    private $logID = "";

    private $moduleName = "";
    private $userIP = "";
    private $userAgent = "";

    private $ralErrno = 0; // last error number
    private $ralError = ''; // last error msg
    private $ralProtocolCode = -1; // last http response code

    public $extParams = null;
    private $retryPolicy = NULL;

    protected function __construct()
    {
        $this->extParams = [];
    }

    public function getLogID()
    {
        if ("" == $this->logID) {
            $this->logID = Bd_Log::genLogID();
        }
        return $this->logID;
    }

    public function getSpanId()
    {
        return "0.1";
    }

    public function getWorkerID()
    {
        return $this->workerID;
    }

    public function localString()
    {
        return sprintf("idc=%s product=%s subsys=%s module=%s caller_uri=%s user_ip=%s"
            , self::ralGetIdc()
            , $this->extParams["product"]
            , $this->extParams["subsys"]
            , $this->ralGetModule()
            , $this->extParams["caller_uri"]
            , $this->ralGetUserIP()
        );
    }

    public function localLogArr()
    {
        return [
            "idc" => $this->ralGetIdc(),
                "product" => $this->extParams["product"],
                "subsys" => $this->extParams["subsys"],
                "module" => $this->ralGetModule(),
                "caller_uri" => $this->extParams["caller_uri"],
                "user_ip" => $this->ralGetUserIP(),
            ];
    }

    public static function getInstance()
    {
        if (self::$ralInst == NULL) {
            self::$ralInst = new Ext_Ral_Ral();
        }

        return self::$ralInst;
    }

    public static function getRalConfig($serviceName)
    {
        // 在 conf/app/apis.conf 或者 conf/common/apis.conf
        // 结构体类似
        //[examRecommand]
        //domain: zns-server-proxy-svc:8080
        //timeout: 3

        $conf = Bd_Conf_Ral::getConf($serviceName);
        return self::checkConf($conf);
    }

    private static function checkConf($conf)
    {
        if($conf === false) {
            return false;
        }

        if (!isset($conf["domain"]) || empty($conf["domain"])) {
            return false;
        }

        if (!isset($conf["protocol"]) || empty($conf["protocol"])) {
            $conf["protocol"] = "http";
        }

        if (!isset($conf["retry"]) || empty($conf["retry"])) {
            $conf["retry"] = 0;
        }

        if (!isset($conf["converter"]) || empty($conf["converter"])) {
            $conf["converter"] = Ext_Ral_Convert::convertTypeForm;
        }

        if (!isset($conf['proxy'])) {
            $conf["proxy"] = 0;
        }

        return $conf;
    }

    public function ral($serviceName, $method, $input, $extra = null, $header)
    {
        // 兼容以前的ral，method不识别情况下默认POST
        $method = Ext_Ral_HttpCurl::formatMethod($method);
        $ralArg = [
            "idc" => self::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
            "service" => $serviceName,
            "method" => $method,
        ];

        $originHeader = $header;
        $reqStartTime = gettimeofday(true);
        $conf = self::getRalConfig($serviceName);
        if (false === $conf) {
            $ralArg["errmsg"] = "config not exit or invalid";
            ral_write_log(RAL_LOG_ERR, "RAL", $ralArg);
            $this->ralErrno = RAL_RET_ERR_CONF;
            $this->ralError = RAL_RET_ERR_CONF_MSG;
            $this->cleanRal();
            return false;
        }

        //如果$extra指定了ctimeout等，优先取指定
        $this->replaceByExtra($conf, $extra);

        $domain = $conf["domain"];
        $protocol = $conf["protocol"];
        $retry = $conf["retry"];
        $converter = $conf["converter"];

        $this->appendRalLog($ralArg, $conf);
        $this->append2Header($header, $conf);


        if ($extra && !is_array($extra)) {
            $header["randID"] = $extra;
        } else {
            $header["randID"] = rand();
        }

        $ralArg["uniqid"] = $header["randID"];


        // 优先使用header中指定的信息
        if (isset($header["pathinfo"]) && !empty($header["pathinfo"])) {
            $this->ralReqUri = $header["pathinfo"];
        }
        if (isset($header["querystring"]) && !empty($header["querystring"])) {
            $this->queryString = $header["querystring"];
        }
        $this->ralReqUri = $this->getURI($this->ralReqUri, $this->queryString);
        $ralArg["uri"] = $this->ralReqUri;

        // 非http协议通过proxy请求
        $reqProxy = false;
        if ($conf['proxy'] != 0 || $protocol == self::PROTOCOL_NSHEAD) {
            // 请求第三方的数据
            $body = $input;
            if (!isset($originHeader['log_id'])) {
                $originHeader['log_id'] = $this->getLogID();
            }
            $ralReqUri = $this->ralReqUri;
            $this->ralReqUri = "/api/ralRequest";

            if (isset($conf['proxy_pack']) && $conf['proxy_pack'] == Ext_Ral_Convert::convertTypeMcpack2) {
                $this->ralReqUri = "/api/mcRalRequest";
                if (is_array($body)) {
                    $body = mc_pack_array2pack($body, PHP_MC_PACK_V2);
                } else {
                    $body = mc_pack_text2pack($body, PHP_MC_PACK_V2);
                }
                $originHeader['body_len'] = strlen($body);
            }

            if (isset($conf['proxy_pack']) && $conf['proxy_pack'] == "mcpack2rawbase") {
                $this->ralReqUri = "/api/mcRawRalRequest";
                if (is_array($body)) {
                    $body = mc_pack_array2pack($body, PHP_MC_PACK_V2);
                } else {
                    $body = mc_pack_text2pack($body, PHP_MC_PACK_V2);
                }
                $originHeader['body_len'] = strlen($body);
            }

            // 请求proxy的数据组合
            $input = [
                'serviceName' => $serviceName,
                'method' => $method,
                'path' => $ralReqUri,
                'body' => $body,
                "logID" => $this->getLogID(),   // todo logID的用处
                'async' => '0',
                'header' => $originHeader,
            ];

            $protocol = "http";
            $converter = "form";
            $method = Ext_Ral_HttpCurl::METHOD_POST;
            $reqProxy = true;
        }

        // 根据convert，对input 参数做不同处理
        $convObj = new Ext_Ral_Convert($converter, $this->getLogID(), $protocol);

        $packStartTime = gettimeofday(true);
        $params = $convObj->Packed($input);
        $ralArg["pack"] = gettimeofday(true) * 1000 - $packStartTime * 1000;

        if ($params === false) {
            $ralArg["errmsg"] = "param convert error";
            $ralArg["input"] = json_encode($input);
            ral_write_log(RAL_LOG_ERR, "RAL", $ralArg);
            $this->ralErrno = RAL_RET_ERR_PACK;
            $this->ralError = RAL_RET_ERR_PACK_MSG;
            $this->cleanRal();
            return false;
        }

        if ($this->userAgent != "") {
            $header['useragent'] = $this->userAgent;
        }

        // basic auth
        if(isset($conf['basicAuth']) && $conf['basicAuth'] != "") {
            $header["Authorization"] = sprintf("Basic %s", base64_encode($conf['basicAuth']));
        }

        $talkStartTime = gettimeofday(true);

        $ralArg["req_start_time"] = $reqStartTime;
        $ralArg["reqStartTime"] = $reqStartTime;
        $ralArg["talk_start_time"] = $talkStartTime;

        $request = [
            "logid" => $this->getLogID(),
            "header" => $header,
            "convert" => $converter,
            "host" => $domain,
            "url" => $this->getUrl($protocol, $domain, $this->ralReqUri),
            "method" => $method,
            "input" => $params,
            "retry" => $retry,
            "moduleName" => $this->ralGetModule(),
            "ralArgs" => $ralArg,
            "reqProxy"=>$reqProxy,
        ];

        $logKey = "rpc_{$serviceName}_{$method}";
        Hk_Util_Log::start($logKey);

        // 重试策略
        if($this->retryPolicy != null) {
            $request["retryPolicy"] = $this->retryPolicy;
        }
        if(isset($conf["retryHttpCode"])) {
            $request["retryHttpCode"] = $conf["retryHttpCode"];
        }

        $resp = Ext_Ral_HttpCurl::instance()->curl($request);

        Hk_Util_Log::stop($logKey);

        $this->ralErrno = $resp['errno'];
        $this->ralError = $resp['errmsg'];
        $this->ralProtocolCode = $resp["httpCode"];

        $ralArg["prot_code"] = $resp["httpCode"];
        $ralArg["prot_info"] = '';
        $ralArg["err_no"] = $resp['errno'];
        $ralArg["ralCode"] = $resp['errno'];
        $ralArg["err_info"] = urlencode($resp['errmsg']);
        $ralArg["retry"] = $resp["retry"] . "/" . $retry;

        if ($ralArg["err_no"] != 0 || $ralArg["prot_code"] >= 400) {
            $this->ralErrno = RAL_RET_ERR_TALK;
            $this->ralError = RAL_RET_ERR_TALK_MSG;
            $this->cleanRal();
            // 已在mcurl时记录日志
            return false;
        }

        $unpackStartTime = gettimeofday(true);
        $data = $convObj->UnPacked($resp['data']);
        $ralArg["unpack"] = gettimeofday(true) * 1000 - $unpackStartTime * 1000;

        if ($data === false) {
            $ralArg["errmsg"] = "unPacked error";
            $ralArg = array_merge($ralArg, $resp['costTimeInfo']);
            ral_write_log(RAL_LOG_ERR, "RAL", $ralArg);
            $this->ralErrno = RAL_RET_ERR_UNPACK;
            $this->ralError = RAL_RET_ERR_UNPACK_MSG;
            $this->cleanRal();
            return false;
        }

        if ($reqProxy) {
            // 解析
            $proxyResponse = json_decode($resp['data'], true);
            // 用户真正请求的服务返回的数据
            if (!isset($proxyResponse['data']['data'])) {
                $ralArg["errmsg"] = "proxy call third failed";
                $ralArg = array_merge($ralArg, $resp['costTimeInfo']);
                ral_write_log(RAL_LOG_ERR, "RAL", $ralArg);
                // 清除ral属性
                $this->cleanRal();
                return false;
            }
            // 第三方真正返回的数据
            $data = $proxyResponse['data']['data'];
            // unpack
            if (isset($conf['proxy_pack']) && $conf['proxy_pack'] == "mcpack2rawbase") {
                $data = base64_decode($data);
                $data = mc_pack_pack2array($data);
            }
        }

        $res_len = 0;
        if (is_string($resp['data'])) {
            $res_len = strlen($resp['data']);
        }

        $ralArg["req_len"] = is_string($params) ? strlen($params) : -1;
        $ralArg["res_len"] = $res_len;
        $ralArg = array_merge($ralArg, $resp['costTimeInfo']);
        ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);
        $this->cleanRal();


        //兼容ral手册中：ext_data:是否获取 ext_data(response 的 header 和各种 cost 时间)
        if ($extra && isset($extra['ext_data']) && $extra['ext_data']) {
            $extra = $this->buildExtraData($ralArg["prot_code"], $ralArg["err_no"], $resp['costTimeInfo']);
            $data = $this->buildExtraResp($data, $extra, $originHeader);
        }

        return $data;
    }

    public function ral_multi($multiReq)
    {
        if ($this->moduleName != "") {
            $moduleName = $this->moduleName;
        } else {
            $moduleName = defined("MAIN_APP") ? MAIN_APP : 'unknown';
        }

        $reqStartTime = gettimeofday(true);

        $commonArg = [
            "idc" => self::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
        ];

        $requests = array();
        $multiResp = array();
        $reqHelper = array();
        foreach ($multiReq as $reqName => $request) {
            $ralArg = array_merge($commonArg, [
                    "requestName" => $reqName,
                ]
            );
            // 给每个请求初始化一个返回值
            $multiResp[$reqName] = false;

            if (count($request) < 5) {
                $ralArg["errmsg"] = "params lack!";
                ral_write_log(RAL_LOG_ERR, "RAL", $ralArg);
                continue;
            }

            $serviceName = $request[0];
            $method = Ext_Ral_HttpCurl::formatMethod($request[1]);
            $input = $request[2];
            $extra = $request[3];
            $header = $request[4];

            $ralArg["service"] = $serviceName;
            $ralArg["method"] = $method;

            $conf = self::getRalConfig($serviceName);
            if ($conf === false) {
                $ralArg["errmsg"] = "config not exit or invalid";
                ral_write_log(RAL_LOG_ERR, "RAL", $ralArg);
                continue;
            }

            //如果$extra指定了ctimeout等，优先取指定
            $this->replaceByExtra($conf, $extra);

            $this->appendRalLog($ralArg, $conf);
            $this->append2Header($header, $conf);

            $ralArg["uri"] = $header['pathinfo'];
            $header['pathinfo'] = $this->getURI($header['pathinfo'], $header['querystring']);

            $converter = $conf["converter"];
            $protocol = $conf["protocol"];
            $ralArg["conv"] = $converter;
            $ralArg["prot"] = $protocol;

            // 非http协议通过proxy请求
            $reqProxy = false;
            $proxyPack = "";
            if($conf['proxy'] != 0 || $conf["protocol"] == self::PROTOCOL_NSHEAD) {
                $body = $input; // 用户输出的数据作为请求proxy的body
                $originHeader = $header;
                if(!isset($originHeader['log_id'])) {
                    $originHeader['log_id'] = $this->getLogID();
                }
                $header['pathinfo'] = "/api/ralRequest";
                // 请求proxy的数据组合 (todo: proxy 没有处理header)
                if(isset($conf['proxy_pack']) && $conf['proxy_pack'] == Ext_Ral_Convert::convertTypeMcpack2) {
                    $header['pathinfo'] = "/api/mcRalRequest";
                    if(is_array($body)) {
                        $body = mc_pack_array2pack($body, PHP_MC_PACK_V2);
                    } else {
                        $body = mc_pack_text2pack($body, PHP_MC_PACK_V2);
                    }
                    $originHeader['body_len'] = strlen($body);
                    $proxyPack = $conf['proxy_pack'];
                }

                if(isset($conf['proxy_pack']) && $conf['proxy_pack'] == "mcpack2rawbase") {
                    $header['pathinfo'] = "/api/mcRawRalRequest";
                    if(is_array($body)) {
                        $body = mc_pack_array2pack($body, PHP_MC_PACK_V2);
                    } else {
                        $body = mc_pack_text2pack($body, PHP_MC_PACK_V2);
                    }
                    $originHeader['body_len'] = strlen($body);
                    $proxyPack = $conf['proxy_pack'];
                }

                $input = [
                    'serviceName' => $serviceName,
                    'method'      => $method,
                    'path'        => $header['pathinfo'],
                    'body'        => $body,
                    "logID"       => $this->getLogID(),   // todo logID的用处
                    'async'       => '0',
                    'header'      => $originHeader,
                ];

                $protocol = "http";
                $converter = "form";
                $method = Ext_Ral_HttpCurl::METHOD_POST;
                $reqProxy = true;
            }

            // 根据convert，对input 参数做不同处理
            $params = "";
            if (!empty($input)) {
                $convObj = new Ext_Ral_Convert($converter, $this->getLogID(), $protocol);
                $params = $convObj->Packed($input);
                if ($params === false) {
                    $err = $ralArg;
                    $err["errmsg"] = "param convert error";
                    $err["input"] = json_encode($input);
                    ral_write_log(RAL_LOG_ERR, "RAL", $err);
                    continue;
                }
            }

            if ($this->userAgent != "") {
                $header['useragent'] = $this->userAgent;
            }

            $reqHelper[$reqName] = array_merge($ralArg, [
                'converter' => $converter, // 记录下来处理response的时候用
                'protocol' => $protocol,
                'reqProxy' => $reqProxy,
            ]);

            $requests[$reqName] = [
                "logid" => $this->getLogID(),
                "header" => $header,
                "convert" => $converter,
                "host" => $conf["domain"],
                "url" => $this->getUrl($protocol, $conf["domain"], $header['pathinfo']),
                "method" => $method,
                "input" => $params,
                "retry" => $conf["retry"],
                "moduleName" => $moduleName,
                "ralArgs" => $ralArg,
                "reqProxy" => $reqProxy,
                "proxy_pack"=> $proxyPack,
            ];
        }

        $logKey = "multi_rpc";
        Hk_Util_Log::start($logKey);
        list($resps, $curlInfo) = Ext_Ral_HttpCurl::instance()->mcurl($requests);
        Hk_Util_Log::stop($logKey);

        // 本次multi请求的结果,具体在每条详细返回中
        $this->ralErrno = 0;
        $this->ralError = "success";
        $this->ralProtocolCode = 200;

        foreach ($resps as $reqName => $oneResp) {
            if ($oneResp === false) {
                // http 请求过程中出错，已在mcurl时记录日志
                continue;
            }

            $converter = $reqHelper[$reqName]['converter'];

            $ralArg = array_merge($reqHelper[$reqName], [
                "httpCode" => $oneResp['httpCode'],
                "errno" => $oneResp['errno'],
                "retry" => ($curlInfo[$reqName]["counter"] - 1) . "/" . $requests[$reqName]["retry"],
            ]);


            // 已在mcurl时记录日志
            if ($oneResp['errno'] != 0 || $oneResp['httpCode'] >= 400) {
                continue;
            }

            $ralArg = array_merge($ralArg, $curlInfo[$reqName]['costTimeInfo']);

            // 处理curl成功的返回
            $convObj = new Ext_Ral_Convert($converter, $this->getLogID(), $protocol);
            $unPackedData = $convObj->UnPacked($oneResp['data']);
            if ($unPackedData === false) {
                $err = $ralArg;
                $err['errmsg'] = "unPacked error";
                $err['respData'] = json_encode($oneResp['data']);
                $err['converter'] = $converter;
                ral_write_log(RAL_LOG_ERR, "RAL", $err);
                continue;
            }

            if ($reqHelper[$reqName]['reqProxy']) {
                // 解析
                $proxyResponse = json_decode($oneResp['data'], true);
                // 用户真正请求的服务返回的数据
                if (!isset($proxyResponse['data']['data'])) {
                    $ralArg['uri'] = "proxy";
                    $ralArg['errmsg'] = "proxy call third failed";
                    ral_write_log(RAL_LOG_ERR, "RAL", $ralArg);
                    continue;
                }
                // 第三方真正返回的数据
                $unPackedData = $proxyResponse['data']['data'];
                // unpack
                if (isset($reqHelper[$reqName]['proxy_pack']) && $reqHelper[$reqName]['proxy_pack'] == "mcpack2rawbase") {
                    $unPackedData = base64_decode($unPackedData);
                    $unPackedData = mc_pack_pack2array($unPackedData);
                }
            }

            //兼容ral手册中：ext_data:是否获取 ext_data(response 的 header 和各种 cost 时间)
            if ($extra && isset($extra['ext_data']) && $extra['ext_data']) {
                $extraRes = $this->buildExtraData($oneResp['httpCode'], $oneResp['errno'], $oneResp);
                $headers = $requests[$reqName]['header'];
                $unPackedData = $this->buildExtraResp($unPackedData, $extraRes, $headers);
            }

            // 成功的请求才赋值
            $multiResp[$reqName] = $unPackedData;

            ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);
        }


        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;

        $ralArg = [
            "prot" => "http",
            "req_start_time" => $reqStartTime,
            "reqStartTime" => $reqStartTime,
            "optime" => $reqEndTime,
            "cost" => intval($cost),
            "msg" => "ral_multi",
        ];

        ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);

        return $multiResp;
    }

    private function getURI($path, $queryString)
    {
        $uri = $path;
        if ($queryString != "") {
            $contain = strrpos($uri, '?');
            if ($contain === false) {
                $uri .= '?' . $queryString;
            } else {
                $uri .= '&' . $queryString;
            }
        }

        return $uri;
    }

    private function getUrl($protocol, $domain, $reqUri)
    {
        if (empty($domain)) {
            return "";
        }
        if (false !== strripos($domain, "unix:")) {
            $domain = "localhost";
        } else if ((substr($domain, 0, 4) === "http") || substr($domain, 0, 5) === "https") {
            // 使用domain中指定的protocol
        } else {
            // 默认使用protocol指定的，没有指定protocol则默认http
            $protocol = ($protocol == "") ? "http" : $protocol;
            $domain = sprintf("%s://%s", $protocol, $domain);
        }

        $domainContain = false;
        if ($domain{strlen($domain) - 1} == '/') {
            $domainContain = true;
        }

        $uriContain = false;
        if ($reqUri{0} == '/') {
            $uriContain = true;
        }

        if ($domainContain === false && $uriContain === false) {
            $url = sprintf("%s/%s", $domain, $reqUri);
        } else if ($domainContain !== false && $uriContain !== false) {
            $domain = rtrim($domain, '/');
            $url = sprintf("%s%s", $domain, $reqUri);
        } else {
            $url = sprintf("%s%s", $domain, $reqUri);
        }

        return $url;
    }

    private function append2Header(&$header, &$conf)
    {
        if (isset($conf["timeout"]) && !empty($conf["timeout"])) {
            $header["timeout"] = $conf["timeout"];
        }
        if (isset($conf["connectTimeOut"]) && !empty($conf["connectTimeOut"])) {
            $header["connectTimeOut"] = $conf["connectTimeOut"];
        }

        if (!isset($header["host"])) {
            if (isset($conf["externalHost"]) && !empty($conf["externalHost"])) {
                $header["host"] = $conf["externalHost"];
            }
        }
    }

    private function appendRalLog(&$ralArg, &$conf)
    {
        if (isset($conf["timeout"]) && !empty($conf["timeout"])) {
            $ralArg["timeout"] = $conf["timeout"];
        }
        if (isset($conf["connectTimeOut"]) && !empty($conf["connectTimeOut"])) {
            $ralArg["connectTimeOut"] = $conf["connectTimeOut"];
        }

        $ralArg["remote_ip"] = $conf["domain"];
        $ralArg["conv"] = $conf["converter"];
        $ralArg["prot"] = $conf["protocol"];
    }

    public static function ralGetIdc()
    {
        $idc = getenv("CURRENT_IDC");
        if ($idc == "") {
            $idc = "test";
        }
        return $idc;
    }

    public function ralSetPathInfo($uri)
    {
        $this->ralReqUri = $uri;
    }

    public function ralGetReqUri()
    {
        return $this->ralReqUri;
    }

    public function ralSetQueryString($str)
    {
        $this->queryString = $str;
    }

    public function ralSetLogID($logID)
    {
        $this->logID = $logID;
    }

    public function ralSetExtParams($key, $value)
    {
        $this->extParams[$key] = $value;
    }

    public function ralSetUserAgent($userAgent)
    {
        $this->userAgent = $userAgent;
    }

    public function ralSetModule($moduleName)
    {
        $this->moduleName = $moduleName;
    }

    public function ralGetModule()
    {
        if ("" == $this->moduleName) {
            $this->moduleName = defined("MAIN_APP") ? MAIN_APP : 'unknown';
        }
        return $this->moduleName;
    }

    public function ralSetUserIP($userIP)
    {
        $this->userIP = $userIP;
    }

    public function ralGetUserIP()
    {
        if ("" == $this->userIP) {
            $this->userIP = Bd_Ip::getUserIp();
        }
        return $this->userIP;
    }

    public function ralGetErrno()
    {
        return $this->ralErrno;
    }

    public function ralGetError()
    {
        return $this->ralError;
    }

    public function ralGetProtocolCode()
    {
        return $this->ralProtocolCode;
    }

    public static function ralCreateSpan()
    {
        return "1";
    }

    private function cleanRal()
    {
        $this->queryString = "";
        $this->ralReqUri = "";
        $this->logID = "";
        $this->retryPolicy = null;
    }

    private function buildExtraResp($outPut = "", $extra = [], $headers = [])
    {
        return [
            "output" => $outPut,
            "extra" => $extra,
            "headers" => $headers,
        ];
    }

    private function buildExtraData($protoCode, $lastErrNo, $other = [])
    {
        $extra = [
            "status" => "OK",
            "protocol_code" => $protoCode,
            "last_errno" => $lastErrNo,
        ];

        if (isset($other['cost'])) {
            $extra['cost'] = $other['cost'];
        }

        if (isset($other['connect'])) {
            $extra['connect'] = $other['connect'];
        }

        if (isset($other['nameLookupTime'])) {
            $extra['nameLookupTime'] = $other['nameLookupTime'];
        }

        if (isset($other['pretransferTime'])) {
            $extra['pretransferTime'] = $other['pretransferTime'];
        }

        if (isset($other['starttransferTime'])) {
            $extra['starttransferTime'] = $other['starttransferTime'];
        }

        if (isset($other['redirectTime'])) {
            $extra['redirectTime'] = $other['redirectTime'];
        }

        return $extra;
    }

    private function replaceByExtra(&$ralCfg, $extra)
    {
        if (!is_array($extra) || !$extra) {
            return;
        }

        if (isset($extra['retry'])) {
            $ralCfg['retry'] = $extra['retry'];
        }

        if (isset($extra['ctimeout'])) {
            //转成秒
            $ralCfg['connectTimeOut'] = sprintf('%f', $extra['ctimeout'] * 1000) / 1000000;
        }

        if (isset($extra['wtimeout']) && isset($extra['rtimeout'])) {
            //转成秒
            $ralCfg['timeout'] = sprintf('%f', ($extra['wtimeout'] + $extra['rtimeout']) * 1000) / 1000000;
        }
    }

    // retry : ClassName::func
    public function setRetryPolicy($retry) {
        if(is_callable($retry)) {
            $this->retryPolicy = $retry;
            return true;
        }

        $ralArg["errmsg"] = "set setRetryPolicy failed, method need callable";
        ral_write_log(RAL_LOG_WARN, "RAL", $ralArg);
        return false;
    }
}
