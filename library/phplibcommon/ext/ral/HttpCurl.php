<?php


class Ext_Ral_HttpCurl
{
    const METHOD_GET = 'GET';
    const METHOD_POST = 'POST';
    const METHOD_PUT = 'PUT';

    const RAL_HTTP_HEADER_LEN = 4096;

    private $defaultOptions = [
        CURLOPT_USERAGENT => 'RAL/******** (internal request)',
        CURLOPT_REFERER => '',
        CURLOPT_ENCODING => 'deflate, gzip',
        CURLOPT_TIMEOUT => 3,
        CURLOPT_CONNECTTIMEOUT => 1,
        CURLOPT_MAXREDIRS => 3,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_RETURNTRANSFER => 1,
        CURLOPT_HEADER => false,
        CURLOPT_NOSIGNAL => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4,
    ];

    private $retryPolicy = NULL;

    private $systemOptions = [];
    private $customOptions = [];
    private $logID = [];

    public static function instance($arrOpt = [])
    {
        $instances = new self();
        if (version_compare(PHP_VERSION, '5.3.0', '>=')) {
            // 由于curl优先解析IPV6地址再解析V4
            // php>= 5.3 && curl >= 7.10.8
            $instances->defaultOptions[CURLOPT_IPRESOLVE] = CURL_IPRESOLVE_V4;
        }
        if (is_array($arrOpt) && !empty($arrOpt)) {
            $instances->defaultOptions = $arrOpt + $instances->defaultOptions;
        }
        return $instances;
    }

    // 拼装url
    private function getRequestUrl($url, $method, $input)
    {
        if ($method != self::METHOD_GET || empty($input)) {
            return $url;
        }

        // 设置GET参数
        if (is_array($input) || is_object($input)) {
            $strGet = http_build_query($input);
        } else {
            $strGet = $input;
        }
        if (strpos($url, '?', 7) > 0) {
            $url .= '&' . $strGet;
        } else {
            $url .= '?' . $strGet;
        }
        return $url;
    }

    public function formatUserOption($k, $v)
    {
        $str = sprintf("%s: %s", $k, $v);
        $headerLength = strlen($str);
        if ($headerLength > self::RAL_HTTP_HEADER_LEN) {
            $ralArg = [
                "logid" => $this->logID,
                "headerLength" => $headerLength,
                "errmsg" => "HttpProtocol _add_header http header too long",
                "prot" => "http",
            ];
            ral_write_log(LOG_TYPE_WARN, "convert", $ralArg);
        }

        return $str;
    }

    public static function formatMethod($method)
    {
        $method = strtoupper($method);
        switch ($method) {
            case self::METHOD_GET:
                return self::METHOD_GET;
            case self::METHOD_PUT:
                return self::METHOD_PUT;
            default:
                return self::METHOD_POST;
        }
    }

    private function setOption($request)
    {
        $this->systemOptions = $this->defaultOptions;
        $this->customOptions = [];

        $method = isset($request['method']) ? $request['method'] : 'POST';
        $method = strtoupper($method);

        $input = $request['input'];
        if ($method == self::METHOD_POST) {
            $this->systemOptions[CURLOPT_POST] = 1;
            $this->systemOptions[CURLOPT_POSTFIELDS] = $input;
        }

        if ($method == self::METHOD_PUT) {
            $this->systemOptions[CURLOPT_CUSTOMREQUEST] = self::METHOD_PUT;
            $this->systemOptions[CURLOPT_POSTFIELDS] = $input;
        }

        $index = strripos($request['host'], "unix:");
        if ($index !== false) {
            $this->systemOptions[CURLOPT_UNIX_SOCKET_PATH] = substr($request['host'], $index + strlen('unix:'));
            $this->systemOptions[CURLOPT_IPRESOLVE] = CURL_IPRESOLVE_WHATEVER;
        }
        $this->systemOptions[CURLOPT_URL] = $request['url'];

        // 优先使用用户指定的header内容
        if (isset($request['header'])) {
            $header = $request['header'];
            unset($header["pathinfo"]);
            unset($header["querystring"]);

            if (isset($header['timeout'])) {
                $this->systemOptions[CURLOPT_TIMEOUT] = $header['timeout'];
                unset($header["timeout"]);
            }

            if (isset($header['connectTimeOut'])) {
                $this->systemOptions[CURLOPT_CONNECTTIMEOUT] = $header['connectTimeOut'];
                unset($header["connectTimeOut"]);
            }

            if (isset($header['useragent'])) {
                $this->systemOptions[CURLOPT_USERAGENT] = $header['useragent'];
                unset($header["useragent"]);
            }

            if (isset($header['Accept-Encoding'])) {
                $this->systemOptions[CURLOPT_ACCEPT_ENCODING] = $header['Accept-Encoding'];
                unset($header["Accept-Encoding"]);
            }

            if (isset($header['referer'])) {
                $this->systemOptions[CURLOPT_REFERER] = $header['referer'];
                unset($header["referer"]);
            }

            // content type
            $contentType = $this->getContentType($header, $request['convert']);
            $this->customOptions[] = $this->formatUserOption("Content-Type", $contentType);

            // 兼容原有配置
            if (isset($header['refer'])) {
                $this->customOptions[] = $this->formatUserOption("refer", $header['refer']);
                unset($header['refer']);
            }

            if (isset($header['Authorization'])) {
                $this->customOptions[] = $this->formatUserOption("Authorization", $header['Authorization']);
                unset($header['Authorization']);
            }

            $cookies = "";
            if (isset($header['cookie'])) {
                $cookies = $header['cookie'];
                unset($header['cookie']);
            } else if (isset($header['COOKIE'])) {
                $cookies = $header['COOKIE'];
                unset($header['COOKIE']);
            }

            if (!empty($cookies)) {
                $strCookie = '';
                if (is_array($cookies)) {
                    foreach ($cookies as $k => $v) {
                        $strCookie .= ($k . '=' . $v . ';');
                    }
                } else {
                    $strCookie = $cookies;
                }
                $this->systemOptions[CURLOPT_COOKIE] = $strCookie;
            }

            // 自定义header内容
            if (isset($header['host'])) {
                $this->customOptions[] = $this->formatUserOption("Host", $header['host']);
                unset($header['host']);
            }

            if (isset($header['randID'])) {
                $this->customOptions[] = $this->formatUserOption("X_BD_UNIQID", $header["randID"]);
                unset($header['randID']);
            }

            // 剩余用户指定的header
            foreach ($header as $k => $v) {
                $this->customOptions[] = $this->formatUserOption($k, $v);
            }
        }

        if (isset($request['logid'])) {
            $this->customOptions[] = $this->formatUserOption("X_BD_LOGID", $request['logid']);
        }

        $this->customOptions[] = $this->formatUserOption("Uber-Trace-Id", Bd_Log::genRequestID());
        $this->customOptions[] = $this->formatUserOption("X_BD_MODULE", $request['moduleName']);
        $this->customOptions[] = $this->formatUserOption("XREQUESTAPP", $request['moduleName']);
        $this->customOptions[] = $this->formatUserOption("Expect", '');
        $this->customOptions[] = $this->formatUserOption("X_BD_IDC", ral_get_idc());
        if (isset($_SERVER['HTTP_X_BD_CALLER_URI']) || isset($header["X_BD_CALLER_URI"])) {
            $callerUri = isset($header["X_BD_CALLER_URI"]) ? $header["X_BD_CALLER_URI"] : $_SERVER['HTTP_X_BD_CALLER_URI'];
            $this->customOptions[] = $this->formatUserOption("X_BD_CALLER_URI", $callerUri);
        }

        // 获取所有需要透传的header
        foreach($this->getallheaders() as $name => $value) {
            if(substr($name, 0, 10) == "X-Zyb-Ctx-") {
                $this->customOptions[] = $this->formatUserOption($name, $value);
            }
        }
    }

    private function getContentType(&$header, $convert) {
        $contentType = "";
        if(isset($header['Content-Type'])) {
            $contentType = $header['Content-Type'];
            unset($header['Content-Type']);
        }

        if(isset($header['content-type'])) {
            $contentType = $header['content-type'];
            unset($header['content-type']);
        }

        if(isset($header['Content-type'])) {
            $contentType = $header['Content-type'];
            unset($header['Content-type']);
        }

        if($contentType != "") {
            return $contentType;
        }


        // 如果用户没有指定 content-type ， 根据 convert 选择
        if($convert == Ext_Ral_Convert::convertTypeJson) {
            $contentType = 'application/json; charset=utf-8';
        } else if($convert == Ext_Ral_Convert::convertTypeForm) {
            $contentType = 'application/x-www-form-urlencoded';
        }

        return $contentType;
    }

    // ref: https://www.php.net/manual/zh/function.getallheaders.php
    private function getallheaders() {
        $headers = [];
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) == 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }

        return $headers;
    }

    private function getHandle()
    {
        $ch = curl_init();
        if (!is_resource($ch)) {
            return false;
        }

        foreach ($this->systemOptions as $option => $value) {
            curl_setopt($ch, CURLOPT_NOSIGNAL, 1);
            if ($option == CURLOPT_TIMEOUT) {
                // 将s转为ms添加对毫秒支持
                curl_setopt($ch, CURLOPT_TIMEOUT_MS, 1000 * $value);
                continue;
            }
            if ($option == CURLOPT_CONNECTTIMEOUT) {
                // 将s转为ms添加对毫秒支持
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, 1000 * $value);
                continue;
            }
            $bolRet = curl_setopt($ch, $option, $value);
            if ($bolRet !== true) {
                curl_close($ch);
                return false;
            }
        }

        $bolRet = curl_setopt($ch, CURLOPT_HTTPHEADER, $this->customOptions);
        if ($bolRet !== true) {
            curl_close($ch);
            return false;
        }

        return $ch;
    }

    function setLogID($logID)
    {
        $this->logID = $logID;
    }

    public function curl($request)
    {
        if (empty($request)) {
            return false;
        }

        $reqProxy = false;
        if (isset($request['reqProxy'])) {
            $reqProxy = $request['reqProxy'];
            unset($request['reqProxy']);
        }

        $request['url'] = $this->getRequestUrl($request['url'], $request['method'], $request['input']);
        $this->setOption($request);
        $curl = $this->getHandle();
        if ($curl === false) {
            return false;
        }
        $logMsg = $request['ralArgs'];

        $this->setLogID($request['logID']);

        // 失败重试次数
        $intMaxRetry = $request['retry'];
        // 当前第几次请求
        $intCounter = 0;

        $arrRet = [];
        while ($intCounter <= $intMaxRetry) {
            // 返回的内容
            $strContent = curl_exec($curl);

            // 返回的错误信息
            $intErrno = curl_errno($curl);

            // 请求返回的错误信息
            $strMsg = curl_error($curl);

            $arrInfo = curl_getinfo($curl);

            $logMsg['retry'] = $intCounter . "/" . $intMaxRetry;

            // 是否需要retry
            $isRetry = false;
            if(isset($request['retryPolicy']) && is_callable($request['retryPolicy'])) {
                // 如果代码中指定了重试策略，优先使用代码中的
                $isRetry = call_user_func_array($request['retryPolicy'], array($intErrno, $arrInfo['http_code'], $strContent));

                $logMsg['retryPolicy'] = "customer";
            } else if(isset($request['retryHttpCode'])) {
                // 如果配置中指定了重试的httpCode，那么拼接重试策略为 errno !=0 || httpCode >= 配置中指定的code
                if($intErrno != CURLE_OK || $arrInfo['http_code'] >= $request['retryHttpCode']) {
                    $isRetry = true;
                }
                $logMsg['retryPolicy'] = "retryHttpCode_".$request['retryHttpCode'];
            } else {
                // 否则走默认重试策略，仅判断 errno != 0
                if($intErrno != CURLE_OK) {
                    $isRetry = true;
                }
                $logMsg['retryPolicy'] = "default";
            }

            // 错误请求记录日志
            $this->checkResponse($arrInfo, $intErrno, $strMsg, $logMsg, ($intCounter == $intMaxRetry));

            // 返回结果
            $arrRet['errno'] = $intErrno;
            $arrRet['errmsg'] = $strMsg;
            $arrRet['data'] = $strContent;
            $arrRet['costTimeInfo'] = $this->getExpireInfo($arrInfo);

            if (is_array($arrInfo) && isset($arrInfo['http_code'])) {
                $arrRet['httpCode'] = $arrInfo['http_code'];
            } else {
                $arrRet['httpCode'] = -1;
            }

            // 如果不需要retry就直接break
            if($isRetry === false) {
                if(!$reqProxy) {
                    // 不使用代理情况: 不满足retry策略，不需要继续retry了
                    break;
                } else {
                    // 使用代理情况: 不满足重试策略 && 代理请求第三方服务也成功
                    $proxyResponse = json_decode($arrRet['data'], true);
                    if (is_array($proxyResponse) && isset($proxyResponse['data']['data'])) {
                        // 代理成功，且代理请求第三方服务也成功，不需要继续retry
                        break;
                    }
                }
            }

            $intCounter++;
        }

        curl_close($curl);

        $arrRet['retry'] = $intCounter;

        return $arrRet;
    }

    public function mcurl($arrReq)
    {
        if (!is_array($arrReq) || empty($arrReq)) {
            return false;
        }

        $arrCurl = []; // 保留引用以供后续mh查询请求结果
        $mh = curl_multi_init();

        foreach ($arrReq as $reqName => $request) {
            $arrReq[$reqName]['runnable'] = false;

            if (isset($request['reqProxy'])) {
                unset($request['reqProxy']);
            }

            $request['url'] = $this->getRequestUrl($request['url'], $request['method'], $request['input']);
            $this->setOption($request);
            $ch = $this->getHandle();
            if ($ch == false) {
                continue;
            }

            $intRet = curl_multi_add_handle($mh, $ch);
            if ($intRet !== 0) {
                continue;
            }

            $arrReq[$reqName]['runnable'] = true;
            $arrReq[$reqName]['counter'] = 0;
            $arrCurl[$reqName] = $ch;
        }

        // 收集执行结果
        $arrRet = [];
        $curlInfo = [];

        //添加retry机制
        while (count($arrReq) > 0) {
            // 开始执行
            $this->execMultiHandle($mh);

            foreach ($arrReq as $reqName => &$req) {
                //记录访问次数
                $arrReq[$reqName]['counter']++;
                $curlInfo[$reqName] = $arrReq[$reqName];

                //跳过
                if ($req['runnable'] !== true) {
                    $arrRet[$reqName] = false;
                    unset($arrReq[$reqName]);
                    curl_multi_remove_handle($mh, $ch);
                    continue;
                }

                $ch = $arrCurl[$reqName];
                $arrInfo = curl_getinfo($ch);
                $errNo = curl_errno($ch);
                $errMsg = curl_error($ch);


                $httpReqMsgArr = $this->getExpireInfo($arrInfo);
                $curlInfo[$reqName]['costTimeInfo'] = $httpReqMsgArr;
                $arrRet[$reqName] = $httpReqMsgArr;

                $logMsg = $req['ralArgs'];
                //是否最后一次重试，影响日志记录级别
                $isLastRetry = $arrReq[$reqName]['counter'] > $arrReq[$reqName]['retry'] ? true : false;
                //失败
                if (!$this->checkResponse($arrInfo, $errNo, $errMsg, $logMsg, $isLastRetry)) {
                    curl_multi_remove_handle($mh, $ch);
                    //返回
                    if ($isLastRetry) {
                        $arrRet[$reqName] = false;
                        unset($arrReq[$reqName]);
                    } else {
                        curl_multi_add_handle($mh, $ch);
                    }
                    continue;
                }

                //$httpReqMsgArr = $this->getExpireInfo($arrInfo);
                //$arrRet[$reqName] = $httpReqMsgArr;
                $arrRet[$reqName]['errno'] = $errNo;
                $arrRet[$reqName]['errmsg'] = $errMsg;
                $arrRet[$reqName]['data'] = curl_multi_getcontent($ch);
                // 解析proxy的情况
                if ($arrReq[$reqName]['reqProxy']) {
                    $proxyResponse = json_decode($arrRet[$reqName]['data'], true);
                    // 用户真正请求的服务返回的数据
                    if (!isset($proxyResponse['data']['data'])) {
                        curl_multi_remove_handle($mh, $ch);
                        //返回
                        if ($isLastRetry) {
                            $arrRet[$reqName] = false;
                            unset($arrReq[$reqName]);
                        } else {
                            curl_multi_add_handle($mh, $ch);
                        }
                        continue;
                    }
                }

                $arrRet[$reqName]['idx'] = $reqName;

                unset($arrReq[$reqName]);
                curl_multi_remove_handle($mh, $ch);
            }
        }

        curl_multi_close($mh);

        return array($arrRet, $curlInfo);
    }

    // 不间断执行curl multi handle，直到全部请求处理完成
    private function execMultiHandle($mh)
    {
        do {
            $bolActive = false;
            curl_multi_exec($mh, $bolActive);
            curl_multi_select($mh);
        } while ($bolActive);
    }

    private function checkResponse(&$arrResp, $errNo, $errMsg, $logMsg = array(), $isLastRetry = true)
    {
        $reqValid = false;
        $httpCode = intval($arrResp['http_code']);

        $msg = "";
        if ($errNo == CURLE_URL_MALFORMAT || $errNo == CURLE_COULDNT_RESOLVE_HOST) {
            // 域名或URL错误
            $msg = 'The URL is not valid';
        } else if ($errNo == CURLE_COULDNT_CONNECT) {
            // URL连接不上
            $msg = 'Service for URL is invalid now, could not connect to';
        } else if ($errNo == CURLE_OPERATION_TIMEOUTED) {
            // 超出最大耗时
            $msg = 'Request for URL timeout';
        } else if ($errNo == CURLE_TOO_MANY_REDIRECTS || $httpCode == 301 || $httpCode == 302 || $httpCode == 307) {
            // 重定向次数过多
            $msg = 'Request for URL caused too many redirections';
        } else if ($httpCode >= 400) {
            $msg = 'Received HTTP error code >= 400 while loading';
        } else if ($errNo != 0) {
            $msg = "";
        } else if ($errMsg != "") {
            // 超时之类错误，errno=0但是出现了错误情况
            $msg = $errMsg;
        } else {
            $reqValid = true;
        }

        $url_parse = isset($arrResp['url']) ? parse_url($arrResp['url']) : array();

        // 记录错误日志
        if ($reqValid === true) {
            return $reqValid;
        }

        $ralArgs = [
            'spanid' => $logMsg['spanid'],
            "service" => $logMsg['service'],
            "retry" => $logMsg['retry'],
            "method" => $logMsg['method'],
            'conv' => $logMsg['conv'],
            'prot' => $logMsg['prot'],
            'prot_code' => isset($arrResp['http_code']) ? $arrResp['http_code'] : -1,
            'prot_info' => urlencode($msg),
            'err_no' => $errNo != 0 ? $errNo : '',
            'ralCode' => $errNo,
            'err_info' => urlencode($errMsg),
            'remote_ip' => $logMsg['remote_ip'],
            'req_start_time' => isset($logMsg['req_start_time']) ? $logMsg['req_start_time'] : -1,
            'reqStartTime' => isset($logMsg['req_start_time']) ? $logMsg['req_start_time'] : -1,
            'talk_start_time' => isset($logMsg['talk_start_time']) ? $logMsg['talk_start_time'] : -1,
            'pack' => isset($logMsg['pack']) ? $logMsg['pack'] : '',
            'local_ip' => $logMsg['local_ip'],
            // 下面为ral新加的日志
            'uri' => isset($url_parse['path']) ? $url_parse['path'] : 'unknow',
            'cost' => isset($arrResp['total_time']) ? $arrResp['total_time'] * 1000 : -1,
            'connect' => isset($arrResp['connect_time']) ? $arrResp['connect_time'] : -1,
            'namelookup_time' => isset($arrResp['namelookup_time']) ? $arrResp['namelookup_time'] : -1,
            'starttransfer_time' => isset($arrResp['starttransfer_time']) ? $arrResp['starttransfer_time'] : -1,
            'pretransfer_time' => isset($arrResp['pretransfer_time']) ? $arrResp['pretransfer_time'] : -1,
            'redirect_count' => isset($arrResp['redirect_count']) ? $arrResp['redirect_count'] : -1,
            'redirect_time' => isset($arrResp['redirect_time']) ? $arrResp['redirect_time'] : -1,
            'retry_policy' => $logMsg['retryPolicy'],
        ];

        $logType = RAL_LOG_ERR;
        if ($isLastRetry == false) {
            $logType = RAL_LOG_TALK_FAIL;
        }
        ral_write_log($logType, "RAL", $ralArgs);

        return $reqValid;
    }

    private function getExpireInfo($arrInfo)
    {
        $ret = [
            // 单个请求耗时(in seconds)
            'cost' => $arrInfo['total_time'] * 1000,
            'connect' => $arrInfo['connect_time'] * 1000,
            // 状态码
            'httpCode' => $arrInfo['http_code'],
            // DNS查询时间(in seconds)
            'nameLookupTime' => $arrInfo['namelookup_time'] * 1000,
            // 从建立连接到准备传输所运用的时间(in seconds)
            'pretransferTime' => $arrInfo['pretransfer_time'] * 1000,
            // 从建立连接到传输开始所运用的时间(in seconds)
            'starttransferTime' => $arrInfo['starttransfer_time'] * 1000,
            // 在事务传输开始前重定向所运用的时间(in seconds)
            'redirectTime' => $arrInfo['redirect_time'] * 1000,
        ];

        return $ret;
    }
 }

