<?php

/*

/**
 * 作业帮通用端（ios|android）反作弊校验<br>
 * 提供以下方法：<br>
 * 1、app端反作弊校验<br>
 * 2、sdk反作弊校验
 *
 * @since 1.3 2019-08-13 android签名适配变化可能导致失败适配
 * @since 1.2 2018-12-05 迁移sdk反作弊
 * @since 1.0 2018-11-23 将antispam功能统一管理
 *
 * @filesource hk/service/Antispam.php
 * <AUTHOR>
 * @version 1.3
 * @date    2019-08-13
 */

class Hk_Service_Antispam {

    private $os;                # 签名os
    private $cuid;              # 签名cuid
    private $appId;             # 签名appId
    private $signParam;         # 参与签名参数列表

    private $conf;
    private $spamDs;

    public function __construct($appId) {
        $this->appId = $appId;
        $this->conf = Bd_Conf::getConf('/hk/antispam/common');
        $this->spamDs = Hk_Ds_User_Antispam::getInstance();
        $this->init();
    }

    private function init() {
        $cgi = Saf_SmartMain::getCgi();
        if (false === $cgi) {
            Bd_Log::addNotice('common_antispam', 'FAIL due to cgi is empty');
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
        }

        # 安卓客户端cuid发生变化，需对cuid做替换，防止签名失败
        # TODO 预估年底可去掉
        $params = $cgi['request_param'];
        if (isset($params["__scuid"])) {        # saf传递签名的cuid，替换签名参数中的cuid
            $scuid = $params["__scuid"];
            $params["cuid"] = $scuid;
            unset($params["__scuid"]);
        } else {
            $scuid = strval($params["cuid"]);
        }
        $this->cuid = $scuid;
        $this->os = isset($params["os"]) ? $params["os"] : "android";
        $this->signParam = $params;
    }

    /**
     * 客户端antispam校验：<br>
     * 1、开关<br>
     * 2、uri跳过<br>
     * 3、内网<br>
     * 4、签名<br>
     * 5、时间重放<br>
     * 6、拒绝uri
     *
     * @param string $uri
     */
    public function appCheck($uri) {
        Bd_Log::addNotice('antispamType', "app");
        $type = $this->os;
        if ($this->closeSpam($type)) {
            return;
        }
        if ($this->skipUri($uri)) {
            return;
        }
        if ($this->skipInner()) {
            return;
        }
        # 签名校验
        $randomKey = $this->getRandomKey();
        if (false === $randomKey) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
        } elseif (true === $randomKey) {
            return;
        }
        if ("ios" === $this->os) {              # iOS特殊处理
            $str = 'K$L@aPb$O^Ic%U*Y`T=f+R~d954e1215aef11a512c1585a0fcd5648ff189f1e?Q0"9{8<7@6#5(4%3&2+1';
            $key = md5($str . $this->cuid . $randomKey);
            $randomKey = Hk_Util_Rc4::rc4($key, $randomKey);
            $randomKey = base64_encode($randomKey);
        }

        $this->signVerify($randomKey);          # 签名校验
        if (false === $this->timeControl()) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
        }
        if (false === $this->rejectUri()) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
        }
        Bd_Log::addNotice('common_antispam', 'PASS');
    }

    /**
     * sdk反作弊校验，校验：<br>
     * 1、开关<br>
     * 2、内网<br>
     * 3、签名<br>
     * 4、时间重放
     */
    public function sdkCheck() {
        Bd_Log::addNotice('antispamType', "sdk");
        $type = "sdk";
        if ($this->closeSpam($type)) {
            return;
        }
        if ($this->skipInner()) {
            return;
        }
        $randomKey = $this->getRandomKey();
        if (false === $randomKey) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
        }elseif (true === $randomKey) {
            return;
        }
        $this->signVerify($randomKey);
        if (false === $this->timeControl()) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
        }
    }

    /**
     * 根据randomKey计算校验签名
     *
     * @param string $randomKey
     */
    private function signVerify($randomKey) {
        if (!isset($this->signParam['sign']) || empty($this->signParam['sign'])) {
            Bd_Log::addNotice('common_antispam', 'FAIL due to sign is empty');
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
        }

        # 计算并校验签名
        $signPack = $this->signParam;
        unset($signPack['sign']);
        ksort($signPack);
        $signStr = '';
        foreach ($signPack as $k => $v) {
            $signStr .= $k . '=' . $v;
        }
        if ('android' === $this->os) {         # android签名
            $prefix = Bd_Conf::getConf('/hk/antispam/keystore/key3');
            $signStr = $prefix . '[' . md5($randomKey) . ']@' . base64_encode($signStr);
        } else {                               # ios签名
            $signStr = '[' . $randomKey . ']@' . base64_encode($signStr);
        }
        $signHash = md5($signStr);
        if ($signHash != $this->signParam['sign']) {
            Bd_Log::addNotice('common_antispam', 'FAIL due to hash error');
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
        }
    }

    /**
     * 获取设备反作弊token信息
     *
     * @return mixed:string|boolean
     */
    private function getRandomKey() {
        if (empty($this->cuid)) {
            Bd_Log::addNotice('common_antispam', 'FAIL due to cuid empty');
            return false;
        }

        $ret = $this->spamDs->getSignToken($this->appId, $this->cuid);

        // 如果ral请求挂掉了，那么就降级 变为通过
        if (false === $ret) {
            Bd_Log::addNotice('common_antispam', 'Downgrade_PASS');
            return true;
        } elseif (empty($ret)) {
            Bd_Log::addNotice('common_antispam', 'FAIL due to randomKey expires');
            return false;
        } elseif ($ret["expireTime"] - time() < 10) {       # 过期时间接近临界时间，直接判断过期
            Bd_Log::addNotice('common_antispam', 'FAIL due to randomKey nearly expire');
            return false;
        }
        $randomKey = $ret["randomKey"];
        return $randomKey;
    }


    /**
     * 判断是否开启反作弊策略，true开启不进行反作弊校验
     *
     * @param string $type
     * @return boolean
     */
    private function closeSpam($type) {
        if ("ios" === $type || "android" === $type) {
            $switch = intval($this->conf['switch'][$type]);
        } elseif ("sdk" === $type) {
            $switch = intval($this->conf["switch"]["other"]);
        } else {
            $switch = 1;
        }
        if (0 === $switch) {
            Bd_Log::addNotice('common_antispam', "NOT need due to {$type}");
            return true;
        }
        return false;
    }

    /**
     * uri是否可以跳过反作弊校验，true表示此接口不进行反作弊校验
     *
     * @param string $uri
     * @return boolean
     */
    private function skipUri($uri) {
        $uri = strtolower($uri);
        foreach ($this->conf['except'] as $exuri) {
            if ($uri == $exuri) {
                Bd_Log::addNotice('common_antispam', 'NOT need due to uri except');
                return true;
            }
        }
        return false;
    }

    /**
     * 内网测试环境使用，true代表不进行反作弊校验
     */
    private function skipInner() {
        if (Hk_Util_Ip::isInnerIp() &&
            (trim($_COOKIE['APP_DEBUG']) == strval(date('Y') . (date('n') - 1)) || trim($_GET['skip']) == 'rdqa')) {
            Bd_Log::addNotice('common_antispam', 'NOT need due to internal debug');
            return true;
        }
        return false;
    }

    /**
     * 时间校验，客户端请求发送时间与服务器接收不应超过固定阀值，防止重放抓取数据<br>
     * 当前所有app均需参与重放校验<br>
     * 先进行摸底调查，即校验时间但是不报错
     *
     * @return boolean
     */
    private function timeControl() {
        $switch = intval($this->conf['timeControl']['switch']);
        if (0 === $switch) {
            return true;
        }
        $maxTime = intval($this->conf['timeControl']['maxTime']);
        $clientTime = intval($this->signParam['_t_']);
        $serverTime = time();
        if (abs($serverTime - $clientTime) > $maxTime) {        # 时间错误
            Bd_Log::addNotice('common_antispam', 'FAIL due to cs time error');
            return false;
        }
        return true;
    }

    /**
     * 拒绝强升后的低版本接口请求<br>
     * 只有主app开启
     * TODO 保留逻辑，在现在看来有问题
     *
     * @return boolean
     */
    private function rejectUri() {
        $switch = intval($this->conf['rejectVersion']['switch']);
        if (Hk_Const_AppId::APP_HOMEWORK !== $this->appId || 0 === $switch) {
            return true;
        }

        $cliVersion = Hk_Util_Client::getVersion();
        $appVersion = $cliVersion['version'];
        $minVersion = 0;
        if ("ios" === $this->os) {
            $minVersion = intval($this->conf['rejectVersion']['minIosVersion']);
        } elseif ("android" === $this->os) {
            $minVersion = intval($this->conf['rejectVersion']['minAndroidVersion']);
        }
        if ($appVersion >= $minVersion) {       # 当前app版本符合预期
            return true;
        }

        $rejectUris = $this->conf['rejectVersion']['rejectUri'];
        $curUri = preg_replace('/\?.*/', '', $_SERVER['REQUEST_URI']);
        if (in_array($curUri, $rejectUris)) {
            Bd_Log::addNotice('common_antispam', 'FAIL due to reject uri');
            return false;
        }
        return true;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
