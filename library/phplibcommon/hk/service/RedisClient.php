<?php


/**
 * redis-client客户端<br>
 * 使用redis.so，只支持redis标准协议<br>
 * 关于如何使用以及官方文档，请参阅以下链接
 *
 * @see http://doc.zuoyebang.cc/wiki/doku.php?id=zyb:dev:odp:redis
 *
 * @since 1.9 2019-10-08 支持自定义设置前缀、连接超时和重试次数
 * @since 1.8 2019-03-15 支持重连操作，更新文档
 * @since 1.7 2019-02-20 bns请求耗时不再加入总耗时
 * @since 1.6 2018-10-16 p5不使用连接复用
 * @since 1.5 2018-10-13 迁移日志到ral
 * @since 1.0 2018-05-24 初始化
 *
 * @filesource hk/service/RedisClient.php
 * <AUTHOR>
 * @version 1.9
 * @date    2019-10-08
 */
class Hk_Service_RedisClient {


    private static $redisInst = array();

    private function __construct() {}

    /**
     * 获取redis集群对应的实例<br>
     * $opts支持设置前缀、连接超时和重试次数，这里请注意：<br>
     * 由于是单例模式，只支持首次设置，后续设置完全无效，设置格式:<br>
     * <code>
     * $opts = array(
     *     "prefix"     => string,      # 前缀，只允许字符串
     *     "timeout"    => int,         # 连接超时时间，> 0
     *     "conn_retry" => int,         # 连接重试次数，> 0
     * );
     * </code>
     *
     * @param string       $name
     * @param array        $opts
     * @return mixed:Hk_Service_RedisClient|boolean
     */
    public static function getInstance($name, array $opts = array()) {
        if (isset(self::$redisInst[$name])) {
            return self::$redisInst[$name];
        }

        $cluster = Bd_Conf::getConf("/hk/redisclient/services/$name");
        if (empty($cluster)) {
            Bd_Log::warning("get redis cluster failed, cluster: {$name}");

            $logArg = array(
                'prot'      => "redis",
                'service'   => $name,
                'err_info'  => "get redis cluster failed, cluster: {$name}",
            );
            ral_set_log(RAL_LOG_MODULE, defined("MAIN_APP") ? MAIN_APP : '');
            ral_set_log(RAL_LOG_USERIP, Bd_Ip::getUserIp());
            ral_write_log(RAL_LOG_WARN, "RedisClient", $logArg);

            return false;
        }
        $instance = $cluster['instance'];
        $timeout  = $cluster['timeout'];
        $retry    = $cluster['conn_retry'];
        $prefix   = isset($cluster['prefix']) ? $cluster['prefix'] : "";

        $readTimeout = 0;
        if (isset($cluster['read_timeout'])) {
            // 配置中的read_timeout与timeout保持一致，单位毫秒。需转换成时间单位秒
            $readTimeout  = floatval($cluster['read_timeout'] / 1000);
        }

        $keepAlive = REDIS_KEEP_ALIVE;

        //以业务配置中的优先
        if (isset($cluster["keep_alive"]) && $cluster["keep_alive"] === "false") {
            $keepAlive = false;
        } elseif (isset($cluster["keep_alive"]) && $cluster["keep_alive"] === "true") {
            $keepAlive = true;
        }
        if(isset($cluster["password"])) {
            $password = $cluster['password'];
        }

        # 自定义设置
        if (isset($opts["timeout"]) && intval($opts["timeout"]) > 0) {
            $timeout = intval($opts['timeout']);
        }
        if (isset($opts["conn_retry"]) && intval($opts["conn_retry"]) > 0) {
            $retry   = intval($opts["conn_retry"]);
        }
        if (isset($opts["prefix"]) && is_string($opts["prefix"])) {
            $prefix  = strval($opts["prefix"]);
        }

        if (isset($opts["read_timeout"]) && is_numeric($opts["read_timeout"])) {
            // 由于历史原因，这里的单位是秒
            $readTimeout  = floatval($opts["read_timeout"]);
        }

        $conn     = new RedisClient($instance, $prefix, $timeout, $retry, $readTimeout, $keepAlive, $password);
        self::$redisInst[$name] = $conn;
        return $conn;
    }
}

/**
 * redis客户端封装，封装redis.so，实例通过bns获取
 *
 * @since 1.5 2018-10-13 php7使用pconnect连接复用模式，日志使用ral输出
 * @since 1.0 2018-05-24 初始化
 *
 * @filesource hk/service/RedisClient.php
 * <AUTHOR>
 * @version 1.5
 * @date    2018-10-13
 */
class RedisClient {

    const ERR_OK   = 0;
    const ERR_INST = -1;         # 获取实例失败
    const ERR_CONN = -2;         # 连接错误
    const ERR_EXEC = -3;         # 命令执行失败

    private $host = "";
    private $port = 0;
    private $connRetry = 0;     # 连接重试次数
    private $timeout;
    private $readTimeout = 0;
    private $keepAlive;
    private $service;
    private $inst;
    private $prefix;
    private $password;

    private $redis = NULL;
    private $initConnect  = false;
    private static $timer = array();

    private $errno = 0;
    private $errmsg;

    public function __construct($inst, $prefix, $timeout, $connRetry, $readTimeout = 0, $keepAlive = true, $password = '') {
        $this->service = "redis";
        $this->inst    = $inst;
        $this->redis   = new Redis();

        self::start("call_bns");
        $server = Bd_Bns_Client::getInstance()->getServiceInstance($this->service, $inst, Bd_Bns_Const::LB_RANDOM,"redis");
        self::stop("call_bns");
        if (false === $server) {
            $this->log("instance get failed", self::ERR_INST, array(), RAL_LOG_WARN);
            return;
        }
        $this->host = $server["host"];
        $this->port = $server["port"];

        $this->prefix    = $prefix;
        $this->timeout   = $timeout;
        $this->connRetry = $connRetry;

        $this->readTimeout = $readTimeout;
        $this->keepAlive = $keepAlive;
        $this->password = $password;
    }

    /**
     * 设置额外选项
     */
    private function setOptions() {
        if (!empty($this->prefix)) {
            $this->redis->setOption(Redis::OPT_PREFIX, "{$this->prefix}:");
        }
        if ($this->readTimeout != 0) {
            // float, value in seconds (optional, default is 0 meaning unlimited)
            $this->redis->setOption(Redis::OPT_READ_TIMEOUT, $this->readTimeout);
        }

        if($this->password != '') {
            $this->redis->auth($this->password);
        }
    }

    /**
     * 连接redis
     *
     * @return boolean
     */
    private function connect($retry) {
        if ($this->host === "") {
            return false;
        }
        $interval = 100;                                # 默认超时重试间隔时间，单位：ms
        $timeout  = floatval($this->timeout / 1000);    # 超时，需转换时间单位，s
        $args     = array(
            "tried"  => 0,
            "method" => "connect",
            'req_start_time' => gettimeofday(true),
            'reqStartTime' => gettimeofday(true),
        );
        $ret      = false;
        $needRt   = false;                      # 是否需重试
        $timer    = "connect";
        for ($tried = 0; $tried <= $retry; $tried++) {    # 连接重试
            $args["tried"] = $tried;
            self::start($timer);
            try {
                if (PHP_MAJOR_VERSION >= 7 && $this->keepAlive) {   # p7: 连接复用
                    $ret = $this->redis->pconnect($this->host, $this->port, $timeout, NULL, $interval);
                } else {                        # p5: 短连接
                    $ret = $this->redis->connect($this->host, $this->port, $timeout, NULL, $interval);
                }
                $needRt  = true;
                $errmsg  = "connect timeout";
            } catch (RedisException $e) {       # 连接有错，不重试
                $errmsg  = $e->getMessage();
                $ret     = false;
                $needRt  = false;
            }
            self::stop($timer);
            if (false === $ret) {
                $this->log($errmsg, self::ERR_CONN, $args, RAL_LOG_WARN);
                if ($needRt) {                  # 需要重试，继续重试
                    continue;
                }
            }
            break;
        }
        if (false === $ret) {
            return false;
        }
        $this->setOptions();                    # 设置选项
        $this->log("OK", self::ERR_OK, $args);
        return true;
    }

    /**
     * redis实例当前连接状态
     *
     * @return boolean
     */
    public function isConnected() {
        return $this->redis->isConnected();
    }

    /**
     * redis强制重连<br>
     * 在连接复用并且连接正常情况下，强制重连不会真正的重连redis
     *
     * @return boolean
     */
    public function reconnect() {
        return $this->connect($this->connRetry);
    }

    /**
     * 执行redis命令，获取命令执行结果，交互日志在ral日志中<br>
     * 请注意，由于使用了集群解决方案，有些命令是被禁止的，使用会自动报错<br>
     * 命令如果执行失败或抛出异常，直接返回NULL
     *
     * @param string       $method
     * @param array        $input
     * @return mixed:ret|NULL
     */
    public function __call($method, $input) {
        if (false === $this->initConnect) {         # 初始化redis连接，解决空连接问题
            $this->connect($this->connRetry);
            $this->initConnect = true;
        }

        $rKey = isset($input[0]) && is_string($input[0]) ? strval($input[0]) : "";
        $rKey = "" !== $this->prefix ? "{$this->prefix}:$rKey" : $rKey;
        $args = array(
            'method' => $method,
            'key'    => strlen($rKey) > 50 ? substr($rKey, 0, 50) : $rKey,
            'req_start_time' => gettimeofday(true),
            'reqStartTime' => gettimeofday(true),
        );


        $this->clearError();

        // 校验
        $checkError = "";
        if (NULL === $this->redis) {
            $checkError = "no redis instance";
        } elseif (false === $this->redis->isConnected()) {
            $checkError = "no redis connect";
        } elseif (!method_exists($this->redis, $method)) {     # 方法不存在
            $checkError = "method not exist";
        } elseif ($this->isDisabled($method)) {                # 方法被禁用
            $checkError = "method disabled";
        }

        if($checkError != "") {
            $this->setError($checkError, self::ERR_EXEC);
            $this->log($checkError, self::ERR_EXEC, $args, RAL_LOG_WARN);
            return NULL;
        }

        // 执行
        $timer   = "read";
        self::start($timer);
        try {
            $this->redis->clearLastError();
            $ret = call_user_func_array(array($this->redis, $method), $input);
        } catch (RedisException $e) {
            // 大部分 $e->getCode==0 , 所以统一用ERR_EXEC, 避免歧义
            $this->setError($e->getMessage(), self::ERR_EXEC);
            $ret = NULL;
            // HACK!! close connection whatever the exception;
            $this->redis->close();
            $this->initConnect = false;
        }
        self::stop($timer);

        $args['res_data'] = $ret;

        // lastError 只打印错误日志，由上层业务自己处理
        $this->checkLastError();

        $level = $this->getErrorMsg() == "OK" ? RAL_LOG_SUM_SUCC : RAL_LOG_WARN;
        $this->log($this->getErrorMsg(), $this->getErrorNo(), $args, $level);

        return $ret;
    }

    private function clearError() {
        $this->errmsg = "";
        $this->errno = 0;
    }

    private function setError($errmsg, $errno) {
        $this->errmsg = $errmsg;
        $this->errno = $errno;
    }

    private function checkLastError() {
        if($this->errmsg == "") { // 没有抛出异常
            if($this->redis->getLastError() != NULL) {
                $this->errmsg = $this->redis->getLastError();
                $this->errno = self::ERR_EXEC;
            } else {
                $this->errmsg = "OK";
                $this->errno = self::ERR_OK;
            }
        }
    }

    public function getErrorMsg() {
        return $this->errmsg;
    }

    public function getErrorNo() {
        return $this->errno;
    }

    /**
     * 判断方法是否被禁用，以下方法被proxy禁用，不要使用
     *
     * @param string      $method
     * @return boolean
     */
    private function isDisabled($method) {
        $disabled = array(
            "keys"       => 1,
            "migrate"    => 1,
            "move"       => 1,
            "object"     => 1,
            "randomkey"  => 1,
            "rename"     => 1,
            "renamenx"   => 1,
            "scan"       => 1,
            "bitop"      => 1,
            "msetnx"     => 1,
            "blpop"      => 1,
            "brpop"      => 1,
            "brpoplpush" => 1,
            "discard"    => 1,
            "exec"       => 1,
            "multi"      => 1,
            "watch"      => 1,
            "unwatch"    => 1,
            "psubscribe" => 1,
            "publish"    => 1,
            "subscribe"  => 1,
        );
        $method = strtolower($method);
        return isset($disabled[$method]) ? true : false;
    }

    /**
     * 计时器
     *
     * @param string      $name
     * @return boolean
     */
    private static function start($name) {
        if (!isset(self::$timer[$name])) {
            self::$timer[$name] = new Bd_Timer(false, Bd_Timer::PRECISION_US);
        }
        self::$timer[$name]->reset();
        return self::$timer[$name]->start();
    }

    /**
     * 关闭并重置计时器，返回计时器时间，单位：ms
     *
     * @param string      $name
     * @return float
     */
    private static function stop($name) {
        if (!isset(self::$timer[$name])) {
            return 0;
        }
        return self::$timer[$name]->stop();
    }

    /**
     * 记录redis实例客户端访问日志
     *
     * @since 2019-01-16 增加链路追踪日志
     *
     * @param string      $msg
     * @param int         $errno
     * @param array       $arg
     * @param const       $level
     */
    private function log($msg = "", $errno = 0, array $arg = array(), $level = RAL_LOG_SUM_SUCC) {
        if (function_exists('ral_create_span')) {
            $span = ral_create_span();
        } else {
            $span = array(
                'spanid' => 0,
                'content_tracing' => false,     # 是否进行内容采样
            );
        }
        $uri    = isset($_SERVER['REQUEST_URI']) ? @parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH) : '';
        $logArg = array(              # 通用日志参数
            'uri'       => false === $uri ? "" : $uri,
            'spanid'    => $span['spanid'],
            'conv'      => 'redis',
            'prot'      => 'redis',
            'service'   => $this->inst,
            'cost'      => 0, // 请求耗时(connect+read/write)，ms，小数点后精确到3位，末位0省略
            'timeout'   => $this->timeout,
            'remote_ip' => empty($this->host) ? "" : "{$this->host}:{$this->port}",
            'err_no'    => $errno,
            'ralCode'   => $errno,
            'err_info'  => $msg,
        );
        # 内容采样
        if ($span["content_tracing"]) {
            $logArg["res_data"] = isset($arg["res_data"]) ? serialize($arg["res_data"]) : "";
        }
        unset($arg["res_data"]);
        # 连接超时重试
        if ("connect" == $arg["method"]) {
            $tried    = isset($arg["tried"]) ? $arg["tried"] : 0;
            unset($arg["tried"]);
            $logArg["retry"] = "{$tried}/{$this->connRetry}";

        }
        # 耗时统计，bns耗时不再计入总耗时
        foreach (self::$timer as $name => $timer) {
            $logArg[$name]   = floatval(number_format($timer->getTotalTime() / 1000, 3, ".", ""));
            self::$timer[$name]->reset();
            if ($name != "call_bns") {
                $logArg["cost"] += $logArg[$name];     # 交互总耗时: connect + read/write
            }
        }
        $caller = 'RedisClient';
        $logArg = array_merge($logArg, $arg);

        ral_set_log(RAL_LOG_MODULE, defined("MAIN_APP") ? MAIN_APP : '');
        ral_set_log(RAL_LOG_USERIP, Bd_Ip::getUserIp());
        ral_write_log($level, $caller, $logArg);
    }

    /**
     * 释放redis连接<br>
     * 如果使用连接复用，fpm连接不会释放直到fpm生命周期结束，fpm被自动重启
     */
    public function __destruct() {
        if (NULL !== $this->redis && $this->redis->isConnected() && $this->keepAlive == false) {
            try {
                $this->redis->close();
            } catch (RedisException $e) {}
        }
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
