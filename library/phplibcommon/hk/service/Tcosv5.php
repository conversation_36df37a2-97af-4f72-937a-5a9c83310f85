<?php


/**
 * @file    Tcos.php
 * @brief   腾讯云对象存储服务
 * <AUTHOR>
 * @version 1.0
 * @date    2017-11-11
 */

require_once LIB_PATH."/ext/tcossdkv5/include.php";

use QCloud\Cos\Client;
use QCloud\Cos\MultipartUpload;

class Hk_Service_Tcosv5 {

    const TCOS_DEFAULT_REGION = 'ap-beijing';//默认region

    const PUT_DIRECTIVE_COPY = 'Copy';//忽略设置的用户元数据信息直接复制
    const PUT_DIRECTIVE_REPLACED = 'Replaced';//按设置的元信息修改元数据，当目标路径和源路径一样时，必须设置为 Replaced

    const DEFAULT_TIME_OUT = 60;

    private static $CosConfig = null;

    private static $CosType = "tcos";


    public static function genObjectKey4FileContentUpload($config, $filePath, $fileName, $fileType, $needsize = 0) {
        $dstFilePath = '';
        if(!empty($fileName)) {
            $dstFilePath = "/$fileName.$fileType";
        } else {
            $fileName = $config['file_prefix'].md5_file($filePath);
            if($needsize) {
                $imgInfo = getimagesize($filePath);
                $fileName = $fileName."_".$imgInfo[0]."_".$imgInfo[1];
            }
            $dstFilePath = "/$fileName.$fileType";
        }
        if(!empty($config['directory'])) {
            $dstFilePath = '/'.$config['directory'].$dstFilePath;
        }

        return $dstFilePath;
    }


    /**
     * @brief 上传本地文件到Cos(当前v5版本只支持上传不超5GB的文件)
     * @param string      $cosName cos服务名
     * @param string      $filePath 本地文件路径
     * @param string      $fileType 上传到cos的文件后缀，默认为jpg
     * @param string|null $fileName 上传到cos的文件名，默认为空，自动生成文件名
     * @param int         $overwrite 同名文件是否强制覆盖，0-不强制覆盖，1-强制覆盖(v5版本中：如果存储桶没有启用版本控制，则新上传的对象将覆盖原来的对象)
     * @param int         $needsize 是否需要在pid中写宽高，0-不需要，1-需要
     * @return boolean|string  正常返回可以访问的url
     */
    public static function uploadLocalFile($cosName, $filePath, $fileType = "jpg", $fileName = null, $overwrite = 1, $needsize = 0) {
        $reqStartTime = gettimeofday(true);

        $ralArg = [
            "idc"      => Ext_Ral_Ral::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
            "prot"     => self::$CosType,
            "service"  => $cosName,
            "method"   => __METHOD__,
        ];

        $config = self::getCosConfig($cosName);

        if(false === $config) {
            $ralArg["errmsg"] = "get cos config error";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        if(!file_exists($filePath)) {
            $ralArg["errmsg"] = "local file $filePath not exist";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $fileSize = filesize($filePath);
        if($fileSize > intval($config['filesize_limit'])) {
            $ralArg["errmsg"] = "local file $filePath exceeds size limit ".$config['filesize_limit'];
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $dstFilePath = self::genObjectKey4FileContentUpload($config, $filePath, $fileName, $fileType, $needsize);

        //上传
        try {
            $cosClient = new Client(array(
                'region'      => $config['ap_region'],
                //'schema' => 'https', //协议头部，默认为http
                'credentials' => array(
                    'appId'     => $config['app_id'],
                    'secretId'  => $config['secret_id'],
                    'secretKey' => $config['secret_key']
                ),
                'timeout'     => $config['timeout'] ? intval($config['timeout']) : self::DEFAULT_TIME_OUT,
            ));


            //普通上块，支持最大5G
            //上传文件流
            if($fileSize < MultipartUpload::DEFAULT_PART_SIZE) {
                $ret = $cosClient->putObject(array(
                    'Bucket' => $config['bucket'],
                    'Key'    => $dstFilePath,
                    'Body'   => fopen($filePath, "rb")
                ));
            } else {//分块上传，最大支持50T
                $ret = $cosClient->Upload($config['bucket'], $dstFilePath, fopen($filePath, "rb"));
            }
        } catch(Exception $e) {
            $ralArg["errmsg"] = "tcos upload file exception: ".$e->getMessage();
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }


        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;
        $ralArg["cost"] = intval($cost);


        $result = $ret->toArray();
        //记录上传时的详情信息
        $ralArg['cos_info'] = json_encode($result);

        if(is_array($result) && isset($result['ETag'])) {
            $ralArg["err_no"] = 0;
            ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);
        } else {
            $ralArg["errmsg"] = "upload file to $dstFilePath failed";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        //成功返回url
        return "https://".$result['Location'];
    }

    /**
     * @brief 上传文件内容到cos
     * @param $cosName    cos服务名
     * @param $content    文件内容
     * @param $fileType   文件类型
     * @param $fileName   指定文件名
     * @param $overwrite  是否强制覆盖(v5版本中：如果存储桶没有启用版本控制，则新上传的对象将覆盖原来的对象)
     * @param $needsize   是否需要在pid中写宽高，0-不需要，1-需要(v5版本中：如果存储桶没有启用版本控制，则新上传的对象将覆盖原来的对象)
     * @return boolean|string  正常返回可以访问的url
     */
    public static function uploadFileContent($cosName, $content, $fileType = "jpg", $fileName = null, $overwrite = 0, $needsize = 0) {
        $reqStartTime = gettimeofday(true);

        $ralArg = [
            "idc"      => Ext_Ral_Ral::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
            "prot"     => self::$CosType,
            "service"  => $cosName,
            "method"   => __METHOD__,
        ];

        if(empty($content)) {
            $ralArg["errmsg"] = "file content is empty!";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }
        $config = self::getCosConfig($cosName);
        if(false === $config) {
            $ralArg["errmsg"] = "get cos config error";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $fileSize = strlen($content);
        if($fileSize > intval($config['filesize_limit'])) {
            $ralArg["errmsg"] = "file content length exceeds size limit ".$config['filesize_limit'];
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        if(empty($fileName)) {
            $fileName = $config['file_prefix'].md5($content);
            if($needsize) {
                $imgInfo = getimagesize('data://image/jpeg;base64,'.base64_encode($content));
                $fileName = $fileName."_".$imgInfo[0]."_".$imgInfo[1];
            }
        }

        if(empty($config['directory'])) {
            $dstFilePath = "/$fileName.$fileType";
        } else {
            $dstFilePath = "/".$config['directory']."/$fileName.$fileType";
        }

        //上传
        try {
            $cosClient = new Client(array(
                'region'      => $config['ap_region'],
                //'schema' => 'https', //协议头部，默认为http
                'credentials' => array(
                    'appId'     => $config['app_id'],
                    'secretId'  => $config['secret_id'],
                    'secretKey' => $config['secret_key']
                ),
                'timeout'     => $config['timeout'] ? intval($config['timeout']) : self::DEFAULT_TIME_OUT,
            ));

            //普通上块，支持最大5G
            //上传文件流
            if($fileSize < MultipartUpload::DEFAULT_PART_SIZE) {
                $ret = $cosClient->putObject(array(
                    'Bucket' => $config['bucket'],
                    'Key'    => $dstFilePath,
                    'Body'   => $content,
                ));
            } else {
                //分块上传，最大支持50T
                $ret = $cosClient->Upload($config['bucket'], $dstFilePath, $content);
            }
        } catch(Exception $e) {
            $ralArg["errmsg"] = "tcos upload file content exception: ".$e->getMessage();
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }


        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;
        $ralArg["cost"] = intval($cost);


        $result = $ret->toArray();

        //记录上传时的详情信息
        $ralArg['cos_info'] = json_encode($result);

        if(is_array($result) && isset($result['ETag'])) {
            $ralArg["err_no"] = 0;
            ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);
        } else {
            $ralArg["errmsg"] = "upload file content to $dstFilePath failed";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        //成功返回url
        return "https://".$result['Location'];
    }


    public static function getUrlByFileName($cosName, $fileName, $fileType, $expire = 1800) {
        $file = "$fileName.$fileType";
        return self::getObjectUrl($cosName, $file, $expire);
    }


    /**
     * @brief 根据文件名和文件类型获取cos url地址
     * @param string $cosName
     * @param string $file
     * @param int    $expire
     * @return miexed:string|boolean
     */
    public static function getObjectUrl($cosName, $file, $expire = 1800) {
        if(empty($file)) {
            return '';
        }
        $cfg = self::getCosConfig($cosName);
        if(false === $cfg) {
            return false;
        }
        $directory = strval($cfg['directory']);
        $appId = strval($cfg['app_id']);
        $isPublic = 1 === $cfg['is_public'] ? true : false;
        $bucket = $cfg["bucket"];
        $host = $cfg["host"];
        $region = $cfg["ap_region"];
        $signHost = "${bucket}-${appId}.cos.ap-{$region}.myqcloud.com";  # 签名host
        if(empty($host)) {
            $host = $signHost;
        }

        $fpath = empty($directory) ? "/$file" : "/$directory/$file";

        if($isPublic) {            # 公共，可以直接访问
            return "https://$host$fpath";
        }

        ### 使用封装的 getObjectUrl 获取下载签名
        try {
            $cosClient = new Client(array(
                'region'      => $cfg['ap_region'],
                //'schema' => 'https', //协议头部，默认为http
                'credentials' => array(
                    'appId'     => $cfg['app_id'],
                    'secretId'  => $cfg['secret_id'],
                    'secretKey' => $cfg['secret_key']
                )
            ));


            $bucket = $cfg['bucket'].'-'.$cfg['app_id']; //存储桶，格式：BucketName-APPID
            $signedUrl = $cosClient->getObjectUrl($bucket, $fpath, '+'.$expire.' seconds'); //签名的有效时间
            // 请求成功
            return $signedUrl;
        } catch(\Exception $e) {
            // 请求失败
            Bd_Log::warning("tcos getObjectUrl exception: ".$e->getMessage());
            return false;
        }
    }


    /**
     * @brief 删除腾讯云cos上面的文件
     * @param $cosName
     * @param $pid
     * @param $fileType
     * @return
     */
    public static function deleteYunFile($cosName, $pid, $fileType = 'jpg') {
        $reqStartTime = gettimeofday(true);

        $ralArg = [
            "idc"      => Ext_Ral_Ral::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
            "prot"     => self::$CosType,
            "service"  => $cosName,
            "method"   => __METHOD__,
        ];

        if(empty($pid)) {
            return true;
        }
        $config = self::getCosConfig($cosName);
        if(false === $config) {
            return false;
        }

        $bucket = strval($config['bucket']);
        $directory = strval($config['directory']);
        if(!empty($directory)) {
            $dstFile = "/$directory/$pid.$fileType";
        } else {
            $dstFile = "/$pid.$fileType";
        }

        //删除
        try {
            $cosClient = new Client(array(
                'region'      => $config['ap_region'],
                //'schema' => 'https', //协议头部，默认为http
                'credentials' => array(
                    'appId'     => $config['app_id'],
                    'secretId'  => $config['secret_id'],
                    'secretKey' => $config['secret_key']
                )
            ));

            $ret = $cosClient->deleteObject(array(
                'Bucket' => $bucket.'-'.$config['app_id'],
                'Key'    => $dstFile,
            ));
        } catch(Exception $e) {
            $ralArg["errmsg"] = "tcos deleteYunFile content exception: ".$e->getMessage();
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;
        $ralArg["cost"] = intval($cost);

        //删除失败
        if($ret == false) {
            $ralArg["errmsg"] = "deleteYunFile failed";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }


        $result = $ret->toArray();

        //记录上传时的详情信息
        $ralArg['cos_info'] = json_encode($result);

        if(is_array($result) && isset($result['Location'])) {
            $ralArg["err_no"] = 0;
            ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);
        }

        return true;
    }


    /**
     * @param        $srcBucket
     * @param        $dstBucket
     * @param        $srcPid
     * @param        $dstPid
     * @param bool   $overwrite
     * @param string $fileType      文件后缀
     * @param string $metaDirective 设置为 Copy 时，忽略设置的用户元数据信息直接复制，设置为 Replaced 时，按设置的元信息修改元数据，当目标路径和源路径一样时，必须设置为 Replaced
     * @return array|bool|mixed
     *                              只能在一个bucket内部移动, 测试发现无法在两个bucket中移动(V5版本支持跨两个bucket移动)
     */
    public static function copyFile($srcBucket, $dstBucket, $srcPid, $dstPid, $overwrite = true, $fileType = 'jpg', $metaDirective = self::PUT_DIRECTIVE_REPLACED) {
        $reqStartTime = gettimeofday(true);

        $srcConfig = self::getCosConfig($srcBucket);
        if(false === $srcConfig) {
            return false;
        }
        if(empty($srcConfig['directory'])) {
            $srcFilePath = "/$srcPid";
        } else {
            $srcFilePath = "/".$srcConfig['directory']."/$srcPid";
        }
        if($fileType != '') {
            $srcFilePath .= ".".$fileType;
        }

        $dstConfig = self::getCosConfig($dstBucket);
        if(false === $dstConfig) {
            return false;
        }
        if(empty($dstConfig['directory'])) {
            $dstFilePath = "/$dstPid";
        } else {
            $dstFilePath = "/".$dstConfig['directory']."/$dstPid";
        }
        if($fileType != '') {
            $dstFilePath .= ".".$fileType;
        }

        //拷贝
        try {
            $cosClient = new Client(array(
                'region'      => $dstConfig['ap_region'],
                //'schema' => 'https', //协议头部，默认为http
                'credentials' => array(
                    'appId'     => $dstConfig['app_id'],
                    'secretId'  => $dstConfig['secret_id'],
                    'secretKey' => $dstConfig['secret_key']
                ),
                'timeout'     => $dstConfig['timeout'] ? intval($dstConfig['timeout']) : self::DEFAULT_TIME_OUT,
            ));


            $source = $srcConfig['bucket']."-".$srcConfig['app_id'].'.cos.'.$srcConfig['ap_region'].'.myqcloud.com'.$srcFilePath;

            $ret = $cosClient->copyObject(array(
                'Bucket'            => $dstConfig['bucket'].'-'.$dstConfig['app_id'], //格式：BucketName-APPID
                'Key'               => $dstFilePath,
                'CopySource'        => $source,
                'MetadataDirective' => $metaDirective,
            ));
        } catch(Exception $e) {
            $ralArg["errmsg"] = "tcos copyFile exception: ".$e->getMessage();
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;
        $ralArg["cost"] = intval($cost);

        //拷贝失败
        if($ret == false) {
            $ralArg["errmsg"] = "copyFile failed";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $result = $ret->toArray();

        //记录上传时的详情信息
        $ralArg['cos_info'] = json_encode($result);

        //成功
        $ralArg["err_no"] = 0;
        ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);

        if(is_array($result) && isset($result['Location'])) {
            //成功返回url
            return "https://".$result['Location'];
        }

        return true;
    }

    /**
     * @param $srcBucket
     * @param $dstBucket
     * @param $srcPid
     * @param $dstPid
     * @param $fileType string 文件后缀
     * @return bool
     *                  支持不同的bucket之间迁移
     */
    public static function copyBucket2Bucket($srcBucket, $dstBucket, $srcPid, $dstPid, $fileType = "jpg") {
        return self::copyFile($srcBucket, $dstBucket, $srcPid, $dstPid, true, $fileType);
    }


    /**
     * 根据用户指定的文本信息（URL 或文本），生成对应的二维码或条形码（见：https://cloud.tencent.com/document/product/436/54071）
     * @param $strContent 可识别的二维码文本信息, 必传
     * @param $mode       生成的二维码类型，可选值：0或1。0为二维码，1为条形码，默认值为0
     * @param $width      指定生成的二维码或条形码的宽度，高度会进行等比压缩        必传
     * @return bool|string base64编码的图片
     */
    public static function genQrcode($bucketName, $strContent, $width = "200", $mode = 0) {
        $reqStartTime = gettimeofday(true);

        $ralArg = [
            "idc"      => Ext_Ral_Ral::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
            "prot"     => self::$CosType,
            "service"  => $bucketName,
            "method"   => __METHOD__,
        ];

        $config = self::getCosConfig($bucketName);

        if(false === $config) {
            $ralArg["errmsg"] = "get cos config error";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $cosClient = new Qcloud\Cos\Client(array(
            'region'      => $config['ap_region'],
            //已创建桶归属的region可以在控制台查看，https://console.cloud.tencent.com/cos5/bucket
            'schema'      => 'https',
            //协议头部，默认为http
            'credentials' => array(
                'secretId'  => $config['secret_id'],
                'secretKey' => $config['secret_key']
            )
        ));
        try {
            $result = $cosClient->QrcodeGenerate(array(
                'Bucket'        => $config['bucket'].'-'.$config['app_id'], //格式：BucketName-APPID
                'QrcodeContent' => $strContent,
                'QrcodeMode'    => $mode,
                'QrcodeWidth'   => $width,
            ));
        } catch(Exception $e) {
            $ralArg["errmsg"] = "tcos genQrcode exception: ".$e->getMessage();
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }


        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;
        $ralArg["cost"] = intval($cost);


        $result = $result->toArray();
        //记录上传时的详情信息
        $ralArg['cos_info'] = json_encode($result);

        if(is_array($result) && isset($result['ResultImage'])) {
            $ralArg["err_no"] = 0;
            ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);
        } else {
            $ralArg["errmsg"] = "genQrcode failed";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        //成功返回url
        return $result['ResultImage'];
    }


    /**
     * 识别图片中有效二维码的位置及内容，输出图像中二维码包含的文本信息（每个二维码对应的 URL 或文本），并可对识别出的二维码添加马赛克(见：https://cloud.tencent.com/document/product/436/54070)
     * @param     $bucketName
     * @param     $objectName
     * @param int $cover 二维码覆盖功能。可为0或1。0表示不开启二维码覆盖  1表示开启二维码覆盖 功能开启后，将对识别出的二维码覆盖上马赛克，默认值0
     * @return bool|string
     */
    public static function qrcode($bucketName, $objectName, $cover = 0) {
        $reqStartTime = gettimeofday(true);

        $ralArg = [
            "idc"      => Ext_Ral_Ral::ralGetIdc(),
            "local_ip" => Bd_Ip::getLocalIp(),
            "prot"     => self::$CosType,
            "service"  => $bucketName,
            "method"   => __METHOD__,
        ];

        $config = self::getCosConfig($bucketName);

        if(false === $config) {
            $ralArg["errmsg"] = "get cos config error";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $cosClient = new Qcloud\Cos\Client(array(
            'region'      => $config['ap_region'],
            //已创建桶归属的region可以在控制台查看，https://console.cloud.tencent.com/cos5/bucket
            'schema'      => 'https',
            //协议头部，默认为http
            'credentials' => array(
                'secretId'  => $config['secret_id'],
                'secretKey' => $config['secret_key']
            )
        ));
        try {
            $result = $cosClient->Qrcode(array(
                'Bucket' => $config['bucket'].'-'.$config['app_id'], //格式：BucketName-APPID
                'Key'    => $objectName,
                'Cover'  => $cover,
            ));
        } catch(Exception $e) {
            $ralArg["errmsg"] = "tcos qrcode exception: ".$e->getMessage();
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }


        $reqEndTime = gettimeofday(true);
        $cost = $reqEndTime * 1000 - $reqStartTime * 1000;
        $ralArg["cost"] = intval($cost);


        $result = $result->toArray();
        //记录上传时的详情信息
        $ralArg['cos_info'] = json_encode($result);

        if(is_array($result) && isset($result['CodeStatus']) && $result['CodeStatus'] === "0") {
            $ralArg["err_no"] = 0;
            ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);
        } else {
            $ralArg["errmsg"] = "qrcode failed";
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        //成功返回url
        return $result;
    }


    /**
     * 替换为CDN加速域名
     * @param        $cdnDomain
     * @param        $sourceUrl
     * @param string $sourceDomain
     * @return mixed
     */
    public static function replace2CDN($cdnDomain, $sourceUrl, $sourceDomain = 'cos.ap-beijing.myqcloud.com') {
        return str_replace($sourceDomain, $cdnDomain, $sourceUrl);
    }


    /**
     * @brief 读取指定cos服务配置
     * @param $cosName
     * @return
     */
    private static function getCosConfig($cosName) {
        if(self::$CosConfig === null) {
            self::$CosConfig = Bd_Conf::getConf('hk/cos');
        }
        $cosConfig = self::$CosConfig;
        if(!isset($cosConfig[$cosName])) {
            Bd_Log::warning("bucket $cosName is null");
            return false;
        }
        $config = $cosConfig[$cosName];
        if(empty($config['bucket'])) {
            Bd_Log::warning("bucket $cosName need bucket");
            return false;
        }
        if(empty($config['app_id'])) {
            Bd_Log::warning("bucket $cosName need app_id");
            return false;
        }
        if(empty($config['secret_id'])) {
            Bd_Log::warning("bucket $cosName need secret_id");
            return false;
        }
        if(empty($config['secret_key'])) {
            Bd_Log::warning("bucket $cosName need secret_key");
            return false;
        }
        if(empty($config['region'])) {
            Bd_Log::warning("bucket $cosName need region");
            return false;
        }
        if (empty($config['ap_region'])) {
            $config['ap_region'] = static::getApRegionByConf($config);
        }
        if(empty($config['file_prefix'])) {
            Bd_Log::warning("bucket $cosName need file_prefix");
            return false;
        }
        /*
        if (empty($config['directory'])) {
            Bd_Log::warning("CosConfig of $cosName need directory");
            return false;
        }*/
        if(!isset($config['is_public'])) {
            Bd_Log::warning("bucket $cosName need is_public");
            return false;
        }
        if(!isset($config['filesize_limit'])) {
            Bd_Log::warning("bucket $cosName need filesize_limit");
            return false;
        }

        # 对需要idc tag的参数进行特殊处理
        $tag = isset($config["tag"]) && is_array($config["tag"]) ? $config["tag"] : array();
        unset($config["tag"]);

        $config["host"] = strval($tag["host"]);
        $config['is_public'] = intval($config['is_public']);
        $config['filesize_limit'] = intval($config['filesize_limit']);
        return $config;
    }

    /**
     * 获取v5版本的区域配置
     * @author: <EMAIL>
     * @dateTime: 2022/3/10 2:42 PM
     * @param array $tCosConf cos配置
     * @return string
     */
    public static function getApRegionByConf($tCosConf)
    {
        if (!empty($tCosConf['ap_region'])) {
            return $tCosConf['ap_region'];
        }
        $regionMap = [
            'tj'  => 'ap-beijing-1', // 北京一区（华北）
            'bj'  => 'ap-beijing', // 北京
            'sh'  => 'ap-shanghai', // 上海（华东）
            'gz'  => 'ap-guangzhou', // 广州（华南）
            'cd'  => 'ap-chengdu', // 成都（西南）
            'hk'  => 'ap-hongkong', // 中国香港
            'sgp' => 'ap-singapore', // 新加坡
            'ca'  => 'ap-toronto', // 多伦多
            'ger' => 'ap-frankfurt', // 法兰克福
        ];
        if (isset($regionMap[$tCosConf['region']])) {
            return $regionMap[$tCosConf['region']];
        }
        return static::TCOS_DEFAULT_REGION;
    }

    public function putObjectSignUrl($bucket, $objectKey, $expired = 5000, $header= array()) {
        $conf = $this->getCosConfig($bucket);
        $host = self::genTcosHost($conf);
        $header['Content-Type'] = 'application/octet-stream';
        $header['Host'] = $host;

        $sign = self::buildSignature($bucket, "put", $host, "/" . $objectKey, $expired);

        return array(
            "url" => "https://". $host. "/" .$objectKey."?sign=" . $sign,
            "httpheader"     => $header,
        );
    }
}
/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
