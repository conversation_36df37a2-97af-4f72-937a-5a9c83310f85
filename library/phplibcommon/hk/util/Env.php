<?php


class Hk_Util_Env {
    // 运行环境
    const RunEnvTest = "test";
    const RunEnvTips = "tips";
    const RunEnvOnline = "prod";
    const RunEnvUnknown = "unknown";

    private static $_runENv;

    // 使用该方法判断是否是容器化环境，使用前需check服务编排时是否增加了RUN_ENV的环境变量
    public static function isDockerPlatform() {
        $env = self::getRunEnv();
        if($env == self::RunEnvUnknown) {
            return false;
        }
        return true;
    }

    public static function getRunEnv() {
        if(!empty(self::$_runENv)) {
            return self::$_runENv;
        }

        $env = getenv("RUN_ENV");
        switch ($env) {
            case "prod":
                $runEnv = self::RunEnvOnline;
                break;
            case "tips":
                $runEnv = self::RunEnvTips;
                break;
            case "test":
                $runEnv = self::RunEnvTest;
                break;
            default:
                $runEnv = self::RunEnvUnknown;
        }

        self::$_runENv = $runEnv;

        return $runEnv;
    }

    public static function getHost() {
        return $_SERVER['HTTP_X-Forwarded-Host'];
    }
}
