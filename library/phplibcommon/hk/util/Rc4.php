<?php

/* * *************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 * ************************************************************************ */

/**
 * @file Rc4.php
 * <AUTHOR>
 * @date 2015-4-13
 * @brief rc4加密
 * */
class Hk_Util_Rc4 {

    static private $key;

    public static function rc4Encode($data) {
        $key = self::getKey();
        $ret = self::rc4($key, $data);
        return base64_encode($ret);
    }

    public static function rc4Decode($data) {
        $key = self::getKey();
        return self::rc4($key, base64_decode($data));
    }

    public static function rc4ArrayEncode($arr) {
        $ret = array();
        $key = self::getKey();
        if ($arr) {
            foreach ($arr as $v) {
                $ret[] = base64_encode(self::rc4($key, $v));
            }
        }
        return $ret;
    }

    /**
     * 获取加密key，使用反作弊randomkey
     *
     * @since 2018-12-25 rdqa标示能跳过key的获取，使用固定key
     * @since 2018-12-04 迁移获取randomkey逻辑
     * @since 2017-09-25 优化逻辑
     * @return string
     */
    private static function getKey() {
        if (self::$key) {
            return self::$key;
        }
        if (Hk_Util_Tools::isTestRequest()) {
            return "rdqa";
        }

        $appId   = $_REQUEST['appid'];
        $appId   = empty($appId) ? $_REQUEST['appId'] : $appId;
        $cuid    = strval($_REQUEST['cuid']);

        $strKey1 = "@#AIjd83#@6B";
        $strKey2 = $_REQUEST['vc'];
        $spamDs  = Hk_Ds_User_Antispam::getInstance();
        $devInfo = $spamDs->getSignToken($appId, $cuid);
        if (empty($devInfo) || false == $devInfo) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
        }

        $appVersion = Hk_Util_Client::getVersion();
        if ("ios" === $appVersion['type']) {               # 2017-09-25 直接去掉vc判断，并兼容以前的逻辑
            $randomKey = $devInfo['randomKey'];
            $str       = 'K$L@aPb$O^Ic%U*Y`T=f+R~d954e1215aef11a512c1585a0fcd5648ff189f1e?Q0"9{8<7@6#5(4%3&2+1';
            $key       = md5($str. $cuid . $randomKey);
            $randomKey = Hk_Util_Rc4::rc4($key, $randomKey);
            $devInfo   = array (
                'randomKey' => base64_encode($randomKey),
            );
        }

        $strPart1   = md5($strKey1);
        $strPart2   = md5($strKey2);
        $strPart3   = self::swapString(md5('[' . $devInfo["randomKey"] . ']@'), 15);
        $strPart123 = self::swapString(($strPart1 . $strPart2 . $strPart3), 3);
        $strPart4   = md5($strPart123);
        $strRc4Key  = self::swapString(($strPart123 . $strPart4), 60);
        self::$key  = $strRc4Key;
        return $strRc4Key;
    }

    private static function swapString($string, $num) {
        $intLength = strlen($string) - 1;
        for ($i = 0; $i < $num; $i++) {
            $tmp = $string[$i];
            $string[$i] = $string[$intLength - $i];
            $string[$intLength - $i] = $tmp;
        }
        return $string;
    }

    /**
     * rc4
     *
     * @param string $pwd 密钥
     * @param string $data 明文
     * @return string
     */
    public static function rc4($pwd, $data) {
        if (strlen($pwd) < 16) {
            return mcrypt_encrypt(MCRYPT_ARCFOUR, $pwd, $data, MCRYPT_MODE_STREAM, '');
        }
        return openssl_encrypt($data, 'rc4', $pwd, OPENSSL_RAW_DATA);
    }

    /**
     * rc4 解密
     *
     * @param string $pwd 密钥
     * @param string $data 密文
     * @return string
     */
    public static function decode($pwd, $data) {
        if (strlen($pwd) < 16) {
            return mcrypt_decrypt(MCRYPT_ARCFOUR, $pwd, $data, MCRYPT_MODE_STREAM, '');
        }
        return openssl_decrypt($data, 'rc4', $pwd, OPENSSL_RAW_DATA);
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
