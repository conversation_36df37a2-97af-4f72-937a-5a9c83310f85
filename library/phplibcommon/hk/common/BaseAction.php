<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file Base.php
 * <AUTHOR>
 * @date 2013/12/06 13:57:15
 * @brief 抽象基类
 **/
abstract class Hk_Common_BaseAction extends Ap_Action_Abstract {


    const CONFIG_IS_LOGIN = 'is_login';
    const SKIP_VALIDATION = 'skip_validation';
    const CONFIG_INPUT    = 'input';
    const CONFIG_OUTPUT   = 'output';
    const CONFIG_ICONV    = 'iconv';
    const CONFIG_OUT_ICONV     = 'out_iconv';
    const CONFIG_NOTNEED_UINFO = 'na_userinfo';
    const CONFIG_TRANSATION_ENABLE = 'transaction_enable';
    const CONFIG_TRANSATION_DBNAME = 'transaction_dbname';

    protected static $CONFIG_KEYS = array(
        self::CONFIG_IS_LOGIN => '_isLogin',
        self::SKIP_VALIDATION =>'_skipValidation',
        self::CONFIG_INPUT    => '_input',
        self::CONFIG_OUTPUT   => '_output',
        self::CONFIG_ICONV    => '_iconv',
        self::CONFIG_OUT_ICONV     => '_outIconv',
        self::CONFIG_NOTNEED_UINFO => '_needNAUserInfo',
        self::CONFIG_TRANSATION_ENABLE => '_transactionEnable',
        self::CONFIG_TRANSATION_DBNAME => '_transactionDbName',
    );

    // 日志变量
    protected $logVars = array(
        'app_version'  => '',
        'app_terminal' => '',
        'api_version'  => '',
        'api_module'   => '',
        'api_action'   => '',
        'pro_errno'    => '',
        'pro_un'       => '',
        'pro_uid'      => '',
        'pv'           => '',
    );

    // 用户信息
    protected $_userInfo = array(
        'isLogin' => false,
        'uid'     => 0,
        'uname'   => '',
    );

    // 使用Filter过滤过的参数列表
    protected $_requestParam;

    # 公共参数列表
    protected $_publicParam;

    // 配置信息项
    protected $_actionName = null;

    // controller name
    protected $_controllor = 'action';

    // api version
    protected $_apiVersion = 'v1';

    // app version 两位版本后端接口映射版本号
    protected $_appVersion = 0;

    // app versionCode 具体客户端中的版本号
    protected $_appVersionCode = 0;

    // app terminal
    protected $_appTerminal    = '';

    // app type app安装的操作系统类型
    protected $_appType   = "";

    # 业务线标示
    protected $_appId     = '';

    // app source
    protected $_appSource = 0;

    # 是否开启etag校验，默认都开启
    protected $_openEtag  = true;

    // 是否跳过输入输出校验
    protected $_skipValidation = 0;

    // 输出转码
    protected $_outIconv = null;

    // 出入转码
    protected $_iconv  = null;

    // 输入验证配置
    protected $_input  = null;

    // 输出验证配置
    protected $_output = null;

    // 是否需要用户信息
    protected $_needNAUserInfo = 0;

    // request
    protected $_request = null;

    // 事务开关
    protected $_transationEnable = 0;

    // 事务database
    protected $_transationDbName = '';

    // 事务句柄
    protected $_transationDb = null;

    protected $_isPressure  = false;

    // response
    protected $_tplData = array(
        'errNo'  => 0,
        'errstr' => 'success',
        'data'   => array(),
    );

    /*
     * 子类特有逻辑，强制子类必须实现
     */
    abstract protected function invoke();

    protected function afterDisplay() {
    }

    //action配置文件读取,参数初始化
    protected function _init() {
        // 初始化配置文件选项
        $objRequest        = $this->getRequest();
        $this->_controllor = strtolower($objRequest->getControllerName());
        $this->_actionName = strtolower($objRequest->getActionName());
        $this->_apiVersion = strtolower($objRequest->getParam('apiversion', 'v1'));
        Bd_AppEnv::setCurrAction($this->_actionName);
        Bd_AppEnv::setCurrCtl($this->_controllor);

        $configPath = implode("/", array('action', $this->_controllor, $this->_apiVersion, $this->_actionName));
        $config     = Bd_Conf::getAppConf($configPath);
        if (!$config) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ACTION_CONF_ERROR, $configPath);
            return false;
        }

        // 配置参数初始化
        foreach (self::$CONFIG_KEYS as $key => $value) {
            if (isset($config[$key])) {
                $this->$value = $config[$key];
            }
        }

        // 获取请求参数
        $arrRequestParam     = Saf_SmartMain::getCgi();
        $this->_requestParam = !is_null($arrRequestParam['request_param']) ? $arrRequestParam['request_param'] : array();

        /* --- 初始化压测标识 Start--- */
        $isPressure            = Hk_Util_Navigator::isPressure();
        $this->_isPressure     = $isPressure;
        //全链路压测跳过token验证，只认参数uid=
        if ($isPressure) {
            $arrUserInfo['uid'] = $this->_requestParam['uid'];
        } else {
            // 获取用户信息
            $arrUserInfo = Saf_SmartMain::getUserInfo();
        }
        /* --- 初始化压测标识 End--- */

        if (intval($arrUserInfo['uid']) > 0) {
            $this->_userInfo = $arrUserInfo;
            if ($this->_needNAUserInfo) {
                $objDsUcloud = new Hk_Ds_User_Ucloud();
                $arrUcloud   = $objDsUcloud->getUserInfo($this->_userInfo['uid']);
                if ($arrUcloud) {
                    $this->_userInfo['naUcloud'] = $arrUcloud;
                } else {
                    $this->_userInfo['naUcloud'] = array();
                }
            }
        }

        if (empty($this->_requestParam["appId"])) {      # 获取appId，兼容一下
            $this->_appId    = empty($this->_requestParam["appid"]) ? "homework" : $this->_requestParam["appid"];
        } else {
            $this->_appId    = $this->_requestParam["appId"];
        }

        $appVersion            = Hk_Util_Client::getVersion();
        $this->_appType        = $appVersion['type'];
        $this->_appVersion     = $appVersion['version'];
        $this->_appSource      = $appVersion['source'];
        $this->_appVersionCode = $appVersion['versionCode'];

        $appTerminal           = Hk_Util_Client::getTerminal();
        $this->_appTerminal    = $appTerminal['terminal'];

        $this->_publicParam    = $this->initPublicParam();              # 初始化公共参数


        $arrLogData = $this->_requestParam;
        foreach ($arrLogData as $logKey => $logValue) {
            if (is_string($logValue) && strlen($logValue) > 1000) {
                $logValue            = 'LongString';
                $arrLogData[$logKey] = $logValue;
            }
        }
        Hk_Util_Log::setLog('request_param', @json_encode($arrLogData));
    }

    /**
     * 初始化app公共参数，其他类型的基类可以通过重载此函数进行实现<br>
     * 目前公共参数包含（不包含类似sign这种系统参数）：<br>
     * <code>
     * array(
     *     os,
     *     vc,
     *     dayivc,
     *     ykvc,            # 2017-06-16
     *     vcname,
     *     cuid,
     *     appId,
     *     channel,
     *     province,
     *     city,
     *     area,
     *     device,
     * );
     * </code>
     * iOS独有：iOSVersion, bundleID
     * android独有：sdk, pkgName
     *
     * @return array
     **/
    protected function initPublicParam() {
        $os       = strval($this->_requestParam["os"]);
        $province = isset($this->_requestParam["province"]) ? strval($this->_requestParam["province"])
                                                            : strval($this->_requestParam["city"]);      # 省兼容，ios在直辖市下不会上传province
        $pubParam = array(
            "os"       => $os,
            "vc"       => intval($this->_requestParam['vc']),
            "dayivc"   => intval($this->_requestParam['dayivc']),
            "vcname"   => strval($this->_requestParam['vcname']),
            "cuid"     => strval($this->_requestParam['cuid']),
            "appId"    => $this->_appId,
            "channel"  => strval($this->_requestParam["channel"]),
            "province" => $province,
            "city"     => strval($this->_requestParam['city']),
            "area"     => strval($this->_requestParam['area']),
            "device"   => strval($this->_requestParam['device']),
        );

        # 操作系统特有参数
        if ("ios" === $os) {
            $pubParam["iOSVersion"] = strval($this->_requestParam["iOSVersion"]);
            $pubParam["bundleID"]   = strval($this->_requestParam['bundleID']);
        } elseif ("android" === $os) {
            $pubParam["sdk"]        = intval($this->_requestParam["sdk"]);
            $pubParam["pkgName"]    = strval($this->_requestParam["pkgName"]);
        }

        # 不同的产品线有特殊的公共参数
        if ("airclass" === $this->_appId) {
            $pubParam["ykvc"] = intval($this->_requestParam['ykvc']);
        }
        return $pubParam;
    }

    // 参数校验
    protected function _before() {
        if ($this->_isLogin == 1 && $this->_userInfo['uid'] <= 0) {           // 登录校验
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::USER_NOT_LOGIN, '', null, Hk_Util_Exception::TRACE);
        }

        if (!is_null($this->_input)) {      // 输入校验
            try {
                if ($this->_skipValidation == 0) {
                    Hk_Util_Filter::setLogLevel(Hk_Util_Exception::TRACE);
                    Hk_Util_Filter::filter($this->_requestParam, $this->_input, true, false, true);
                }
            } catch (Hk_Util_Exception $e) {
                $this->_tplData['errNo']  = $e->getErrNo();
                $this->_tplData['errstr'] = $e->getErrStr();
                return false;
            }
        }

        if (!is_null($this->_iconv)) {      // 输入转码
            $this->_requestParam = Bd_String::iconv_recursive($this->_requestParam, $this->_iconv['ie'], $this->_iconv['oe']);
        }

        if (intval($this->_transactionEnable) == 1 && strlen($this->_transactionDbName) > 0) {       // 开启事务
            $this->_transactionDb = Hk_Service_Db::getDB($this->_transactionDbName);
            if (empty($this->_transactionDb)) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('dbname' => $this->_transactionDbName));
            }
            $this->_transactionDb->startTransaction();
        }
        return true;
    }

    //输出参数校验
    protected function _after() {
        try {
            if ($this->_skipValidation == 0) {
                Hk_Util_Filter::setLogLevel(Hk_Util_Exception::WARNING);
                Hk_Util_Filter::filter($this->_tplData['data'], $this->_output, false);
            }
        } catch (Hk_Util_Exception $e) {
            $this->_tplData['errNo']  = $e->getErrNo();
            $this->_tplData['errstr'] = $e->getErrStr();
            $this->_tplData['data']   = array();
        }

        $env = getenv("SAFE_WEB_SCAN_ENV");
        if($env=="on") {
            $stop = Hk_Util_WebScan::StopAttack();
            if($stop) {
                $this->_tplData['errNo']  = Hk_Util_ExceptionCodes::PARAM_ERROR;
                $this->_tplData['errstr'] = "输入内容存在危险字符，安全起见，已被本站拦截";
                return false;
            }
        }

        //输出转码
        if (!is_null($this->_outIconv)) {
            $this->_tplData = Bd_String::iconv_recursive($this->_tplData, $this->_outIconv['ie'], $this->_outIconv['oe']);
        }

        //提交事务
        if (!empty($this->_transactionDb)) {
            $this->_transactionDb->commit();
        }
    }

    protected function _display() {
        $json = json_encode((object)$this->_tplData);
        if ($json === false) {
            Bd_Log::warning("json_encode failed");
        }
        header('Content-type:application/json; charset=UTF-8');     // 设置json头

        $etag = '"' . md5($json) . ':' . dechex(strlen($json)) . '"';
        // 检测etag缓存，开启etag检测才会执行
        if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && strlen($_SERVER['HTTP_IF_NONE_MATCH']) > 0 && $this->_openEtag) {
            if ($etag == $_SERVER['HTTP_IF_NONE_MATCH'] || "W/".$etag == $_SERVER['HTTP_IF_NONE_MATCH']) {
                Hk_Util_Log::setLog('http_etag', 1);
                header('ETag: ' . $etag, true, 304);
                return true;
            }
        }
        header('ETag: ' . $etag);

        // upsHeader 设定
        $upsErrNo = Hk_Util_UpsErrorHeader::getUpsErrNo();
        if($upsErrNo != -1) {
            header('X_BD_UPS_ERR_NO: ' . $upsErrNo);
        }
        $upsErrMsg = Hk_Util_UpsErrorHeader::getUpsErrMsg();
        if($upsErrMsg != "") {
            header('X_BD_UPS_ERR_MSG: ' . $upsErrMsg);
        }

        echo $json;
        return true;
    }

    //统计处理
    protected function _processLog() {
        $this->logVars['api_version']  = $this->_apiVersion;
        $this->logVars['api_module']   = $this->_controllor;
        $this->logVars['api_action']   = $this->_actionName;
        $this->logVars['pro_errno']    = $this->_tplData['errNo'];
        $this->logVars['pro_un']       = $this->_userInfo['uname'];
        $this->logVars['pro_uid']      = $this->_userInfo['uid'];
        $this->logVars['app_version']  = $this->_appVersion;
        $this->logVars['app_terminal'] = $this->_appTerminal;
        if (empty($this->logVars['pv'])) {
            $this->logVars['pv']   = sprintf("%s/%s/%s",$this->_controllor, $this->_apiVersion, $this->_actionName);
        }

        // print peak memory usage
        $this->logVars['mem_peak'] = round(memory_get_peak_usage(true)/1024/1024, 3).'MB';

        foreach ($this->logVars as $key => $value) {
            Hk_Util_Log::setLog($key, $value);
        }

        $idc = Bd_Conf::getConf('idc/cur');
        Hk_Util_Log::setLog('idc', $idc);

        Hk_Util_Log::printLog();
    }

    protected function _antispam() {
        $uri      = strtolower($this->getRequest()->getRequestUri());
        $antispam = new Hk_Service_Antispam($this->_appId);
        $antispam->appCheck($uri);
    }

    public function execute() {
        Hk_Util_Log::start('ts_all');
        try{
            Hk_Util_Log::start('ts_init');
            $this->_init();
            Hk_Util_Log::stop('ts_init');

            Hk_Util_Log::start('ts_antispam');
            $this->_antispam();
            Hk_Util_Log::stop('ts_antispam');

            Hk_Util_Log::start('ts_before');
            $ret = $this->_before();
            Hk_Util_Log::stop('ts_before');

            if ($ret === true) {
                Hk_Util_Log::start('ts_invoke');
                $res = $this->invoke();
                $this->_tplData['data'] = is_array($res) ? $res : array();
                Hk_Util_Log::stop('ts_invoke');

                Hk_Util_Log::start('ts_after');
                $ret = $this->_after();
                Hk_Util_Log::stop('ts_after');
            }
        } catch (Hk_Util_Exception $e) {
            $this->_tplData['errNo']  = $e->getErrNo();
            $this->_tplData['errstr'] = $e->getErrStr();

            if (!empty($this->_transactionDb)) {        // 回滚事务
                $this->_transactionDb->rollback();
            }
        } catch (\Exception $e){
            $this->_tplData['errNo']  = $e->getCode();
            $this->_tplData['errstr'] = $e->getMessage();

            if (!empty($this->_transactionDb)) {        // 回滚事务
                $this->_transactionDb->rollback();
            }
        }

        Hk_Util_Log::start('ts_display');
        $this->_display();           // 输出
        Hk_Util_Log::stop('ts_display');

        Hk_Util_Log::stop('ts_all');
        $this->afterDisplay();
        $this->_processLog();       // 打印日志
    }

    /**
     * @brief 检查校验提交用户
     */
    public function checkSubmitUser() {
        if ($this->_userInfo['naUcloud']['baned']) {        // 封禁用户禁止提交
            Bd_Log::warning('user has been banned. userid = '.$this->_userInfo['uid']);
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::USER_BEEN_BANNED);
        }

        // 设置了naUcloud则检查用户是否注册过
        if (isset($this->_userInfo['naUcloud']) && ! $this->_userInfo['naUcloud']) {
            throw new Hk_Util_Exception (Hk_Util_ExceptionCodes::USERINFO_NOT_EXIST);
        }
        return true;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
