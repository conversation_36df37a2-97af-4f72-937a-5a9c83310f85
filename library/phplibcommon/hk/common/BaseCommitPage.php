<?php
/**
 * @category    library
 * @package     common
 * <AUTHOR>
 * @version     2014/12/1 17:11:10
 * @copyright   Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 **/

/**
 * 提交页面的基类
 */
class Hk_Common_BaseCommitPage {
    protected $dbName;
    protected $db;
    protected $autoTrans;
    protected $intCmdNo;
    protected $arrCommand;
    protected $strModName;
    protected $transId;

    public function initCommand($intCmdNo, $arrCommand, $strModName, $dbName, $autoTrans='on') {
        $this->intCmdNo   = $intCmdNo;
        $this->arrCommand = $arrCommand;
        $this->strModName = $strModName;
        $this->dbName     = $dbName;
        $this->autoTrans  = $autoTrans;
        $this->transId    = intval($_REQUEST['transid']);

        return true;
    }

    public function execute() {
        try {
            //命令先进行去重处理
            $objMutex = new Hk_Util_TransIdMutex(array(
                'transId'    => $this->transId,
                'moduleName' => $this->strModName,
            ));
            $flag = $objMutex->flag;

            //新命令进入处理过程
            if ($flag === Hk_Util_TransIdMutex::STATUS_ALL_OK) {
                try {
                    $this->_start();
                    $ret = $this->process();
                    $this->_end();
                    $out['errno'] = 0;
                    if (false === $ret) {           # 处理返回false认为失败
                        $out['errno'] = 9999;
                    }
                } catch (Hk_Util_Exception $e) {
                    //autoTrans默认开启，自动连接DB开启事务
                    if ($this->autoTrans === 'on') {
                        if (!empty($this->db)) {
                            $this->db->rollback();
                        }
                    }

                    $out['errno'] = $e->getErrNo();
                    $objMutex->setError();
                    Bd_Log::warning("Error:[Deal command failed], Abstract:[transid:'{$this->transId}']");
                }
            }

            //命令去重结束，根据命令状态决定结束还是重试
            $objMutex->unlock();
        } catch (Hk_Util_Exception $e) {
            $out['errno'] = $e->getErrNo();
        }
        return $out;
    }

    private function _start() {
        //autoTrans默认开启，自动连接DB开启事务
        if ($this->autoTrans === 'on') {
            $this->db = Hk_Service_Db::getDB($this->dbName);
            if (empty ( $this->db )) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR, '', array('dbname' => $this->dbName));
            }
            $this->db->startTransaction();
        }
    }

    private function _end() {
        //autoTrans默认开启，自动连接DB提交事务
        if ($this->autoTrans === 'on') {
            $this->db->commit();
        }
    }

    protected function process() {
        return true;
    }
}

/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */
