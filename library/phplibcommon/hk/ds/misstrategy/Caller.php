<?php


/**
 * 元策略判断执行函数。<br>
 * 所有的元策略都是在此定义并设置执行函数，最终通过Strategy执行得到是否命中的结果。
 *
 * @since 2.2 2018-12-17 迁移uid|cuid|phone策略到用户流量平台
 * @since 2.1 2018-10-31 增加型号策略
 * @since 2.0 2018 重构
 * @since 1.0 2016-05-13 初始化
 *
 * @filesource hk/ds/misstrategy/Caller.php
 * <AUTHOR>
 * @version 2.2
 * @date    2018-12-17
 */
class Hk_Ds_MisStrategy_Caller {

    const STG_HIT = 1;
    const STG_MIS = 0;

    private $ip;
    private $cuid;
    private $uid;
    private $location;
    private $osType;
    private $appVersion;
    private $telephone;
    private $grade;
    private $gradeCUG;
    private $channel;
    private $model;
    private $osVersion;

    public function __construct($params) {
        $this->ip         = isset($params["ip"])    ? $params["ip"]    : "";            # string
        $this->uid        = isset($params["uid"])   ? $params["uid"]   : 0;             # int
        $this->cuid       = isset($params["cuid"])  ? $params["cuid"]  : "";            # string
        $this->grade      = isset($params["grade"]) ? $params["grade"] : 255;           # int
        $this->gradeCUG   = isset($params["gradeCUG"]) ? $params["gradeCUG"] : 255;     # int
        $this->model      = isset($params["model"]) ? $params["model"] : "";            # string
        $this->osType     = isset($params["os_type"])  ? $params["os_type"]  : "";      # string    android|ios
        $this->channel    = isset($params["channel"])  ? $params["channel"]  : "";      # string
        $this->location   = isset($params["location"]) ? $params["location"] : [];      # array ["province" => "", "city" => ""]
        $this->appVersion = isset($params["version"])  ? $params["version"]  : [];      # array ["vc" => int, "vcname" => ""]
        $this->telephone  = isset($params["telephone"]) ? $params["telephone"] : "";    # string
        $this->osVersion  = isset($params['osVersion']) ? $params['osVersion'] : '';    # string
    }

    /**
     * 判断一个ip是否命中内网策略。<br>
     * 只需判断此ip是否是内网ip即可。
     *
     * @param string       $ip
     * @return int
     */
    public function checkInnerIp() {
        $hit = "" === $this->ip ? false : Hk_Util_Ip::isInnerIp($this->ip);
        return $hit ? self::STG_HIT : self::STG_MIS;
    }

    /**
     * 判断cuid是否命中策略，使用用户流量平台
     *
     * @param string       $stg
     * @return int
     */
    public function checkCuid($stg) {
        if ("" === $this->cuid) {
            return self::STG_MIS;
        }
        if (is_array($stg)) {           # TODO 兼容过渡使用
            return in_array($this->cuid, $stg) ? self::STG_HIT : self::STG_MIS;
        }
        $mark = $stg;
        return $this->checkUserStg($mark, $this->cuid);
    }

    /**
     * 判断uid是否命中策略，使用用户流量平台
     *
     * @param string       $stg
     * @return int
     */
    public function checkUid($stg) {
        if (0 === $this->uid) {
            return self::STG_MIS;
        }
        if (is_array($stg)) {           # TODO 兼容过渡使用
            return in_array($this->uid, $stg) ? self::STG_HIT : self::STG_MIS;
        }
        $mark = $stg;
        return $this->checkUserStg($mark, $this->uid);
    }

    /**
     * 判断用户手机号是否命中，使用用户流量平台
     *
     * @param string       $stg
     * @return int
     */
    public function checkTelephone($stg) {
        if ("" === $this->telephone) {
            return self::STG_MIS;
        }
        if (is_array($stg)) {           # TODO 兼容过渡使用
            return in_array($this->telephone, $stg) ? self::STG_HIT : self::STG_MIS;
        }
        $mark = $stg;
        return $this->checkUserStg($mark, $this->telephone);
    }

    /**
     * 判断一个用户是否命中区域。<br>
     * 用户location格式为：["province" => "", "city" => ""]<br>
     * 策略的location格式为：<br>
     * [
     *     "province" => ["city1", "city2", ...],
     *     "province" => [],       # 空代表省都命中
     * ]
     *
     * 2018-03-09 是否包含条件
     *
     * @param array        $stg
     * @return boolean
     */
    public function checkLocation(array $stg) {
        if (empty($this->location)) {
            return self::STG_MIS;
        }

        $type     = !isset($stg["type"]) ? Hk_Ds_MisStrategy_Const::STG_CONTAIN : intval($stg["type"]);
        $locEmpty = intval($stg["empty"]);     # 如果无定位的处理情况，0：不处理；1：处理
        $stgLoc   = $stg["location"];

        $hit      = false;
        if (empty($this->location)) {             # 无定位信息，根据用户选择进行处理
            $hit  = 1 === $locEmpty ? true : false;
        } else {                                  # 有定位信息，判断城市是否命中
            $prov = $this->location["province"];
            $city = $this->location["city"];
            if (isset($stgLoc[$prov])) {
                if (!empty($stgLoc[$prov])) {     # 策略包含城市，判断城市是否满足
                    $hit = in_array($city, $stgLoc[$prov]) ? true : false;
                } else {                          # 策略不包含城市，只需要省满足条件即可
                    $hit = true;
                }
            }
        }

        # 根据是否包含城市列表进行取反操作
        if (Hk_Ds_MisStrategy_Const::STG_CONTAIN === $type) {
            return $hit ? self::STG_HIT : self::STG_MIS;
        } elseif (Hk_Ds_MisStrategy_Const::STG_NOTCONTAIN === $type) {
            return !$hit ? self::STG_HIT : self::STG_MIS;
        } else {
            return self::STG_MIS;
        }
    }

    /**
     * 判断Cuid客户端的比例命中
     *
     * @param int          $stg
     */
    public function checkCuidRatio($stg) {
        if ("" === $this->cuid) {
            return self::STG_MIS;
        }

        $ratio   = $stg[0];
        $randNum = isset($stg[1]) ? $stg[1] : 1;      # 随机数，随机比例
        if ($ratio === 0) {         # 比例为0，直接返回未命中
            return self::STG_MIS;
        }

        $chk     = crc32($this->cuid) * $randNum;
        $hit     = ($chk % 100 < $ratio) ? true : false;
        return $hit ? self::STG_HIT : self::STG_MIS;
    }

    /**
     * 判断uid的比例命中
     *
     * @param int          $stg
     * @return int
     */
    public function checkUidRatio($stg) {
        if (0 === $this->uid) {
            return self::STG_MIS;
        }

        $ratio   = $stg[0];
        $randNum = isset($stg[1]) ? $stg[1] : 1;      # 随机数，随机比例
        if ($ratio === 0) {         # 比例为0，直接返回未命中
            return self::STG_MIS;
        }

        $chk     = crc32($this->uid) * $randNum;
        $hit     = ($chk % 100 < $ratio) ? true : false;
        return $hit ? self::STG_HIT : self::STG_MIS;
    }

    /**
     * 用户vcname版本判断，策略格式：<br>
     * <code>
     * {
     *     "compare" => ">",
     *     "vc"      => 251,
     *     "type"    => 0,
     *     "vcnames" => ["3.1.1", ".3.1.2", ...],
     * }
     * </code>
     *
     * @param array        $stg
     * @return int
     */
    public function checkAppVersion(array $stg) {
        if (empty($this->appVersion)) {
            return self::STG_MIS;
        }

        $vc       = $this->appVersion["vc"];
        $vcname   = $this->appVersion["vcname"];

        $compChar = isset($stg["compare"]) ? $stg["compare"] : ">=";        # vc大小比较，默认比较>=
        $compVc   = isset($stg["vc"])      ? $stg["vc"]      : 0;

        # 比较vc是否符合条件，如果vcname不需要判断，直接返回命中
        if (!empty($compChar)) {
            if (false === $this->compare($compChar, $vc, $compVc)) {
                return self::STG_MIS;
            }
            if (!isset($stg["type"])) {  # 不需要继续判断vcname是否符合
                return self::STG_HIT;
            }
        }

        # 比较vcname是否符合条件
        $type     = isset($stg["type"]) ? $stg["type"] : Hk_Ds_MisStrategy_Const::STG_CONTAIN;
        $vcnames  = $stg["vcnames"];
        if (empty($vcname) || empty($vcnames)) {
            return self::STG_MIS;
        }
        if (Hk_Ds_MisStrategy_Const::STG_CONTAIN === $type) {             # 包含
            return in_array($vcname, $vcnames)  ? self::STG_HIT : self::STG_MIS;
        } elseif (Hk_Ds_MisStrategy_Const::STG_NOTCONTAIN === $type) {    # 不包含
            return !in_array($vcname, $vcnames) ? self::STG_HIT : self::STG_MIS;
        } else {
            return self::STG_MIS;
        }
    }

    /**
     * 判断手机系统是否命中
     * osType: android/ios
     *
     * @param array        $stg
     */
    public function checkOsType(array $stg) {
        if (empty($this->osType)) {
            return self::STG_MIS;
        }
        return in_array($this->osType, $stg) ? self::STG_HIT : self::STG_MIS;
    }

    /**
     * 年级是否命中
     *
     * @param array        $stg
     * @return int
     */
    public function checkGrade(array $stg) {
        if (0 === $this->grade || 255 === $this->grade) {
            return self::STG_MIS;
        }
        return in_array($this->grade, $stg) ? self::STG_HIT : self::STG_MIS;
    }
    public function checkGradeWithCUG(array $stg) {
        if (0 !== $this->grade && 255 !== $this->grade) {
            return in_array($this->grade, $stg) ? self::STG_HIT : self::STG_MIS;
        }
        if (0 === $this->gradeCUG || 255 === $this->gradeCUG) {
            return self::STG_MIS;
        }
        return in_array($this->gradeCUG, $stg) ? self::STG_HIT : self::STG_MIS;
    }
    /**
     * 判断用户渠道是否命中
     *
     * @param array        $stg
     * @return int
     */
    public function checkChannel(array $stg) {
        if ("" === $this->channel) {
            return self::STG_MIS;
        }

        $type     = intval($stg["type"]);
        $channels = $stg["channels"];
        if (Hk_Ds_MisStrategy_Const::STG_CONTAIN === $type) {             # 包含
            return in_array($this->channel, $channels)  ? self::STG_HIT : self::STG_MIS;
        } elseif (Hk_Ds_MisStrategy_Const::STG_NOTCONTAIN === $type) {    # 不包含
            return !in_array($this->channel, $channels) ? self::STG_HIT : self::STG_MIS;
        } else {
            return self::STG_MIS;
        }
    }

    /**
     * 判断用户手机型号是否命中
     *
     * @param array        $stg
     * @return int
     */
    public function checkModel(array $stg) {
        if ("" === $this->model) {
            return self::STG_MIS;
        }

        $type   = intval($stg["type"]);
        $models = $stg["models"];
        if (Hk_Ds_MisStrategy_Const::STG_CONTAIN === $type) {             # 包含
            return in_array($this->model, $models)  ? self::STG_HIT : self::STG_MIS;
        } elseif (Hk_Ds_MisStrategy_Const::STG_NOTCONTAIN === $type) {    # 不包含
            return !in_array($this->model, $models) ? self::STG_HIT : self::STG_MIS;
        } else {
            return self::STG_MIS;
        }
    }

    /**
     * 数字符号大小比较
     *
     * @param string       $compareChar
     * @param int          $val
     * @param int          $compareVal
     * @return boolean
     */
    private function compare($compareChar, $val, $compareVal) {
        if (empty($compareChar)) {
            return false;
        }
        $compareChar = $compareChar == "=" ? "==" : $compareChar;
        $code        = "return {$val} {$compareChar} {$compareVal};";
        return eval($code) ? true : false;
    }

    /**
     * 判断用户是否命中指定策略
     *
     * @param string      $mark
     * @param mixed       $key
     * @return int
     */
    private function checkUserStg($mark, $key) {
        if (Hk_Ds_MisStrategy_UserStrategy::check($mark, $key)) {
            return self::STG_HIT;
        }
        return self::STG_MIS;
    }

    /**
     * 判断系统版本是否命中指定策略
     *
     * @param array        $stg
     * @return int
     */
    public function checkOsVersion(array $stg){

        if(empty($this->osVersion)){
            return self::STG_MIS;
        }

        # 比较os version是否符合条件
        $type     = isset($stg["type"]) ? $stg["type"] : Hk_Ds_MisStrategy_Const::STG_CONTAIN;
        $verNames  = $stg["vernames"];
        if (empty($verNames) || empty($verNames)) {
            return self::STG_MIS;
        }
        if (Hk_Ds_MisStrategy_Const::STG_CONTAIN === $type) {             # 包含
            return in_array($this->osVersion, $verNames)  ? self::STG_HIT : self::STG_MIS;
        } elseif (Hk_Ds_MisStrategy_Const::STG_NOTCONTAIN === $type) {    # 不包含
            return !in_array($this->osVersion, $verNames) ? self::STG_HIT : self::STG_MIS;
        } else {
            return self::STG_MIS;
        }
    }
}

/* vim: set ft=php expandtab ts=4 sw=4 sts=4 tw=0: */
