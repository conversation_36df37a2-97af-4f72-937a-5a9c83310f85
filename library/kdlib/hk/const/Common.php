<?php
/**
 * 一些常用的常量定义
 *
 * @file   Book.php
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/16
 * @package /hk/const/
 */

class Hk_Const_Common
{
    // appid
    const APP_ID = 'scancode'; // 快对appid
    const ZYB_APP_ID = 'homework'; // 作业帮appid
    const KS_APP_ID = 'parent'; // 作业帮口算appid
    const ZBK_APP_ID = 'airclass'; // 作业帮口算appid
    const JZB_APP_ID = 'knowledge'; // 作业帮家长版

    const KW_APP_ID = 'chatatp'; //快问

    const IDENTITY_IDFA = 'idfa';
    const IDENTITY_IMEI = 'imei';
    const IDENTITY_OAID = 'oaid';
    const IDENTITY_CUID = 'cuid';
    const IDENTITY_DID = 'did';

    // rc4加密固定key，用于和客户端约定加密的某些参数如imei、idfa、oaid等
    const ENCODE_FIXED_KEY = 'vVkiD!@9vaXB0INQ';

    public static $APP_ID_LIST = [
        self::APP_ID,
        self::ZYB_APP_ID,
        self::KS_APP_ID,
        self::ZBK_APP_ID,
    ];
    // 通用封面
    const DEFAULT_COVER = 'scan_62c52a981b3a973408162e098b2be572';

    //旧版本使用过的默认书籍封面
    public static $OLD_DEFAULT_BOOK_COVERS = [
        'scan_ae00eb47c6dd5b2d95428af0933bd64d',
        'scan_e01ba4f29ecc1589955d9a85c19ce18b'
    ];

    // 马甲包的appid
    const VEST_APP_ID = 'kuaiduiwendang';

    // 马甲包的封面
    const VEST_DEFAULT_COVER = 'scan_b8c42c0e1de0a4a59f6ba6f08c319819';

    // 升级app搜索应用商店引导封面
    const UPGRADE_GUIDE_IMAGE = 'scan_7dae46e0e98648f5c21f098b8747acb1';

    //开发人员调试
    const REQUEST_SKIP_RD = 'rdqa';

    // 正式online环境分享域名
    const SHARE_DOMAIN_PROD = 'https://share.kuaiduizuoye.com';

    // tips环境分享域名
    const SHARE_DOMAIN_TIPS = 'https://www.kuaiduizuoye.com';

    // 正式online环境活动域名
    const ACTIVITY_DOMAIN_PROD = 'https://kdactivity.kuaiduizuoye.com';

    // tips环境分享域名
    const ACTIVITY_DOMAIN_TIPS = 'https://www.kuaiduizuoye.com';

    // 教材图片bucket(kd02)对应cdn域名
    const BOOK_CDN_DOMAIN = 'https://kd-book.cdnjtzy.com';

    // 用户上传图片bucket(kw1)对应cdn域名
    const UPLOAD_CDN_DOMAIN = 'https://kd-upload.cdnjtzy.com';

    // 旧的bucket(kuaidui)对应cdn域名
    const OLD_CDN_DOMAIN = 'https://kd-old.cdnjtzy.com';

    // apk bucket对应cdn域名
    const APK_CDN_DOMAIN = 'https://static-apk.kuaiduizuoye.com';

    // NCM 屏蔽名单中全国
    const BLOCK_KEY_NATIONWIDE = '全国';

    // 相机相关功能常量定义
    const TOOL_TEXT_SEARCH = 'textSearch';
    const TOOL_CODE_SEARCH = 'codeSearch';
    const TOOL_PIC_SEARCH = 'picSearch';
    const TOOL_PIGAI = 'pigai';
    const TOOL_PAGE_SEARCH = 'pageSearch';
    const TOOL_SINGLE_SEARCH = 'singleSearch';
    const TOOL_MISTAKE = 'mistake';
    const TOOL_PIC_TRANSLATE = 'picTranslate';
    const TOOL_PIGAI_WHOLE_PAGE = 'pigaiWholePage';
    const TOOL_PIGAI_MULTI_PAGE = 'pigaiMultiPage';
    const TOOL_PIC_TRANS_PICTURE = 'picTransSubPicture';
    const TOOL_PIC_TRANS_WORD = 'picTransSubWord';
    const TOOL_PIC_TRANS_SPEECH = 'picTransSubSpeech';
    const TOOL_MISTAKE_NORMAL = 'mistakeSubNormal';
    const TOOL_MISTAKE_EXAM = 'mistakeSubExam';
    const TOOL_DOC_SCANNER = 'docScanner';
    const TOOL_DOC_SCANNER_SINGLE = 'docScannerSingle';
    const TOOL_DOC_SCANNER_MULTI = 'docScannerMulti';

    // 工具的默认值
    const TOOL_DEFAULT_TITLE = [
        Hk_Const_Common::TOOL_CODE_SEARCH => '扫码搜书',
        Hk_Const_Common::TOOL_PAGE_SEARCH => '拍整页',
        Hk_Const_Common::TOOL_SINGLE_SEARCH => '拍单题',
        Hk_Const_Common::TOOL_PIGAI => '作业批改',
        Hk_Const_Common::TOOL_PIGAI_WHOLE_PAGE => '单页',
        Hk_Const_Common::TOOL_PIGAI_MULTI_PAGE => '多页',
    ];

    // 功能依赖
    const TOOL_DEPENDS = [
        self::TOOL_PIGAI_MULTI_PAGE => self::TOOL_PIGAI,
        self::TOOL_PIGAI_WHOLE_PAGE => self::TOOL_PIGAI,
        self::TOOL_PIC_TRANS_PICTURE => self::TOOL_PIC_TRANSLATE,
        self::TOOL_PIC_TRANS_WORD => self::TOOL_PIC_TRANSLATE,
        self::TOOL_MISTAKE_NORMAL => self::TOOL_MISTAKE,
        self::TOOL_MISTAKE_EXAM => self::TOOL_MISTAKE,
        self::TOOL_DOC_SCANNER_SINGLE => self::TOOL_DOC_SCANNER,
        self::TOOL_DOC_SCANNER_MULTI => self::TOOL_DOC_SCANNER,
    ];

    // 默认封面列表
    const DEFAULT_COVER_LIST = [
        'scan_62c52a981b3a973408162e098b2be572' => true,
        'scan_81cd4b553f31d2d7c840c9c27c57b786' => true,
        'scan_0200368a1ded6946b524a84fc3729348' => true,
        'scan_ba9768ceb2a83d39e587c70a840bf2c4' => true,
        'scan_de33a432f66f738edfd8d0777abcd4f3' => true,
        'scan_c0c713090c6beac0be613a7747536a3a' => true,
        'scan_501134a837a0cc4288326bdb9d5ca559' => true,
        'scan_252f6b7948b4aee4f12b669bcbd8874b' => true,
        'scan_654d58d8d884187a68c847050a174ca3' => true,
        'scan_d66e51adee4da0803802c789bc2c5009' => true,
        'scan_2a4ab3c6da3fa4f1df27e291a0c90284' => true,
        'scan_9d910ff94d44cf79833486d7a448c29b' => true,
        'scan_3dd69ffdb914d103b37af84d286b7f38' => true,
        'scan_ae00eb47c6dd5b2d95428af0933bd64d' => true,
        'scan_85564f3a81c03aafe2d63e4b51ffa5ea' => true,
        'scan_486ce41bdcaffe9185db673a4b6701e4' => true,
        'scan_460e43b34d8c5b29ffd3cebfa06c0630' => true,
        'scan_f3e28426ce2b5c8c9ec4bcf599f0772c' => true,
        'scan_a81c250ff2568e91fe3a6a36a7d6f8af' => true,
        'scan_1a602b97141f86f0db39514c3e30be8a' => true,
        'scan_dab6a44f9147fe3d0bc83e55ff84fe4e' => true,
        'scan_e01ba4f29ecc1589955d9a85c19ce18b' => true,
        'scan_276d2fb225e35e499b2eb670f7ae1ccf' => true,
        'scan_948f09a771021f4451cc87dda428d6f4' => true,
        'scan_bf33c39e35746b406c8b426e92171600' => true,
        'scan_0aa2436d7a2178222b9a887324e926c6' => true,
        'scan_4b28ec48e4da8a5d2ce1063e0e2a7090' => true,
        'scan_7269a5afc98ceeede67805694d04a933' => true,
    ];

    // 列表页新封面列表
    const BOOK_NEW_COVER_FOR_LIST = [
        'scan_fda2531dfc33867cbbcadf87e0269c2d',
        'scan_1456f41dcd626c1877a67ce64ca3b669',
        'scan_b591ef5802a2fdeb96459776bfa2f8c6',
        'scan_8cb45dbaf2f09b3e88aae09d4382c5a2',
        'scan_46c31fec2e16a28b630d7fea26e2368d',
    ];
}
